-- Out of Basket codes for <PERSON>NG<PERSON>

INSERT INTO `cssStyles` (`id`, `name`, `style`, `status`) VALUES (3, 'Out of Basket', 'color:#CC3300;', 'A');

UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='C989A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='E079A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='E409A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='E410A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='E411A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='G002A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='G004A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='G005A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='G010A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='G014A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='G310A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='G319A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='G365A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='G440A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='G480A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='G481A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='G482A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='G489A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='G700A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='K018A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='K021A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='K031A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='K035A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='K036A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='K038A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='K050A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='K051A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='K052A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='K053A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='K054A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='K055A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='K061A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='K070A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='K071A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='K072A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='K101A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='K102A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='K111A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='K112A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='K623A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='K624A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='K629A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='P006A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='P009A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='P011A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='P018A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='P020A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='P030A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='P038A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='P041A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='Q003A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='Q012A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='Q013A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='Q023A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='Q040A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='Q042A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='Q043A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='Q050A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='Q053A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='Q054A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='Q055A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='Q150A';
UPDATE `billingservice` SET `displaystyle`=3 WHERE `service_code`='Z555A';
