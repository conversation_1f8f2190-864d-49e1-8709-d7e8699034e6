
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	
	<parent>
		<artifactId>hapi</artifactId>
		<groupId>ca.uhn.hapi</groupId>
		<version>1.0.1</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	
	<modelVersion>4.0.0</modelVersion>
	
	<groupId>ca.uhn.hapi</groupId>
	<artifactId>hapi-structures-v26</artifactId>
	<name>HAPI - 04 - Generated Structures v2.6</name>
	
	<packaging>jar</packaging>

	<dependencies>
		<dependency>
			<groupId>ca.uhn.hapi</groupId>
			<artifactId>hapi-base</artifactId>
			<version>${hapi.version}</version>
		</dependency>
	</dependencies>

	<properties>
		<gen.skip>false</gen.skip>
		<gen.version>2.6</gen.version>
		<gen.version.short>v26</gen.version.short>
		<sourcegen.jdbcUrl>${sourcegen.jdbcUrl.new}</sourcegen.jdbcUrl>
	</properties>

	<build>
		<plugins>
			<plugin>
				<groupId>ca.uhn.hapi</groupId>
				<artifactId>hapi-sourcegen</artifactId>
				<executions>
					<execution>
						<id>${gen.version}</id>
						<phase>generate-sources</phase>
						<goals>
							<goal>sourcegen</goal>
						</goals>
						<inherited>true</inherited>
						<configuration>
							<version>2.6</version>
							<jdbcUrl>${sourcegen.jdbcUrl}</jdbcUrl>
							<jdbcUser>${sourcegen.jdbcUser.old}</jdbcUser>
							<jdbcPassword>${sourcegen.jdbcPassword.old}</jdbcPassword>
							<targetDirectory>${basedir}/target/generated-sources/sourcegen</targetDirectory>
							<targetResourceDirectory>${basedir}/target/generated-sources/resourcegen</targetResourceDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<phase>site</phase>
						<inherited>true</inherited>
						<configuration>
							<tasks>
								<echo message="Copying resources"/>
								<copy todir="../target/site/${gen.version.short}/apidocs">
									<fileset dir="target/site/apidocs">
										<include name="**/*"/>
									</fileset>
								</copy>
							</tasks>
						</configuration>
						<goals>
							<goal>run</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<artifactId>maven-assembly-plugin</artifactId>
				<executions>
					<execution>
						<id>make-assembly</id> <!-- this is used for inheritance merges -->
						<phase>package</phase> <!-- append to the packaging phase. -->
						<goals>
							<goal>single</goal> <!-- goals == mojos -->
						</goals>
						<inherited>true</inherited>
						<configuration>
							<descriptors>
								<descriptor>../src/assembly/structures-source.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>retrotranslator-maven-plugin</artifactId>
				<version>1.0-alpha-4</version>
				<executions>
					<execution>
						<goals>
							<goal>translate-project</goal>
						</goals>
						<configuration>
							<attach>true</attach>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
		<resources>
            <resource>
                <directory>${basedir}/target/generated-sources/resourcegen</directory>
                <filtering>false</filtering>
            </resource>
		</resources>
	</build>

	<reporting>
		<plugins>
			<plugin>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>2.5</version>
				<inherited>true</inherited>
				<configuration>
					<minmemory>128m</minmemory>
					<maxmemory>1g</maxmemory>
					<linksource>true</linksource>
				</configuration>
			</plugin>
		</plugins>
	</reporting>
	
</project>
