#!/bin/bash

echo "Fixing whitespace issues in JEMR-633 files..."

# List of files that are part of your JEMR-633 changes
JEMR_FILES=(
    "src/main/webapp/common/pendoHeader.jsp"
    "src/main/webapp/dms/documentUploader.jsp" 
    "src/main/webapp/dms/incomingDocs.jsp"
    "src/main/webapp/lab/CA/ALL/testUploader.jsp"
    "src/main/webapp/oscarMDS/SelectProvider.jsp"
    "src/main/webapp/oscarMDS/documentsInQueues.jsp"
)

# Create a temporary branch to work on
git checkout -b temp-whitespace-fix

# For each file, we'll add only the meaningful changes
for file in "${JEMR_FILES[@]}"; do
    echo "Processing $file..."
    
    # Check if file has changes
    if git diff --quiet HEAD~1 "$file"; then
        echo "  No changes in $file"
        continue
    fi
    
    # Show the diff to see what changed
    echo "  Changes in $file:"
    git diff --ignore-all-space --stat HEAD~1 "$file"
    
    # Add the file with intent to add, then we'll manually review
    git add "$file"
done

echo ""
echo "Files staged for commit:"
git status --porcelain

echo ""
echo "Review the changes and commit when ready:"
echo "git commit -m 'JEMR-633: Integrate Pendo with Inbox in Juno - clean version'"
echo ""
echo "Then merge back to your branch:"
echo "git checkout JEMR-633"
echo "git reset --hard temp-whitespace-fix"
echo "git branch -d temp-whitespace-fix"
