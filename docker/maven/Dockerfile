# syntax=docker/dockerfile:1
FROM eclipse-temurin:8-jdk-focal

# Install Apache Maven 3.6.3 manually
ARG MAVEN_VERSION=3.6.3
ARG MAVEN_BASE_URL=https://archive.apache.org/dist/maven/maven-3/${MAVEN_VERSION}/binaries
ENV MAVEN_HOME=/opt/apache-maven-${MAVEN_VERSION}
ENV PATH="${MAVEN_HOME}/bin:${PATH}"

RUN set -eux; \
    apt-get update; \
    apt-get install -y --no-install-recommends \
        # apt-utils for dependency resolution, wget/tar for download, git for scm
        apt-utils ca-certificates wget tar git \
        # Original runtime dependencies
        mariadb-client \
        libjpeg-turbo8 libx11-6 libxcb1 libxext6 libxrender1 \
        xfonts-75dpi xfonts-base \
        # Install wkhtmltopdf from official Ubuntu.
        wkhtmltopdf; \
    \
    # --- Install Maven --- \
    mkdir -p /opt; \
    wget -q "${MAVEN_BASE_URL}/apache-maven-${MAVEN_VERSION}-bin.tar.gz" -O /tmp/maven.tgz; \
    wget -q "${MAVEN_BASE_URL}/apache-maven-${MAVEN_VERSION}-bin.tar.gz.sha512" -O /tmp/maven.tgz.sha512; \
    echo "$(cat /tmp/maven.tgz.sha512)  /tmp/maven.tgz" | sha512sum -c -; \
    tar -xzf /tmp/maven.tgz -C /opt; \
    ln -s "${MAVEN_HOME}/bin/mvn" /usr/bin/mvn; \
    \
    # --- Clean up --- \
    rm -rf /tmp/maven.tgz /tmp/maven.tgz.sha512; \
    # Purge build-time dependencies.
    apt-get purge -y --auto-remove wget; \
    rm -rf /var/lib/apt/lists/*