#!/bin/bash

mkdir -p ~/.npm.docker
mkdir -p ~/.m2

# Specify "-b" on the command line to rebuild the container
if [ "$1" = "-b" ]
then
	echo "Building Container..."
	DOCKER_USER="$(id -u):$(id -g)" USER_HOME="$HOME" sudo -E docker-compose -f ${BASH_SOURCE%/*}/docker-compose.yml up --build --exit-code-from juno-maven
else
	DOCKER_USER="$(id -u):$(id -g)" USER_HOME="$HOME" sudo -E docker-compose -f ${BASH_SOURCE%/*}/docker-compose.yml up --exit-code-from juno-maven
fi
