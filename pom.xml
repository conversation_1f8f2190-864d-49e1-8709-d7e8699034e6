<project
		xmlns="http://maven.apache.org/POM/4.0.0"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
        <version>2.5.2</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>org.oscarehr</groupId>
	<artifactId>oscar</artifactId>
	<packaging>war</packaging>
	<version>14.0.0-SNAPSHOT</version>
	<name>oscar</name>
	<description>OSCAR McMaster is a web-based electronic medical record (EMR) system initially developed for academic primary care clinics. It has grown into a comprehensive EMR
		and billing system used by many doctor's offices and private medical clinics in Canada and other parts of the world. The name is derived from where it was created and an
		acronym; OSCAR stands for Open Source Clinical Application and Resource and McMaster refers to McMaster University, where it was developed. It enables the delivery of
		evidence resources at the point of care.
	</description>
	<issueManagement>
		<system>SourceForge Tracker</system>
		<url>https://sourceforge.net/tracker/?group_id=66701</url>
	</issueManagement>
	<ciManagement>
		<system>Jenkins</system>
		<url>https://demo.oscarmcmaster.org:11042/</url>
	</ciManagement>
	<inceptionYear>2001</inceptionYear>
	<mailingLists>
		<mailingList>
			<name>oscarmcmaster-devel</name>
			<subscribe>https://lists.sourceforge.net/lists/listinfo/oscarmcmaster-devel</subscribe>
			<post><EMAIL></post>
			<archive>https://sourceforge.net/mailarchive/forum.php?forum_name=oscarmcmaster-devel</archive>
		</mailingList>
	</mailingLists>
	<licenses>
		<license>
			<name>GPLv2</name>
			<url>http://www.gnu.org/licenses/gpl-2.0.txt</url>
		</license>
	</licenses>
	<url>http://www.oscarcanada.org</url>
	<scm>
		<url>http://oscarmcmaster.git.sourceforge.net/git/gitweb.cgi?p=oscarmcmaster/oscar</url>
		<connection>scm:git:git://oscarmcmaster.git.sourceforge.net/gitroot/oscarmcmaster/oscar</connection>
		<developerConnection>scm:git:ssh://<EMAIL>:29418/oscar</developerConnection>
		<tag>master</tag>
	</scm>
	<properties>
		<java.version>1.8</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<netbeans.hint.deploy.server>Tomcat60</netbeans.hint.deploy.server>
		<webpack.env>prod</webpack.env>
		<antrun.skip.jspc>false</antrun.skip.jspc>
		<oscar.use.integration.prop>false</oscar.use.integration.prop>
		<oscar.dbinit.skip>false</oscar.dbinit.skip>
		<cxf.version>3.2.12</cxf.version>
		<memcachedSessionManager.version>2.3.2</memcachedSessionManager.version>
		<jaxb.version>2.3.1</jaxb.version>
		<jaxbcore.version>*******</jaxbcore.version>
    <jackson.version>2.12.3</jackson.version>
		<juno.webpack.skip>false</juno.webpack.skip>
	</properties>
	<repositories>
		<repository>
			<id>spring-milestones</id>
			<name>Spring Milestones</name>
			<url>https://repo.spring.io/milestone</url>
		</repository>
    <repository>
      <id>local_repo</id>
      <url>file://${basedir}/local_repo</url>
    </repository>
		<repository>
			<id>jaspersoft-third-party</id>
			<url>https://jaspersoft.jfrog.io/jaspersoft/third-party-ce-artifacts/</url>
		</repository>
		<repository>
			<id>openshift.nexus</id>
			<url>http://nexus.apps.prod.cldmd.net/nexus/content/groups/public/</url>
		</repository>
		<repository>
			<id>well.nexus.public-releases</id>
			<url>https://cobalt.azu.oscar-emr.net:63827/repository/public-releases/</url>
		</repository>
	</repositories>
	<pluginRepositories>
		<pluginRepository>
			<id>spring-milestones</id>
			<name>Spring Milestones</name>
			<url>https://repo.spring.io/milestone</url>
		</pluginRepository>
		<pluginRepository>
			<id>local_repo</id>
			<url>file://${basedir}/local_repo</url>
		</pluginRepository>
	</pluginRepositories>

	<dependencies>
		<!-- Spring Boot -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-api</artifactId>
        </exclusion>
      </exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-tomcat</artifactId>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-mail</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-integration</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>

<!--
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<optional>true</optional>
		</dependency>
-->

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>

		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-registry-prometheus</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.integration</groupId>
			<artifactId>spring-integration-file</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.integration</groupId>
			<artifactId>spring-integration-sftp</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.tomcat.embed</groupId>
			<artifactId>tomcat-embed-jasper</artifactId>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.mariadb.jdbc</groupId>
			<artifactId>mariadb-java-client</artifactId>
			<version>1.5.7</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.junit.vintage</groupId>
			<artifactId>junit-vintage-engine</artifactId>
			<scope>test</scope>
		</dependency>

		<!-- https://mvnrepository.com/artifact/javax.persistence/javax.persistence-api -->
		<dependency>
			<groupId>javax.persistence</groupId>
			<artifactId>javax.persistence-api</artifactId>
			<version>2.2</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/javax.transaction/javax.transaction-api -->
		<dependency>
			<groupId>javax.transaction</groupId>
			<artifactId>javax.transaction-api</artifactId>
			<version>1.3</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/javax.interceptor/javax.interceptor-api -->
		<dependency>
			<groupId>javax.interceptor</groupId>
			<artifactId>javax.interceptor-api</artifactId>
			<version>1.2.2</version>
		</dependency>



		<!-- general libraries -->
		<dependency>
			<groupId>net.bull.javamelody</groupId>
			<artifactId>javamelody-core</artifactId>
			<version>1.53.0</version>
		</dependency>
		<dependency>
			<groupId>commons-logging</groupId>
			<artifactId>commons-logging</artifactId>
			<version>1.1.1</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>log4j-over-slf4j</artifactId>
			<version>1.7.30</version>
		</dependency>
		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
			<version>2.6</version>
		</dependency>
		<dependency>
			<groupId>commons-validator</groupId>
			<artifactId>commons-validator</artifactId>
			<version>1.3.1</version>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.4</version>
		</dependency>
		<dependency>
			<groupId>commons-net</groupId>
			<artifactId>commons-net</artifactId>
			<version>3.0.1</version>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>1.3.3</version>
		</dependency>
		<dependency>
			<groupId>commons-collections</groupId>
			<artifactId>commons-collections</artifactId>
			<version>3.2.2</version>
		</dependency>
		<dependency>
			<groupId>org.javassist</groupId>
			<artifactId>javassist</artifactId>
			<version>3.21.0-GA</version>
		</dependency>
		<dependency>
			<groupId>org.glassfish</groupId>
			<artifactId>javax.el</artifactId>
			<version>3.0.1-b08</version>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4</artifactId>
			<version>2.0.9</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-api-mockito2</artifactId>
			<version>2.0.9</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<version>3.1.0</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>javax.servlet.jsp</groupId>
			<artifactId>jsp-api</artifactId>
			<version>2.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.jcs</groupId>
			<artifactId>jcs</artifactId>
			<version>1.3</version>
		</dependency>
		<dependency>
			<groupId>antlr</groupId>
			<artifactId>antlr</artifactId>
			<version>2.7.7</version>
		</dependency>
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20140107</version>
		</dependency>

		<!-- hapi/HL7 -->
		<dependency>
			<groupId>ca.uhn.hapi</groupId>
			<artifactId>hapi-base</artifactId>
			<version>2.3</version>
		</dependency>
		<dependency>
			<groupId>ca.uhn.hapi</groupId>
			<artifactId>hapi-structures-v26</artifactId>
			<version>2.3</version>
		</dependency>
		<dependency>
			<groupId>ca.uhn.hapi</groupId>
			<artifactId>hapi-structures-v25</artifactId>
			<version>2.3</version>
		</dependency>
		<dependency>
			<groupId>ca.uhn.hapi</groupId>
			<artifactId>hapi-structures-v23</artifactId>
			<version>2.3</version>
		</dependency>
		<dependency>
			<groupId>ca.uhn.hapi</groupId>
			<artifactId>hapi-structures-v22</artifactId>
			<version>2.3</version>
		</dependency>
		<dependency>
			<groupId>ca.uhn.hapi</groupId>
			<artifactId>hapi-structures-v231</artifactId>
			<version>2.3</version>
		</dependency>

		<!-- hapi/FHIR -->
		<!-- https://mvnrepository.com/artifact/ca.uhn.hapi.fhir/hapi-fhir-base -->
		<dependency>
			<groupId>ca.uhn.hapi.fhir</groupId>
			<artifactId>hapi-fhir-base</artifactId>
			<version>5.7.9</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/ca.uhn.hapi.fhir/hapi-fhir-client -->
		<dependency>
			<groupId>ca.uhn.hapi.fhir</groupId>
			<artifactId>hapi-fhir-client</artifactId>
			<version>5.7.9</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/ca.uhn.hapi.fhir/hapi-fhir-structures-r4 -->
		<dependency>
			<groupId>ca.uhn.hapi.fhir</groupId>
			<artifactId>hapi-fhir-structures-r4</artifactId>
			<version>5.7.9</version>
		</dependency>

		<!-- DNS lookup -->
		<dependency>
			<groupId>dnsjava</groupId>
			<artifactId>dnsjava</artifactId>
			<version>2.1.8</version>
		</dependency>
		<!-- hibernate / sql -->
		<dependency>
			<groupId>commons-dbcp</groupId>
			<artifactId>commons-dbcp</artifactId>
			<version>1.4</version>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>5.1.39</version>
		</dependency>
		<dependency>
			<groupId>cglib</groupId>
			<artifactId>cglib-nodep</artifactId>
			<version>2.2</version>
		</dependency>

		<!--  pdfbox -->
		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
			<!-- version 2.0.8 released 2017-10-30 -->
			<version>2.0.8</version>
		</dependency>

		<!-- google collections lib -->
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>24.1.1-jre</version>
		</dependency>
		<!-- struts -->
		<dependency>
			<groupId>struts</groupId>
			<artifactId>struts</artifactId>
			<version>1.2.7</version>
		</dependency>
		<dependency>
			<groupId>org.apache.struts</groupId>
			<artifactId>struts-menu</artifactId>
			<version>UNKNOWN</version>
		</dependency>
		<dependency>
			<groupId>struts</groupId>
			<artifactId>struts-el</artifactId>
			<version>1.2.7</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc-struts</artifactId>
			<version>2.5.6.SEC03</version>
			<exclusions>
				<exclusion>
					<groupId>org.springframework</groupId>
					<artifactId>spring-webmvc</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.springframework</groupId>
					<artifactId>spring-web</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>jstl</artifactId>
<!--
			<scope>provided</scope>
-->
			<version>1.2</version>
		</dependency>
		<dependency>
			<groupId>taglibs</groupId>
			<artifactId>standard</artifactId>
			<version>1.1.2</version>
		</dependency>
		<!-- pdf / itext -->
		<dependency>
			<groupId>com.itextpdf</groupId>
			<artifactId>itextpdf</artifactId>
			<version>5.5.13.2</version>
		</dependency>
		<dependency>
			<groupId>com.itextpdf.tool</groupId>
			<artifactId>xmlworker</artifactId>
			<version>5.5.13.2</version>
		</dependency>
    <!-- https://mvnrepository.com/artifact/com.itextpdf/itext7-core -->
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>itext7-core</artifactId>
      <version>7.1.16</version>
      <type>pom</type>
    </dependency>
    <!-- iText pdfHTML add-on -->
	<!-- https://mvnrepository.com/artifact/com.itextpdf/html2pdf -->
	<dependency>
		<groupId>com.itextpdf</groupId>
		<artifactId>html2pdf</artifactId>
		<version>3.0.5</version>
	</dependency>

		<!-- rtf / lowagie -->
		<dependency>
			<groupId>com.lowagie</groupId>
			<artifactId>itext-rtf</artifactId>
			<version>2.1.7</version>
			<exclusions>
				<exclusion>
					<groupId>bouncycastle</groupId>
					<artifactId>bcprov-jdk14</artifactId>
				</exclusion>
				<exclusion>
					<groupId>bouncycastle</groupId>
					<artifactId>bcmail-jdk14</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bcprov-jdk14</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bctsp-jdk14</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- json -->
		<dependency>
			<groupId>net.sf.json-lib</groupId>
			<artifactId>json-lib</artifactId>
			<version>2.3</version>
			<classifier>jdk15</classifier>
		</dependency>


		<!-- json_simple -->
		<dependency>
			<groupId>com.googlecode.json-simple</groupId>
			<artifactId>json-simple</artifactId>
			<version>1.1</version>
		</dependency>

		<!-- apache xmlrpc -->
		<dependency>
			<groupId>xmlrpc</groupId>
			<artifactId>xmlrpc</artifactId>
			<version>1.2-b1</version>
		</dependency>
		<!-- apache xmlrpc -->
		<dependency>
			<groupId>drools</groupId>
			<artifactId>drools-all</artifactId>
			<version>2.0</version>
		</dependency>
		<!-- jasper reports -->
		<!-- https://mvnrepository.com/artifact/net.sf.jasperreports/jasperreports -->
		<dependency>
			<groupId>net.sf.jasperreports</groupId>
			<artifactId>jasperreports</artifactId>
			<version>6.5.1</version>
			<exclusions>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-databind</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-annotations</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>net.sf.jasperreports</groupId>
			<artifactId>jasperreports-fonts</artifactId>
			<version>6.0.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.xmlgraphics</groupId>
			<artifactId>batik-gvt</artifactId>
			<version>1.9</version>
		</dependency>
		<dependency>
			<groupId>org.apache.xmlgraphics</groupId>
			<artifactId>batik-bridge</artifactId>
			<version>1.9</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/net.sourceforge.barbecue/barbecue -->
		<dependency>
			<groupId>net.sourceforge.barbecue</groupId>
			<artifactId>barbecue</artifactId>
			<version>1.5-beta1</version>
		</dependency>
		<!-- caisi integrator -->
		<dependency>
			<groupId>org.oscarehr.caisi_integrator</groupId>
			<artifactId>caisi_integrator_client_stubs</artifactId>
			<version>0.2</version>
		</dependency>
		<!-- macplus -->
		<dependency>
			<groupId>ca.mcmaster.plus</groupId>
			<artifactId>macplus_client_stubs</artifactId>
			<version>SNAPSHOT</version>
		</dependency>
		<!-- apache xmlgraphics batik -->
		<dependency>
			<groupId>org.apache.xmlgraphics</groupId>
			<artifactId>batik-dom</artifactId>
			<version>1.9</version>
		</dependency>
		<!-- apache commons codec -->
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<version>1.4</version>
		</dependency>
		<!-- indivo -->
		<dependency>
			<groupId>org.indivo</groupId>
			<artifactId>indivo-core</artifactId>
			<version>3.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>org.indivo</groupId>
			<artifactId>indivo-model-core</artifactId>
			<version>3.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>org.indivo</groupId>
			<artifactId>indivo-model-phr-jackson</artifactId>
			<version>3.1-SNAPSHOT</version>
		</dependency>
		<!-- jcharts -->
		<dependency>
			<groupId>net.sf.jcharts</groupId>
			<artifactId>krysalis-jCharts</artifactId>
			<version>0.7.5</version>
		</dependency>
		<dependency>
			<groupId>batik</groupId>
			<artifactId>batik-awt-util</artifactId>
			<version>1.6-1</version>
		</dependency>
		<dependency>
			<groupId>batik</groupId>
			<artifactId>batik-dom</artifactId>
			<version>1.6-1</version>
		</dependency>
		<dependency>
			<groupId>batik</groupId>
			<artifactId>batik-svggen</artifactId>
			<version>1.6-1</version>
		</dependency>
		<dependency>
			<groupId>batik</groupId>
			<artifactId>batik-util</artifactId>
			<version>1.6-1</version>
		</dependency>
		<dependency>
			<groupId>batik</groupId>
			<artifactId>batik-xml</artifactId>
			<version>1.6-1</version>
		</dependency>
		<!-- jsoup - for html parsing -->
		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
			<version>1.11.3</version>
		</dependency>
		<!-- cds -->
		<dependency>
			<groupId>cds</groupId>
			<artifactId>cds</artifactId>
			<version>0.0-SNAPSHOT</version>
		</dependency>
		<!-- cds cihi phc vrs -->
		<dependency>
			<groupId>cds</groupId>
			<artifactId>cds_cihi_phcvrs</artifactId>
			<version>1.0</version>
		</dependency>
		<!-- cds cihi -->
		<dependency>
			<groupId>cds</groupId>
			<artifactId>cds_cihi</artifactId>
			<version>1.0</version>
		</dependency>
		<!-- cds rourke -->
		<dependency>
			<groupId>cds</groupId>
			<artifactId>cds_rourke</artifactId>
			<version>1.0</version>
		</dependency>
		<!-- cds hrm -->
		<dependency>
			<groupId>cds</groupId>
			<artifactId>cds_hrm</artifactId>
			<version>1.0</version>
		</dependency>
		<!-- HRM module -->
		<dependency>
			<groupId>org.oscarehr.hrm</groupId>
			<artifactId>hrm-jaxb</artifactId>
			<version>4.1a</version>
		</dependency>

		<!-- XML parsing -->
		<!-- https://mvnrepository.com/artifact/com.sun.xml.bind/jaxb-impl -->
		<dependency>
			<groupId>com.sun.xml.bind</groupId>
			<artifactId>jaxb-impl</artifactId>
			<version>${jaxb.version}</version>
		</dependency>
		<dependency>
			<groupId>com.sun.xml.bind</groupId>
			<artifactId>jaxb-xjc</artifactId>
			<version>${jaxb.version}</version>
		</dependency>
		<dependency>
			<groupId>com.sun.xml.bind</groupId>
			<artifactId>jaxb-core</artifactId>
			<version>${jaxbcore.version}</version>
		</dependency>
		<dependency>
			<groupId>javax.xml.bind</groupId>
			<artifactId>jaxb-api</artifactId>
			<version>${jaxb.version}</version>
		</dependency>
		<dependency>
			<groupId>org.glassfish.jaxb</groupId>
			<artifactId>jaxb-runtime</artifactId>
			<version>2.3.4</version>
		</dependency>
		<!-- for xsd generation -->
		<dependency>
			<groupId>org.jvnet.jaxb2_commons</groupId>
			<artifactId>jaxb2-basics-runtime</artifactId>
			<version>1.11.1</version>
		</dependency>
		<dependency>
			<groupId>org.jvnet.jaxb2.maven2</groupId>
			<artifactId>maven-jaxb2-plugin</artifactId>
			<version>0.14.0</version>
		</dependency>
		<dependency>
			<groupId>com.sun.xml.messaging.saaj</groupId>
			<artifactId>saaj-impl</artifactId>
			<version>1.5.1</version>
		</dependency>

		<!-- hsfo -->
		<dependency>
			<groupId>hsfo</groupId>
			<artifactId>hsfo</artifactId>
			<version>2007-02-12</version>
		</dependency>
		<dependency>
			<groupId>hsfo2</groupId>
			<artifactId>hsfo2</artifactId>
			<version>2.0</version>
		</dependency>
		<!-- apache xml beans -->
		<dependency>
			<groupId>org.apache.xmlbeans</groupId>
			<artifactId>xmlbeans</artifactId>
			<version>2.4.0</version>
		</dependency>
		<!-- HttpClient -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpmime</artifactId>
			<version>4.2.2</version>
		</dependency>
		<!-- quartz -->
		<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz</artifactId>
			<version>2.3.2</version>
		</dependency>
		<!-- cookie revolver -->
		<dependency>
			<groupId>net.sf.cookierevolver</groupId>
			<artifactId>cookierevolver</artifactId>
			<version>0.2.5</version>
		</dependency>
		<!-- commons-betwixt ... what is this for? -->
		<dependency>
			<groupId>commons-betwixt</groupId>
			<artifactId>commons-betwixt</artifactId>
			<version>0.7</version>
		</dependency>
		<!-- email -->
		<dependency>
			<groupId>javax.mail</groupId>
			<artifactId>mail</artifactId>
			<version>1.4</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-email</artifactId>
			<version>1.1</version>
		</dependency>
		<!-- jfree charts -->
		<dependency>
			<groupId>jfree</groupId>
			<artifactId>jfreechart</artifactId>
			<version>1.0.12</version>
		</dependency>
		<!-- dm.jar , suspect this is generated classes and should be removed as a jar in the future -->
		<dependency>
			<groupId>dm</groupId>
			<artifactId>dm</artifactId>
			<version>0.0-SNAPSHOT</version>
		</dependency>
		<!-- ocan.jar , suspect this is generated classes and should be removed as a jar in the future -->
		<dependency>
			<groupId>ocan</groupId>
			<artifactId>ocan</artifactId>
			<version>0.0-SNAPSHOT</version>
		</dependency>
		<!-- FOR BORN Project - AR2005 in XML format -->
		<dependency>
			<groupId>org.oscarehr.ar2005</groupId>
			<artifactId>ar2005</artifactId>
			<version>1.2</version>
		</dependency>
		<!-- FOR BORN18MWBV Project - Rourke, NDDS, 18M Summary in XML format -->
		<dependency>
			<groupId>ca.bornontario</groupId>
			<artifactId>x18MEWBV</artifactId>
			<version>1.8.2</version>
		</dependency>
		<dependency>
			<groupId>ca.bornontario</groupId>
			<artifactId>BORNWB</artifactId>
			<version>2.1.5</version>
		</dependency>
		<dependency>
			<groupId>ca.bornontario</groupId>
			<artifactId>BORNWBCSD</artifactId>
			<version>2.2.1</version>
		</dependency>
		<!-- FOR ORN Project - CKD config in XML format -->
		<dependency>
			<groupId>org.oscarehr.ckd</groupId>
			<artifactId>ckd</artifactId>
			<version>1.0</version>
		</dependency>
		<dependency>
			<groupId>jspellchecker</groupId>
			<artifactId>jazzy-core</artifactId>
			<version>unknown</version>
		</dependency>
		<!-- patientSiteVisit.jar , suspect this is generated classes and should be removed as a jar in the future -->
		<dependency>
			<groupId>patientSiteVisit</groupId>
			<artifactId>patientSiteVisit</artifactId>
			<version>0.0-SNAPSHOT</version>
		</dependency>
		<!-- surveyModel.jar , suspect this is generated classes and should be removed as a jar in the future -->
		<dependency>
			<groupId>surveyModel</groupId>
			<artifactId>surveyModel</artifactId>
			<version>0.0-SNAPSHOT</version>
		</dependency>
		<!-- jaxm api ... why do we need this? -->
		<dependency>
			<groupId>javax.xml</groupId>
			<artifactId>jaxm-api</artifactId>
			<version>UNKNOWN</version>
		</dependency>
		<!-- apache poi -->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>3.17</version>
			<exclusions>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- ostermillerutils_1_04_03_for_java_1_4.jar we need to stop using this and replace it with apache commons -->
		<dependency>
			<groupId>com.ostermiller</groupId>
			<artifactId>ostermillerutils</artifactId>
			<version>1.4.3</version>
		</dependency>
		<!-- sun xacml -->
		<dependency>
			<groupId>com.sun</groupId>
			<artifactId>xacml</artifactId>
			<version>1.2</version>
		</dependency>
		<!-- sun pdfview -->
		<dependency>
			<groupId>com.sun</groupId>
			<artifactId>pdfview</artifactId>
			<version>UNKNOWN</version>
		</dependency>
		<!-- display tag -->
		<dependency>
			<groupId>displaytag</groupId>
			<artifactId>displaytag</artifactId>
			<version>1.1.1</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>jcl104-over-slf4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.google.zxing/core -->
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>core</artifactId>
			<version>3.3.2</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.google.zxing/javase -->
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>javase</artifactId>
			<version>3.3.2</version>
		</dependency>
		<dependency>
			<groupId>org.chip.ping</groupId>
			<artifactId>oscar-ping</artifactId>
			<version>UNKNOWN</version>
		</dependency>
		<dependency>
			<groupId>org.chip.ping</groupId>
			<artifactId>ping-client</artifactId>
			<version>UNKNOWN</version>
		</dependency>
		<dependency>
			<groupId>org.chip.ping</groupId>
			<artifactId>ping-core</artifactId>
			<version>UNKNOWN</version>
		</dependency>
		<dependency>
			<groupId>org.chip.ping</groupId>
			<artifactId>ping-server</artifactId>
			<version>UNKNOWN</version>
		</dependency>
		<dependency>
			<groupId>org.chip.ping</groupId>
			<artifactId>ping-xml</artifactId>
			<version>UNKNOWN</version>
		</dependency>
		<!-- jtidy -->
		<dependency>
			<groupId>net.sf.jtidy</groupId>
			<artifactId>jtidy</artifactId>
			<version>r938</version>
		</dependency>
		<!-- xerces -->
		<dependency>
			<groupId>xerces</groupId>
			<artifactId>xercesImpl</artifactId>
			<version>2.12.0</version>
		</dependency>
		<!-- janino -->
		<dependency>
			<groupId>janino</groupId>
			<artifactId>janino</artifactId>
			<version>2.3.2</version>
		</dependency>
		<!-- *sigh* we really need to get rid of this -->
		<dependency>
			<groupId>pluginframework</groupId>
			<artifactId>pluginframework</artifactId>
			<version>0.9.13</version>
		</dependency>
		<!-- ocan -->
		<dependency>
			<groupId>ocan</groupId>
			<artifactId>ocan-iar</artifactId>
			<version>3.0</version>
		</dependency>
		<dependency>
			<groupId>ocan</groupId>
			<artifactId>ocan-iar-consent</artifactId>
			<version>UNKNOWN</version>
		</dependency>
		<dependency>
			<groupId>ocan</groupId>
			<artifactId>ocan-iar-phr</artifactId>
			<version>UNKNOWN</version>
		</dependency>
		<dependency>
			<groupId>com.jcraft</groupId>
			<artifactId>jsch</artifactId>
			<version>0.1.55</version>
		</dependency>
		<!-- OLIS -->
		<dependency>
			<groupId>ca.ssha.www</groupId>
			<artifactId>olis-service</artifactId>
			<version>20111111</version>
		</dependency>
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcmail-jdk15on</artifactId>
			<version>1.51</version>
		</dependency>
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15on</artifactId>
			<version>1.54</version>
		</dependency>
		<!-- AXIS2 (for OLIS) -->
		<dependency>
			<groupId>org.apache.axis2</groupId>
			<artifactId>axis2</artifactId>
			<version>1.5.4</version>
		</dependency>
		<dependency>
			<groupId>org.apache.axis2</groupId>
			<artifactId>axis2-transport-http</artifactId>
			<version>1.5.4</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.ws.commons.schema</groupId>
					<artifactId>XmlSchema</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.neethi</groupId>
					<artifactId>neethi</artifactId>
				</exclusion>
				<exclusion>
					<groupId>javax.servlet</groupId>
					<artifactId>servlet-api</artifactId>
				</exclusion>
        <exclusion>
          <groupId>org.codehaus.woodstox</groupId>
          <artifactId>woodstox-core-asl</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.codehaus.woodstox</groupId>
          <artifactId>wstx-asl</artifactId>
        </exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.axis2</groupId>
			<artifactId>axis2-transport-local</artifactId>
			<version>1.5.4</version>
		</dependency>
		<dependency>
			<groupId>org.apache.ws.commons.axiom</groupId>
			<artifactId>axiom-api</artifactId>
			<version>1.2.11</version>
		</dependency>
		<dependency>
			<groupId>org.apache.ws.commons.axiom</groupId>
			<artifactId>axiom-impl</artifactId>
			<version>1.2.11</version>
      <exclusions>
        <exclusion>
          <groupId>org.codehaus.woodstox</groupId>
          <artifactId>wstx-asl</artifactId>
        </exclusion>
      </exclusions>
		</dependency>
		<!-- velocity -->
		<dependency>
			<groupId>org.apache.velocity</groupId>
			<artifactId>velocity</artifactId>
			<version>1.7</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.velocity</groupId>
			<artifactId>velocity-tools</artifactId>
			<version>2.0</version>
			<scope>compile</scope>
			<exclusions>
				<exclusion>
					<groupId>org.apache.struts</groupId>
					<artifactId>struts-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.struts</groupId>
					<artifactId>struts-taglib</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.struts</groupId>
					<artifactId>struts-tiles</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- CXF -->
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>apache-cxf</artifactId>
			<version>${cxf.version}</version>
			<type>pom</type>
			<exclusions>
				<exclusion>
					<groupId>javax.servlet</groupId>
					<artifactId>javax.servlet-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.springframework</groupId>
					<artifactId>spring-web</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>jcl-over-slf4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-jdk14</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.geronimo.specs</groupId>
					<artifactId>geronimo-servlet_2.5_spec</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.geronimo.specs</groupId>
					<artifactId>geronimo-servlet_3.0_spec</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.cxf.services.ws-discovery</groupId>
					<artifactId>cxf-services-ws-discovery-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.cxf.services.ws-discovery</groupId>
					<artifactId>cxf-services-ws-discovery-service</artifactId>
				</exclusion>
				<exclusion>
					<groupId>javax.servlet</groupId>
					<artifactId>servlet-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-frontend-jaxws</artifactId>
			<version>${cxf.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-transports-http</artifactId>
			<version>${cxf.version}</version>
		</dependency>
		<dependency>
			<groupId>org.codehaus.woodstox</groupId>
			<artifactId>stax2-api</artifactId>
			<version>4.1</version>
		</dependency>
    <dependency>
      <groupId>com.fasterxml.woodstox</groupId>
      <artifactId>woodstox-core</artifactId>
      <version>5.3.0</version>
    </dependency>
		<dependency>
			<groupId>org.oscarehr</groupId>
			<artifactId>myoscar_client_utils</artifactId>
			<version>2013.07.22.TLS</version>
			<exclusions>
				<exclusion>
					<groupId>com.google.collections</groupId>
					<artifactId>google-collections</artifactId>
				</exclusion>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.oscarehr.myoscar_server</groupId>
			<artifactId>myoscar_server_client_stubs2</artifactId>
			<version>2014.10.24</version>
		</dependency>
		<!-- MARC-HI - Everest Framework -->
		<dependency>
			<groupId>org.marc.everest</groupId>
			<artifactId>everest-core</artifactId>
			<version>1.1.0</version>
		</dependency>
		<dependency>
			<groupId>org.marc.everest</groupId>
			<artifactId>everest-rmim-ca-r02-04-03</artifactId>
			<version>1.1.0</version>
		</dependency>
		<dependency>
			<groupId>org.marc.everest</groupId>
			<artifactId>everest-rmim-uv-cdar2</artifactId>
			<version>1.1.0</version>
		</dependency>
		<dependency>
			<groupId>org.marc.everest</groupId>
			<artifactId>everest-formatters-xml-its1</artifactId>
			<version>1.1.0</version>
		</dependency>
		<dependency>
			<groupId>org.marc.everest</groupId>
			<artifactId>everest-formatters-xml-dt-r1</artifactId>
			<version>1.1.0</version>
		</dependency>
		<!-- MARC-HI - Shared Health Integration Components (SHIC) Library -->
		<dependency>
			<groupId>org.marc.shic</groupId>
			<artifactId>shic-core</artifactId>
			<version>1.0.8</version>
			<exclusions>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bcprov-jdk15</artifactId>
				</exclusion>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.marc.shic</groupId>
			<artifactId>shic-xds</artifactId>
			<version>1.0.8</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>jcl-over-slf4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>jul-to-slf4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>log4j-over-slf4j</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-simple</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.santuario</groupId>
					<artifactId>xmlsec</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bcprov-jdk15</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.opensaml</groupId>
					<artifactId>opensaml</artifactId>
				</exclusion>
        <exclusion>
          <groupId>org.codehaus.woodstox</groupId>
          <artifactId>woodstox-core-asl</artifactId>
        </exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.marc.shic</groupId>
			<artifactId>shic-atna</artifactId>
			<version>1.0.8</version>
		</dependency>
		<dependency>
			<groupId>org.marc.shic</groupId>
			<artifactId>shic-pix</artifactId>
			<version>1.0.8</version>
		</dependency>
		<dependency>
			<groupId>org.marc.shic</groupId>
			<artifactId>shic-svs</artifactId>
			<version>1.0.8</version>
		</dependency>
		<dependency>
			<groupId>org.marc.shic</groupId>
			<artifactId>shic-cda</artifactId>
			<version>1.0.8</version>
			<exclusions>
				<exclusion>
					<groupId>org.reflections</groupId>
					<artifactId>reflections</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- Apache Tika - MimeType handling for downloaded files (ie. XDS) -->
		<dependency>
			<groupId>org.apache.tika</groupId>
			<artifactId>tika-core</artifactId>
			<version>1.5</version>
		</dependency>
		<!--tagsoup-->
		<dependency>
			<groupId>org.ccil.cowan.tagsoup</groupId>
			<artifactId>tagsoup</artifactId>
			<version>1.2.1</version>
		</dependency>
		<dependency>
			<groupId>com.cbi.ws</groupId>
			<artifactId>cbi_ws_client</artifactId>
			<version>1.1</version>
		</dependency>
		<dependency>
			<groupId>rome</groupId>
			<artifactId>rome</artifactId>
			<version>1.0</version>
		</dependency>
		<dependency>
			<groupId>org.oscarehr.integration.ebs</groupId>
			<artifactId>ebs-client</artifactId>
			<version>0.0.5</version>
			<exclusions>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bcprov-jdk16</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.santuario</groupId>
					<artifactId>xmlsec</artifactId>
				</exclusion>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.oscarehr.integration.ebs</groupId>
			<artifactId>edt-stubs</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>org.oscarehr.integration.ebs</groupId>
			<artifactId>hcv-stubs</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>${jackson.version}</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>${jackson.version}</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.jaxrs</groupId>
			<artifactId>jackson-jaxrs-json-provider</artifactId>
			<version>${jackson.version}</version>
		</dependency>
		<dependency>
			<!-- json support for java 8 localDate -->
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-jsr310</artifactId>
			<version>${jackson.version}</version>
		</dependency>
		<dependency>
			<groupId>io.prometheus</groupId>
			<artifactId>simpleclient</artifactId>
			<version>0.12.0</version>
		</dependency>
		<dependency>
			<groupId>io.prometheus</groupId>
			<artifactId>simpleclient_servlet</artifactId>
			<version>0.12.0</version>
		</dependency>
		<dependency>
			<groupId>javax.ws.rs</groupId>
			<artifactId>javax.ws.rs-api</artifactId>
			<version>2.1</version>
		</dependency>
		<dependency>
			<groupId>io.swagger.core.v3</groupId>
			<artifactId>swagger-models</artifactId>
			<version>2.1.11</version>
		</dependency>
		<dependency>
			<groupId>io.swagger.core.v3</groupId>
			<artifactId>swagger-jaxrs2</artifactId>
			<version>2.1.11</version>
		</dependency>
		<dependency>
			<groupId>com.sun.xml.ws</groupId>
			<artifactId>jaxws-ri</artifactId>
			<version>2.3.0</version>
			<type>pom</type>
			<exclusions>
				<exclusion>
					<groupId>com.sun.xml.ws</groupId>
					<artifactId>jaxws-eclipselink-plugin</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.sun.xml.ws</groupId>
					<artifactId>sdo-eclipselink-plugin</artifactId>
				</exclusion>
        <exclusion>
          <groupId>org.codehaus.woodstox</groupId>
          <artifactId>woodstox-core-asl</artifactId>
        </exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.auth0</groupId>
			<artifactId>java-jwt</artifactId>
			<version>3.8.1</version>
		</dependency>
		<dependency>
			<groupId>com.auth0</groupId>
			<artifactId>jwks-rsa</artifactId>
			<version>0.21.1</version>
		</dependency>
		<!-- Integration Tests -->
		<dependency>
			<groupId>org.seleniumhq.selenium</groupId>
			<artifactId>selenium-java</artifactId>
			<version>4.1.1</version>
		</dependency>
		<dependency>
			<groupId>org.seleniumhq.selenium</groupId>
			<artifactId>selenium-api</artifactId>
			<version>4.1.1</version>
		</dependency>
		<dependency>
			<groupId>org.seleniumhq.selenium</groupId>
			<artifactId>selenium-firefox-driver</artifactId>
			<version>4.1.1</version>
		</dependency>
		<dependency>
			<groupId>org.seleniumhq.selenium</groupId>
			<artifactId>selenium-remote-driver</artifactId>
			<version>4.1.1</version>
		</dependency>
		<!-- Google APIs for OAuth -->
		<dependency>
			<groupId>com.google.api-client</groupId>
			<artifactId>google-api-client</artifactId>
			<version>1.30.10</version>
		</dependency>
		<dependency>
			<groupId>com.google.apis</groupId>
			<artifactId>google-api-services-cloudresourcemanager</artifactId>
			<version>v1-rev20200720-1.30.10</version>
		</dependency>
		<dependency>
			<groupId>com.google.auth</groupId>
			<artifactId>google-auth-library-oauth2-http</artifactId>
			<version>0.21.1</version>
		</dependency>
		<dependency>
			<groupId>com.google.oauth-client</groupId>
			<artifactId>google-oauth-client</artifactId>
			<version>1.33.1</version>
		</dependency>
		<dependency>
			<groupId>com.google.oauth-client</groupId>
			<artifactId>google-oauth-client-servlet</artifactId>
			<version>1.33.1</version>
		</dependency>
		<!-- Lombok -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.20</version>
		</dependency>
		<!-- AQS -->
		<dependency>
			<groupId>ca.cloudpractice</groupId>
			<artifactId>aqs-java-client</artifactId>
			<version>0.4.14</version>
			<exclusions>
				<exclusion>
					<groupId>javax.annotation</groupId>
					<artifactId>jsr250-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- Memcached session manager -->
		<dependency>
			<groupId>de.javakaffee.msm</groupId>
			<artifactId>memcached-session-manager</artifactId>
			<version>${memcachedSessionManager.version}</version>
		</dependency>

		<dependency>
			<groupId>de.javakaffee.msm</groupId>
			<artifactId>memcached-session-manager-tc9</artifactId>
			<version>${memcachedSessionManager.version}</version>
		</dependency>

		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>2.9.0</version>
		</dependency>

		<dependency>
			<groupId>org.xeustechnologies</groupId>
			<artifactId>jcl-core</artifactId>
			<version>2.8</version>
		</dependency>
		<dependency>
			<groupId>apps.health</groupId>
			<artifactId>converter</artifactId>
			<version>0.1.1</version>
		</dependency>
		<dependency>
			<groupId>org.jetbrains</groupId>
			<artifactId>annotations</artifactId>
			<version>13.0</version>
		</dependency>
  </dependencies>

	<build>
		<resources>
			<resource>
				<directory>${basedir}/src/main/resources</directory>
				<filtering>true</filtering>
			</resource>
			<resource>
				<directory>${basedir}/database</directory>
				<filtering>false</filtering>
				<targetPath>create_instance/database</targetPath>
			</resource>
			<resource>
				<directory>${basedir}/package_files</directory>
				<filtering>false</filtering>
				<targetPath>create_instance/package_files</targetPath>
			</resource>
		</resources>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<image>
						<env>
							<BP_JVM_VERSION>16.0.2</BP_JVM_VERSION>
						</env>
					</image>
					<layers>
						<enabled>true</enabled>
					</layers>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>build-info</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.tomcat.maven</groupId>
				<artifactId>tomcat7-maven-plugin</artifactId>
				<version>2.2</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>2.6</version>
				<configuration>
					<nonFilteredFileExtensions>
						<nonFilteredFileExtension>pdf</nonFilteredFileExtension>
						<nonFilteredFileExtension>jar</nonFilteredFileExtension>
						<nonFilteredFileExtension>xls</nonFilteredFileExtension>
						<nonFilteredFileExtension>dat</nonFilteredFileExtension>
						<nonFilteredFileExtension>xsd</nonFilteredFileExtension>
						<nonFilteredFileExtension>jasper</nonFilteredFileExtension>
						<nonFilteredFileExtension>doc</nonFilteredFileExtension>
					</nonFilteredFileExtensions>
				</configuration>
			</plugin>
			<plugin>
				<groupId>pl.project13.maven</groupId>
				<artifactId>git-commit-id-plugin</artifactId>
        <configuration>
          <offline>true</offline>
          <verbose>false</verbose>
        </configuration>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>buildnumber-maven-plugin</artifactId>
				<version>1.0</version>
				<executions>
					<execution>
						<phase>validate</phase>
						<goals>
							<goal>create</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-site-plugin</artifactId>
				<version>3.0</version>
				<dependencies>
					<dependency>
						<groupId>org.apache.maven.doxia</groupId>
						<artifactId>doxia-module-markdown</artifactId>
						<version>1.3</version>
					</dependency>
				</dependencies>
				<configuration>
					<inputEncoding>UTF-8</inputEncoding>
					<outputEncoding>UTF-8</outputEncoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-war-plugin</artifactId>
				<version>3.3.1</version>
				<configuration>
					<failOnMissingWebXml>false</failOnMissingWebXml>
					<archive>
						<manifest>
							<addDefaultImplementationEntries>true</addDefaultImplementationEntries>
						</manifest>
						<manifestEntries>
							<git-SHA-1>${buildNumber}</git-SHA-1>
						</manifestEntries>
					</archive>
					<webResources>
						<webResource>
							<directory>${basedir}/src/main/webapp/admin</directory>
							<filtering>true</filtering>
						</webResource>
					</webResources>

					<!-- do not package unneeded files from JunoUI into WAR -->
					<!-- TODO: exclude everything but dist JS and CSS and the JSPs -->
					<packagingExcludes>
						web/*.sh,
						web/bower.json,
						web/package.json,
						web/gulpfile.js,
						web/node_modules/,
						web/**/*.ts
					</packagingExcludes>

				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.1</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<annotationProcessorPaths>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
							<version>1.18.20</version>
						</path>
						<path>
							<!-- generate metamodels for JPA criteria queries -->
							<groupId>org.hibernate</groupId>
							<artifactId>hibernate-jpamodelgen</artifactId>
							<version>${hibernate.version}</version>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-checkstyle-plugin</artifactId>
				<version>2.17</version>
				<configuration>
					<configLocation>utils/checkstyle.xml</configLocation>
					<failsOnError>true</failsOnError>
					<consoleOutput>true</consoleOutput>
					<sourceDirectory>.</sourceDirectory>
					<includes>src/main/**/*,database/**/*.sql</includes>
					<excludes>**/*.jar, **/*.txt, **/*.rpt, **/*.fla, **/*.xml, **/*.dtd, **/*.xslt, **/*.doc, **/*.bak, **/*.tld, **/*.jpg, **/*.png, **/*.gif, **/*.pdf, **/*.swf,
						**/*.js, **/*.css, src/main/webapp/share/calendar/**, **/*svg.jsp, src/main/webapp/casemgmt/Index2.jsp, database/mysql/updates/update-2016-06-06.sql,
						src/main/java/oscar/oscarMessenger/util/MsgDemoMap.java, src/main/java/oscar/oscarEncounter/pageUtil/EctDisplayMsgAction.java, database/mysql/oscarinit.sql,
						src/main/webapp/oscarEncounter/Index.jsp, database/mysql/oscarinit_on.sql, database/mysql/oscardata.sql, database/mysql/oscarinit_bc.sql
					</excludes>
				</configuration>
				<executions>
					<execution>
						<phase>process-sources</phase>
						<goals>
							<goal>checkstyle</goal>
						</goals>
					</execution>
				</executions>
				<dependencies>
					<dependency>
						<groupId>org.oscarehr</groupId>
						<artifactId>util</artifactId>
						<version>2013.03.06</version>
					</dependency>
				</dependencies>
			</plugin>
			<plugin>
				<groupId>com.mycila.maven-license-plugin</groupId>
				<artifactId>maven-license-plugin</artifactId>
				<version>1.10.b1</version>
				<configuration>
					<header>utils/headers/mcmaster.txt</header>
					<validHeaders>
						<validHeader>utils/headers/os.txt</validHeader>
						<validHeader>utils/headers/quatro.txt</validHeader>
						<validHeader>utils/headers/quatro2.txt</validHeader>
						<validHeader>utils/headers/md.txt</validHeader>
						<validHeader>utils/headers/caisi.txt</validHeader>
						<validHeader>utils/headers/hs.txt</validHeader>
						<validHeader>utils/headers/pc.txt</validHeader>
						<validHeader>utils/headers/andromedia.txt</validHeader>
						<validHeader>utils/headers/lgpl.txt</validHeader>
						<validHeader>utils/headers/asl.txt</validHeader>
						<validHeader>utils/headers/cmi.txt</validHeader>
						<validHeader>utils/headers/indivica.txt</validHeader>
						<validHeader>utils/headers/peaceworks.txt</validHeader>
						<validHeader>utils/headers/ubc.txt</validHeader>
						<validHeader>utils/headers/uvic.txt</validHeader>
						<validHeader>utils/headers/kai.txt</validHeader>
						<validHeader>utils/headers/cloudpractice.txt</validHeader>
						<validHeader>utils/headers/formated_caisi.txt</validHeader>
						<validHeader>utils/headers/well.txt</validHeader>
						<validHeader>utils/headers/mcmaster.txt</validHeader>
					</validHeaders>
					<includes>
						<include>src/main/java/**/*.java</include>
						<include>src/test/java/**/*.java</include>
						<include>src/main/webapp/**/*.jsp</include>
					</includes>
					<excludes>
						<exclude>src/main/webapp/billing/CA/BC/billingAccountReports.jsp</exclude>
						<exclude>src/main/webapp/billing/CA/BC/billingPreferences.jsp</exclude>
						<exclude>src/main/webapp/billing/CA/BC/billingSVCTrayAssoc.jsp</exclude>
						<exclude>src/main/webapp/billing/CA/BC/billTransactions.jsp</exclude>
						<exclude>src/main/webapp/billing/CA/BC/billType_frag.jsp</exclude>
						<exclude>src/main/webapp/billing/CA/BC/deletePrivateCode.jsp</exclude>
						<exclude>src/main/webapp/billing/CA/BC/dxcode_svccode_assoc.jsp</exclude>
						<exclude>src/main/webapp/billing/CA/BC/manageSVCDXAssoc.jsp</exclude>
						<exclude>src/main/webapp/billing/CA/BC/receivePayment.jsp</exclude>
						<exclude>src/main/webapp/billing/CA/BC/saveAssocs.jsp</exclude>
						<exclude>src/main/webapp/billing/CA/BC/settleBG.jsp</exclude>
						<exclude>src/main/webapp/report/tabulardaysheetreport.jsp</exclude>

						<exclude>src/main/webapp/form/formadf.jsp</exclude>
						<exclude>src/main/webapp/form/formadfv2.jsp</exclude>
						<exclude>src/main/webapp/form/formbcar.jsp</exclude>
						<exclude>src/main/webapp/form/formbcarpg1.jsp</exclude>
						<exclude>src/main/webapp/form/formbcarpg1namepopup.jsp</exclude>
						<exclude>src/main/webapp/form/formbcarpg2.jsp</exclude>
						<exclude>src/main/webapp/form/formbcarpg3.jsp</exclude>
						<exclude>src/main/webapp/form/formbcbirthsummo.jsp</exclude>
						<exclude>src/main/webapp/form/formbcclientchartchecklist.jsp</exclude>
						<exclude>src/main/webapp/form/formbcinr.jsp</exclude>
						<exclude>src/main/webapp/form/formbcnewborn.jsp</exclude>
						<exclude>src/main/webapp/form/formbcnewbornpg1.jsp</exclude>
						<exclude>src/main/webapp/form/formbcnewbornpg2.jsp</exclude>
						<exclude>src/main/webapp/form/formbcnewbornpg3.jsp</exclude>
						<exclude>src/main/webapp/form/formGrowth0_36.jsp</exclude>
						<exclude>src/main/webapp/form/formGrowth0_36Print.jsp</exclude>
						<exclude>src/main/webapp/form/formGrowthChart.jsp</exclude>
						<exclude>src/main/webapp/form/formGrowthChartPrint.jsp</exclude>
						<exclude>src/main/webapp/form/formimmunallergy.jsp</exclude>
						<exclude>src/main/webapp/form/formInvoice.jsp</exclude>
						<exclude>src/main/webapp/form/formonar.jsp</exclude>
						<exclude>src/main/webapp/form/formonarpg1.jsp</exclude>
						<exclude>src/main/webapp/form/formonarpg2.jsp</exclude>
						<exclude>src/main/webapp/form/formonarpg3.jsp</exclude>
						<exclude>src/main/webapp/form/formonarpg4.jsp</exclude>
						<exclude>src/main/webapp/form/formovulation.jsp</exclude>
						<exclude>src/main/webapp/form/study/ar2ping.jsp</exclude>
						<exclude>src/main/webapp/form/study/dm2ping.jsp</exclude>
						<exclude>src/main/webapp/form/study/dmdata.jsp</exclude>
						<exclude>src/main/webapp/form/study/formar2ping.jsp</exclude>
						<exclude>src/main/webapp/form/study/formarpg1.jsp</exclude>
						<exclude>src/main/webapp/form/study/formarpg2.jsp</exclude>
						<exclude>src/main/webapp/form/study/formarpg3.jsp</exclude>
						<exclude>src/main/webapp/form/study/formdiabete2ping.jsp</exclude>
						<exclude>src/main/webapp/report/onbillingtotal.jsp</exclude>
						<exclude>src/main/webapp/report/reportBCARDemo2.jsp</exclude>
						<exclude>src/main/webapp/report/reportBCARDemo.jsp</exclude>
						<exclude>src/main/webapp/report/reportbcedblist.jsp</exclude>
						<exclude>src/main/webapp/report/reportbilledvisit1.jsp</exclude>
						<exclude>src/main/webapp/report/reportbilledvisit2.jsp</exclude>
						<exclude>src/main/webapp/report/reportbilledvisit3.jsp</exclude>
						<exclude>src/main/webapp/report/reportbilledvisit.jsp</exclude>
						<exclude>src/main/webapp/report/reportdxvisit.jsp</exclude>
						<exclude>src/main/webapp/report/reportFilter.jsp</exclude>
						<exclude>src/main/webapp/report/reportFormCaption.jsp</exclude>
						<exclude>src/main/webapp/report/reportFormConfig.jsp</exclude>
						<exclude>src/main/webapp/report/reportFormDemoConfig.jsp</exclude>
						<exclude>src/main/webapp/report/reportFormOrder.jsp</exclude>
						<exclude>src/main/webapp/report/reportFormRecord.jsp</exclude>
						<exclude>src/main/webapp/report/reportonbilledphcp.jsp</exclude>
						<exclude>src/main/webapp/report/reportonbilledvisit.jsp</exclude>
						<exclude>src/main/webapp/report/reportonbilledvisitprovider.jsp</exclude>
						<exclude>src/main/webapp/report/reportonedblist.jsp</exclude>
						<exclude>src/main/webapp/report/reportResult.jsp</exclude>

						<exclude>src/main/webapp/billing/billingBrazil.jsp</exclude>
						<exclude>src/main/webapp/billing/billingBrazilSuccess.jsp</exclude>
						<exclude>src/main/webapp/billing/billingConsFatMedBrazil.jsp</exclude>
						<exclude>src/main/webapp/billing/billingConsFatPatBrazil.jsp</exclude>
						<exclude>src/main/webapp/popup/atividade.jsp</exclude>
						<exclude>src/main/webapp/popup/cid.jsp</exclude>
						<exclude>src/main/webapp/popup/procedimento.jsp</exclude>
						<exclude>src/main/webapp/provider/setAppointmentCountPrefs.jsp</exclude>
						<exclude>src/main/java/oscar/entities/S21.java</exclude>
						<exclude>src/main/java/oscar/entities/S22.java</exclude>
						<exclude>src/main/java/oscar/entities/S23.java</exclude>
						<exclude>src/main/java/oscar/oscarBilling/ca/bc/MSP/CDMReminderHlp.java</exclude>
						<exclude>src/main/java/oscar/oscarBilling/ca/bc/MSP/CreateBillingReportActionForm.java</exclude>
						<exclude>src/main/java/oscar/oscarBilling/ca/bc/MSP/CreateBillingReportAction.java</exclude>
						<exclude>src/main/java/oscar/oscarBilling/ca/bc/MSP/ServiceCodeValidationLogic.java</exclude>
						<exclude>src/main/java/oscar/oscarBilling/ca/bc/MSP/ServiceCodeValidator.java</exclude>
						<exclude>src/main/java/oscar/oscarBilling/ca/bc/MSP/SexValidator.java</exclude>
						<exclude>src/main/java/oscar/oscarDemographic/PrintDemoAddressLabelAction.java</exclude>
						<exclude>src/main/java/oscar/oscarDemographic/PrintDemoChartLabelAction.java</exclude>
						<exclude>src/main/java/oscar/oscarDemographic/PrintDemoLabelAction.java</exclude>
						<exclude>src/main/java/oscar/OscarDocumentCreator.java</exclude>
						<exclude>src/main/webapp/oscarReport/patientlist.jsp</exclude>
						<exclude>src/main/java/oscar/oscarReport/data/DoctorList.java</exclude>
						<exclude>src/main/webapp/jspspellcheck/*.jsp</exclude>
						<exclude>src/main/java/net/sf/jasperreports/renderers/BarbecueRenderer.java</exclude>
						<exclude>src/main/java/oscar/oscarDB/ResultSetBuilder.java</exclude>
						<exclude>src/main/java/oscar/login/UAgentInfo.java</exclude>
						<exclude>src/main/webapp/lab/CA/ON/uploadComplete.jsp</exclude>
						<exclude>src/main/webapp/lab/CA/ALL/uploadComplete.jsp</exclude>
						<exclude>src/**/*.txt</exclude>
						<exclude>src/**/*.js</exclude>
						<exclude>database/**</exclude>
						<exclude>docs/**</exclude>
						<exclude>local_repo/**</exclude>
						<exclude>**/.gitignore</exclude>
						<exclude>src/main/java/oscar/form/FrmAdfRecord.java</exclude>
						<exclude>src/main/java/oscar/form/FrmAdfV2Record.java</exclude>
						<exclude>src/main/java/oscar/form/FrmBCARRecord.java</exclude>
						<exclude>src/main/java/oscar/form/FrmBCBrithSumMoRecord.java</exclude>
						<exclude>src/main/java/oscar/form/FrmBCClientChartChecklistRecord.java</exclude>
						<exclude>src/main/java/oscar/form/FrmBCINRRecord.java</exclude>
						<exclude>src/main/java/oscar/form/FrmBCNewBornRecord.java</exclude>
						<exclude>src/main/java/oscar/form/FrmGrowth0_36Record.java</exclude>
						<exclude>src/main/java/oscar/form/FrmGrowthChartRecord.java</exclude>
						<exclude>src/main/java/oscar/form/FrmInvoiceRecord.java</exclude>
						<exclude>src/main/java/oscar/form/FrmONARRecord.java</exclude>
						<exclude>src/main/java/oscar/form/FrmOvulationRecord.java</exclude>
						<exclude>src/main/java/oscar/form/graphic/FrmPdfGraphicAR.java</exclude>
						<exclude>src/main/java/oscar/form/graphic/FrmPdfGraphicGrowthChart.java</exclude>
						<exclude>src/main/java/oscar/form/pdfservlet/FrmPDFServlet.java</exclude>
						<exclude>src/main/java/oscar/form/study/FrmStudyPING_DiabetesRecord.java</exclude>

						<exclude>src/main/java/org/apache/xml/security/encryption/XMLCipher.java</exclude>
						<exclude>src/main/java/org/apache/xml/security/encryption/DocumentSerializer.java</exclude>
						<exclude>src/main/webapp/library/bootstrap2-datepicker/**</exclude>
						<exclude>src/main/webapp/library/ng-table/**</exclude>
						<exclude>src/main/webapp/web/**</exclude>

						<!-- omit generated models -->
						<exclude>src/main/java/org/oscarehr/common/xml/**</exclude>
						<exclude>src/gen/java/**</exclude>
					</excludes>
					<strictCheck>true</strictCheck>
				</configuration>
				<executions>
					<execution>
						<phase>process-sources</phase>
						<goals>
							<goal>check</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-pmd-plugin</artifactId>
				<version>2.7.1</version>
				<configuration>
					<sourceEncoding>ISO-8859-1</sourceEncoding>
					<targetJdk>1.6</targetJdk>
					<rulesets>
						<ruleset>utils/pmd_rules.xml</ruleset>
					</rulesets>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<systemPropertyVariables>
						<oscar.skip.dbinit>${oscar.dbinit.skip}</oscar.skip.dbinit>
						<buildDirectory>${project.build.directory}</buildDirectory>
					</systemPropertyVariables>
					<excludes>
						<exclude>**/AR2005*.java</exclude>
						<exclude>**/OntarioMDSpec4DataTest.java</exclude>
						<exclude>**/ONAREnhancedBornConnectorTest.java</exclude>
						<exclude>integration/tests/*</exclude>
						<exclude>org/oscarehr/common/dao/**/*</exclude>
						<exclude>org/oscarehr/e2e/populator/body/*</exclude>
						<exclude>org/oscarehr/e2e/populator/header/*</exclude>
						<exclude>org/oscarehr/e2e/model/**/*</exclude>
						<exclude>org/oscarehr/eform/parser/**/*</exclude>
						<exclude>org/oscarehr/eyeform/dao/**/*</exclude>
						<exclude>org/oscarehr/labs/alberta/*</exclude>
						<exclude>org/oscarehr/PMmodule/dao/**/*</exclude>
						<exclude>org/oscarehr/billing/CA/ON/dao/**/*</exclude>
						<exclude>org/oscarehr/billing/CA/dao/**/*</exclude>
						<exclude>org/oscarehr/billing/CA/BC/dao/**/*</exclude>
						<exclude>org/oscarehr/hospitalReportManager/dao/**/*</exclude>
						<exclude>org/oscarehr/research/eaaps/EaapsIntegrationTest.java</exclude>
						<exclude>org/oscarehr/research/eaaps/EaapsHandlerTest.java</exclude>
						<exclude>org/oscarehr/labs/alberta/UploadingTest.java</exclude>
						<exclude>org/oscarehr/managers/DemographicManagerTest.java</exclude>
						<exclude>org/oscarehr/appointment/service/AppointmentStatusServiceTest.java</exclude>
						<exclude>org/oscarehr/ws/conversion/DemographicConverterTest.java</exclude>
						<exclude>org/oscarehr/casemgmt/service/DefaultNoteServiceTest.java</exclude>
						<exclude>org/oscarehr/e2e/constant/ConstantsTest.java</exclude>
						<exclude>org/oscarehr/e2e/util/EverestUtilsTest.java</exclude>
						<exclude>org/oscarehr/e2e/director/E2ECreatorTest.java</exclude>
						<exclude>org/oscarehr/e2e/populator/PopulatorTest.java</exclude>
						<exclude>org/oscarehr/e2e/populator/body/AbstractBodyPopulatorTest.java</exclude>
						<exclude>org/oscarehr/e2e/populator/AbstractPopulatorTest.java</exclude>
						<exclude>org/oscarehr/e2e/populator/AbstractPopulatorTest.java</exclude>
						<exclude>org/oscarehr/integration/born/ONAREnhancedBornConnectorTest.java</exclude>
						<exclude>org/oscarehr/integration/born/AR2005BornConnectorTest.java</exclude>
						<exclude>org/oscarehr/integration/born/AR2005Form2XMLTest.java</exclude>
					</excludes>
					<trimStackTrace>false</trimStackTrace>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<executions>
					<execution>
						<id>pre-integration-test</id>
						<phase>pre-integration-test</phase>
						<goals>
							<goal>test</goal>
						</goals>
						<configuration>
							<skipTests>false</skipTests>
							<test>SetupDatabase</test>
							<systemPropertyVariables>
								<oscar.skip.dbinit>${oscar.dbinit.skip}</oscar.skip.dbinit>
								<buildDirectory>${project.build.directory}</buildDirectory>
							</systemPropertyVariables>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-failsafe-plugin</artifactId>
				<configuration>
          <argLine>-Xms1024m -Xmx1024m</argLine>
          <runOrder>alphabetical</runOrder>
					<includes>
						<include>integration/tests/*</include>
						<include>org/oscarehr/common/dao/**/*</include>
						<include>org/oscarehr/e2e/populator/body/*</include>
						<include>org/oscarehr/e2e/populator/header/*</include>
						<include>org/oscarehr/e2e/model/**/*</include>
						<include>org/oscarehr/eform/parser/**/*</include>
						<include>org/oscarehr/eyeform/dao/**/*</include>
						<include>org/oscarehr/labs/alberta/*</include>
						<include>org/oscarehr/PMmodule/dao/**/*</include>
						<include>org/oscarehr/billing/CA/ON/dao/**/*</include>
						<include>org/oscarehr/billing/CA/dao/**/*</include>
						<include>org/oscarehr/billing/CA/BC/dao/**/*</include>
						<include>org/oscarehr/hospitalReportManager/dao/**/*</include>
						<include>org/oscarehr/research/eaaps/EaapsIntegrationTest.java</include>
						<include>org/oscarehr/research/eaaps/EaapsHandlerTest.java</include>
						<include>org/oscarehr/labs/alberta/UploadingTest.java</include>
						<include>org/oscarehr/managers/DemographicManagerTest.java</include>
						<include>org/oscarehr/appointment/service/AppointmentStatusServiceTest.java</include>
						<include>org/oscarehr/ws/conversion/DemographicConverterTest.java</include>
						<include>org/oscarehr/casemgmt/service/DefaultNoteServiceTest.java</include>
						<include>org/oscarehr/e2e/constant/ConstantsTest.java</include>
						<include>org/oscarehr/e2e/util/EverestUtilsTest.java</include>
						<include>org/oscarehr/e2e/director/E2ECreatorTest.java</include>
						<include>org/oscarehr/e2e/populator/PopulatorTest.java</include>
						<include>org/oscarehr/e2e/populator/body/AbstractBodyPopulatorTest.java</include>
						<include>org/oscarehr/e2e/populator/AbstractPopulatorTest.java</include>
						<include>org/oscarehr/e2e/populator/AbstractPopulatorTest.java</include>
						<include>org/oscarehr/integration/born/ONAREnhancedBornConnectorTest.java</include>
						<include>org/oscarehr/integration/born/AR2005BornConnectorTest.java</include>
						<include>org/oscarehr/integration/born/AR2005Form2XMLTest.java</include>
					</includes>
					<excludes>
						<exclude>**/SetupDatabase.java</exclude>
					</excludes>
					<skipTests>false</skipTests>
					<systemPropertyVariables>
						<oscar.skip.dbinit>true</oscar.skip.dbinit>
					</systemPropertyVariables>
					<useManifestOnlyJar>false</useManifestOnlyJar>
          <trimStackTrace>false</trimStackTrace>
				</configuration>
				<executions>
					<execution>
						<id>verify</id>
						<goals>
							<goal>verify</goal>
						</goals>
						<phase>integration-test</phase>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.oscarehr</groupId>
				<artifactId>oscar-il18n-check-plugin</artifactId>
				<version>1.0</version>
				<configuration>
					<referenceResourceFile>oscarResources_en.properties</referenceResourceFile>
					<otherResourceFiles>
						<param>oscarResources_es.properties</param>
						<param>oscarResources_fr.properties</param>
						<param>oscarResources_pt_BR.properties</param>
					</otherResourceFiles>
				</configuration>
				<dependencies>
					<dependency>
						<groupId>com.google.code</groupId>
						<artifactId>google-api-translate-java</artifactId>
						<version>0.95</version>
					</dependency>
				</dependencies>
			</plugin>
			<plugin>
				<groupId>org.mortbay.jetty</groupId>
				<artifactId>maven-jetty-plugin</artifactId>
				<version>6.1.26</version>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>native2ascii-maven-plugin</artifactId>
				<version>1.0-beta-1</version>
				<executions>
					<execution>
						<id>native2ascii-utf8-resources</id>
						<goals>
							<goal>native2ascii</goal>
						</goals>
						<configuration>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<!-- This plugin creates the openapi.json file for the internal REST api.  It is used -->
			<!-- below to generate code.  This must be run before the generate-typescpipt-api -->
			<!-- execution. -->
			<!-- TODO: This plugin should be replaced with swagger-maven-plugin once that plugin -->
			<!-- TODO: supports openapi V3 -->
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>exec-maven-plugin</artifactId>
				<version>1.6.0</version>
				<executions>
					<execution>
						<id>generate-openapi-spec</id>
						<phase>process-classes</phase>
						<goals>
							<goal>java</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<mainClass>org.oscarehr.maven.OpenApiSpecBuilder</mainClass>
					<classpathScope>test</classpathScope>
					<systemProperties>
						<systemProperty>
							<key>openApiSpecBuilder.resourceClassCsv</key>
							<value>
								<!-- Alphabetical please -->
								org.oscarehr.ws.rest.AppointmentService
								org.oscarehr.ws.rest.careTracker.CareTrackerWebService
								org.oscarehr.ws.rest.careTracker.CareTrackersWebService
								org.oscarehr.ws.rest.ClinicService
								org.oscarehr.ws.rest.consultation.ConsultationWebService
								org.oscarehr.ws.rest.consultation.SpecialistsWebService
								org.oscarehr.ws.rest.DecisionSupportWebService
								org.oscarehr.ws.rest.demographic.DemographicService
								org.oscarehr.ws.rest.demographic.DemographicsService
								org.oscarehr.ws.rest.demographic.DemographicCareTrackerWebService
								org.oscarehr.ws.rest.demographic.DemographicContactWebService
								org.oscarehr.ws.rest.demographic.DemographicContactsWebService
								org.oscarehr.ws.rest.demographic.DemographicDocumentsWebService
								org.oscarehr.ws.rest.demographic.DemographicDocumentWebService
								org.oscarehr.ws.rest.demographic.DemographicDxWebService
								org.oscarehr.ws.rest.demographic.DemographicMeasurementsWebService
								org.oscarehr.ws.rest.demographic.DemographicNoteWebService
								org.oscarehr.ws.rest.demographic.DemographicRxWebService
								org.oscarehr.ws.rest.demographic.DemographicWaitListWebService
								org.oscarehr.ws.rest.DocumentTypesWebService
								org.oscarehr.ws.rest.DiseaseRegistryService
								org.oscarehr.ws.rest.DocumentWebService
								org.oscarehr.ws.rest.eform.EFormInstanceWebService
								org.oscarehr.ws.rest.EFormService
								org.oscarehr.ws.rest.EFormsService
								org.oscarehr.ws.rest.fax.FaxAccountWebService
								org.oscarehr.ws.rest.fax.FaxInboundWebService
								org.oscarehr.ws.rest.fax.FaxOutboundWebService
								org.oscarehr.ws.rest.FormsService
								org.oscarehr.ws.rest.integrations.aqs.QueuedAppointmentWebService
								org.oscarehr.ws.rest.integrations.aqs.QueuedAppointmentsWebService
								org.oscarehr.ws.rest.integrations.aqs.QueueWebService
								org.oscarehr.ws.rest.integrations.hrm.HrmAccountWebService
								org.oscarehr.ws.rest.integrations.hrm.HrmCategoryWebService
								org.oscarehr.ws.rest.integrations.hrm.HrmCategoriesWebService
								org.oscarehr.ws.rest.integrations.hrm.HrmScheduleWebService
								org.oscarehr.ws.rest.integrations.hrm.HrmSubClassWebService
								org.oscarehr.ws.rest.integrations.iceFall.IceFallWebService
								org.oscarehr.ws.rest.integrations.polaris.PolarisWebService
								org.oscarehr.ws.rest.integrations.imdhealth.iMDHealthWebService
								org.oscarehr.ws.rest.integrations.netcare.NetcareWebService
								org.oscarehr.ws.rest.integrations.ontarioehr.OntarioEhrDHDRWebService
								org.oscarehr.ws.rest.integrations.ontarioehr.OntarioEhrWebService
								org.oscarehr.ws.rest.integrations.ontarioehr.ProviderOntarioEhrWebService
								org.oscarehr.ws.rest.lab.LabWebService
								org.oscarehr.ws.rest.lab.olis.OlisLabWebService
								org.oscarehr.ws.rest.MeasurementsWebService
								org.oscarehr.ws.rest.myhealthaccess.AppointmentWebService
								org.oscarehr.ws.rest.myhealthaccess.clinic.ConversationWebService
								org.oscarehr.ws.rest.myhealthaccess.clinic.message.AttachmentWebService
								org.oscarehr.ws.rest.myhealthaccess.clinic.MessagesWebService
								org.oscarehr.ws.rest.myhealthaccess.clinic.MessageWebService
								org.oscarehr.ws.rest.myhealthaccess.DemographicWebService
								org.oscarehr.ws.rest.myhealthaccess.IntegrationWebService
								org.oscarehr.ws.rest.myhealthaccess.IntegrationsWebService
								org.oscarehr.ws.rest.myhealthaccess.MhaPatientsWebService
								org.oscarehr.ws.rest.myhealthaccess.MhaPatientWebService
								org.oscarehr.ws.rest.myhealthaccess.patient.AccessWebService
								org.oscarehr.ws.rest.myhealthaccess.SSOWebService
								org.oscarehr.ws.rest.NotesService
								org.oscarehr.ws.rest.PreventionService
								org.oscarehr.ws.rest.provider.ProviderPreferenceWebService
								org.oscarehr.ws.rest.provider.ProviderWebService
								org.oscarehr.ws.rest.provider.ProvidersWebService
								org.oscarehr.ws.rest.ReferralDoctorsService
								org.oscarehr.ws.rest.reporting.ReportingWebService
								org.oscarehr.ws.rest.RosterWebService
								org.oscarehr.ws.rest.ScheduleService
								org.oscarehr.ws.rest.SecurityRolesWebService
								org.oscarehr.ws.rest.SitesService
								org.oscarehr.ws.rest.SystemPreferenceWebService
								org.oscarehr.ws.rest.WaitListWebService
							</value>
						</systemProperty>
						<systemProperty>
							<key>openApiSpecBuilder.outputDirectory</key>
							<value>${project.build.directory}/openapi/</value>
						</systemProperty>
					</systemProperties>
					<skip>${juno.webpack.skip}</skip>
				</configuration>
        <dependencies>
          <dependency>
            <groupId>javax.ws.rs</groupId>
            <artifactId>javax.ws.rs-api</artifactId>
            <version>2.1</version>
          </dependency>
          <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-models</artifactId>
            <version>2.0.1</version>
          </dependency>
          <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-jaxrs2</artifactId>
            <version>2.0.1</version>
          </dependency>
        </dependencies>
			</plugin>

			<!-- This plugin generates typescript models from the openapi.json file generated -->
			<!-- above. This must be run after the generate-openapi-spec execution -->
			<!-- This script must also be run before gulp so the files are available to compile -->
			<plugin>
				<groupId>org.openapitools</groupId>
				<artifactId>openapi-generator-maven-plugin</artifactId>
				<version>5.1.0</version>
				<executions>
					<execution>
						<id>generate-typescript-api</id>
						<phase>process-classes</phase>
						<goals>
							<goal>generate</goal>
						</goals>
						<configuration>
							<inputSpec>${project.build.directory}/openapi/openapi.json</inputSpec>
							<generatorName>typescript-angularjs-deprecated</generatorName>
							<output>src/main/frontend/generated/</output>
							<skip>${juno.webpack.skip}</skip>
							<skipValidateSpec>true</skipValidateSpec>
							<additionalProperties>
								<additionalProperty>enumPropertyNaming=PascalCase</additionalProperty>
							</additionalProperties>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<!-- plugin allows more than one source to be accessed in the project -->
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>build-helper-maven-plugin</artifactId>
				<version>3.2.0</version>
				<executions>
					<execution>
						<phase>generate-sources</phase>
						<goals>
							<goal>add-source</goal>
						</goals>
						<configuration>
							<sources>
								<!-- allow imports from these sources -->
								<source>src/gen/java</source>
							</sources>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<artifactId>maven-antrun-plugin</artifactId>
				<version>1.8</version>
				<executions>
					<execution>
						<id>fix-typescript-api</id>
						<phase>process-classes</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<skip>${juno.webpack.skip}</skip>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<!-- show maven clean how to clean up files generated by open-api generator above -->
			<plugin>
				<artifactId>maven-clean-plugin</artifactId>
				<version>3.1.0</version>
				<configuration>
					<filesets>
						<fileset>
							<directory>src/main/frontend/generated/</directory>
							<includes>
								<include>**/*.ts</include>
							</includes>
							<followSymlinks>false</followSymlinks>
						</fileset>
					</filesets>
					<skip>${juno.webpack.skip}</skip>
				</configuration>
			</plugin>

			<plugin>
				<artifactId>maven-antrun-plugin</artifactId>
				<version>1.7</version>
				<executions>
					<execution>
						<id>set_timestamp</id>
						<phase>process-classes</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<target>
								<tstamp>
									<format pattern="yyyy-MM-dd hh:mm aa" property="build.dateTime.value"/>
								</tstamp>
								<echo message="build time : ${build.dateTime.value}"/>
								<replace file="target/classes/oscar_mcmaster.properties" token="${build.dateTime}" value="${build.dateTime.value}"/>
								<property environment="env"/>
								<echo message="JOB_NAME - BUILD_NUMBER : ${env.JOB_NAME} - ${env.BUILD_NUMBER}"/>
								<replace file="target/classes/oscar_mcmaster.properties" token="${build.JOB_NAME}" value="${env.JOB_NAME}"/>
								<replace file="target/classes/oscar_mcmaster.properties" token="${build.BUILD_NUMBER}" value="${env.BUILD_NUMBER}"/>
							</target>

						</configuration>
					</execution>
					<execution>
						<id>copy_web_xml_for_jsp_precompile</id>
						<phase>prepare-package</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<target>
								<copy file="${basedir}/target/web.xml" tofile="${basedir}/target/${project.name}-${project.version}/WEB-INF/precompiled-jsp-web.xml"/>
							</target>
						</configuration>
					</execution>
					<execution>
						<id>deploy_local_catalina_base</id>
						<phase>package</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<target>
								<copy todir="catalina_base/webapps/oscar">
									<fileset dir="target/${project.name}-${project.version}"/>
								</copy>
							</target>
						</configuration>
					</execution>
					<execution>
						<id>clean_local_catalina_base</id>
						<phase>clean</phase>
						<configuration>
							<target>
								<delete dir="catalina_base/webapps/oscar"/>
								<delete includeemptydirs="true" quiet="true">
									<fileset dir="catalina_base/logs" excludes=".gitignore"/>
									<fileset dir="catalina_base/work" excludes=".gitignore"/>
								</delete>
							</target>
						</configuration>
						<goals>
							<goal>run</goal>
						</goals>
					</execution>
				</executions>
			</plugin>


			<!-- JunoUI -->
			<plugin>
				<groupId>com.github.eirslett</groupId>
				<artifactId>frontend-maven-plugin</artifactId>
				<version>1.3</version>

				<configuration>
					<workingDirectory>${basedir}/src/main/frontend</workingDirectory>
					<!-- TODO switch to JUNO directory -->
					<!-- <workingDirectory>${basedir}/src/main/webapp/juno</workingDirectory> -->
					<installDirectory>target</installDirectory>
					<nodeVersion>v6.10.3</nodeVersion>
					<skip>${juno.webpack.skip}</skip>
				</configuration>

				<executions>
					<!-- installs node and npm locally into target/node -->
					<execution>
						<id>install node and npm</id>
						<phase>process-classes</phase>
						<goals>
							<goal>install-node-and-npm</goal>
						</goals>
					</execution>
					<!-- runs npm install on working directory -->
					<execution>
						<id>npm install</id>
						<phase>process-classes</phase>
						<goals>
							<goal>npm</goal>
						</goals>
					</execution>
					<execution>
						<id>webpack {webpack.env}</id>
						<phase>process-classes</phase>
						<goals>
							<goal>webpack</goal>
						</goals>

						<configuration>
							<arguments>--config webpack.${webpack.env}.js</arguments>
						</configuration>
					</execution>
				</executions>
			</plugin>

      <!-- This has to run after webpack because webpack modifies some jsp files -->
			<plugin>
				<groupId>io.leonard.maven.plugins</groupId>
				<artifactId>jspc-maven-plugin</artifactId>
				<version>3.2.0</version>
				<executions>
					<execution>
						<id>jspc</id>
						<goals>
							<goal>compile</goal>
						</goals>
						<phase>process-classes</phase>
						<configuration>
							<excludes>
								<!-- These should already be included by other jsp files -->
								<exclude>**/*.jspf</exclude>
							</excludes>
							<keepSources>true</keepSources>
							<threads>4</threads>
							<!-- Use a dummy web.xml file just used for registering jsps compiled by jspc -->
							<webXml>${basedir}/src/main/webapp/WEB-INF/blank-web.xml</webXml>
						</configuration>
					</execution>
				</executions>
				<dependencies>
					<dependency>
						<groupId>org.apache.tomcat</groupId>
						<artifactId>tomcat-jasper</artifactId>
						<version>${tomcat.version}</version>
						<exclusions>
							<exclusion>
								<groupId>org.eclipse.jdt.core.compiler</groupId>
								<artifactId>ecj</artifactId>
							</exclusion>
						</exclusions>
					</dependency>
					<dependency>
						<groupId>org.eclipse.jdt.core.compiler</groupId>
						<artifactId>ecj</artifactId>
						<version>4.6.1</version>
					</dependency>
				</dependencies>
			</plugin>

      <!-- Maven Enforcer plugin to ban certain dependencies -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <version>3.2.1</version>
        <executions>
          <execution>
            <id>enforce-banned-dependencies</id>
            <phase>validate</phase>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <bannedDependencies>
                  <searchTransitive>true</searchTransitive>
                  <excludes>
                    <exclude>org.mockito:mockito-inline</exclude>
                  </excludes>
                  <message>mockito-inline conflicts with powermock, please use refactor your tests
                    to use powermock instead
                  </message>
                </bannedDependencies>
              </rules>
              <fail>true</fail>
            </configuration>
          </execution>
        </executions>
      </plugin>
		</plugins>
		<pluginManagement>
			<plugins>
				<!--This plugin's configuration is used to store Eclipse m2e settings only. It has no influence on the Maven build itself.-->
				<plugin>
					<groupId>org.eclipse.m2e</groupId>
					<artifactId>lifecycle-mapping</artifactId>
					<version>1.0.0</version>
					<configuration>
						<lifecycleMappingMetadata>
							<pluginExecutions>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>org.apache.maven.plugins
										</groupId>
										<artifactId>maven-antrun-plugin
										</artifactId>
										<versionRange>[1.3,)
										</versionRange>
										<goals>
											<goal>run</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<ignore/>
									</action>
								</pluginExecution>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>org.apache.maven.plugins
										</groupId>
										<artifactId>maven-checkstyle-plugin
										</artifactId>
										<versionRange>[2.8,)
										</versionRange>
										<goals>
											<goal>checkstyle</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<ignore/>
									</action>
								</pluginExecution>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>org.codehaus.mojo
										</groupId>
										<artifactId>native2ascii-maven-plugin
										</artifactId>
										<versionRange>[1.0-beta-1,)
										</versionRange>
										<goals>
											<goal>native2ascii</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<ignore/>
									</action>
								</pluginExecution>
							</pluginExecutions>
						</lifecycleMappingMetadata>
					</configuration>
				</plugin>
				<plugin>
					<groupId>org.codehaus.mojo</groupId>
					<artifactId>exec-maven-plugin</artifactId>
					<version>1.6.0</version>
				</plugin>
          </plugins>
        </pluginManagement>
      </build>
      <profiles>
        <profile>
          <id>jdk8</id>
          <activation>
            <activeByDefault>false</activeByDefault>
            <jdk>1.8</jdk>
          </activation>
          <dependencies>
            <dependency>
              <groupId>org.oscarehr</groupId>
              <artifactId>datasource-jdk7</artifactId>
              <version>1.0</version>
            </dependency>
          </dependencies>
          <build>
            <plugins>
              <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.9</version>
                <configuration>
                  <minmemory>128m</minmemory>
                  <maxmemory>1g</maxmemory>
                  <tags>
                    <tag>
                      <name>todo</name>
                      <placement>a</placement>
                      <head>To Do:</head>
                    </tag>
                  </tags>
                  <quiet>true</quiet>
                </configuration>
                <executions>
                  <execution>
                    <phase>verify</phase>
                    <goals>
                      <goal>javadoc</goal>
                    </goals>
                    <configuration>
                      <additionalparam>-Xdoclint:none</additionalparam>
                    </configuration>
                  </execution>
                </executions>
              </plugin>
            </plugins>
          </build>
        </profile>
        <profile>
          <id>jdk16</id>
          <activation>
            <activeByDefault>true</activeByDefault>
            <jdk>16</jdk>
          </activation>
          <dependencies>
            <dependency>
              <groupId>org.oscarehr</groupId>
              <artifactId>datasource-jdk7</artifactId>
              <version>1.0</version>
            </dependency>
          </dependencies>
          <build>
            <plugins>
              <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                  <minmemory>128m</minmemory>
                  <maxmemory>1g</maxmemory>
                  <tags>
                    <tag>
                      <name>todo</name>
                      <placement>a</placement>
                      <head>To Do:</head>
                    </tag>
                  </tags>
                  <quiet>true</quiet>
                </configuration>
                <executions>
                  <execution>
                    <phase>verify</phase>
                    <goals>
                      <goal>javadoc</goal>
                    </goals>
                    <configuration>
                      <additionalOptions>-Xdoclint:none</additionalOptions>
                    </configuration>
                  </execution>
                </executions>
              </plugin>
            </plugins>
          </build>
        </profile>
        <profile>
          <!-- run maven with -Dregenerate-source=true to re-generate files defined below -->
			<id>source-regeneration</id>
			<activation>
				<property>
					<name>regenerate-source</name>
				</property>
			</activation>
			<build>
				<plugins>
					<!-- plugin to generate java files from xsd schema, and allow similar ones to implement a shared interface -->
					<!-- this is used for CDS and HRM schemas for example -->
					<plugin>
						<groupId>org.jvnet.jaxb2.maven2</groupId>
						<artifactId>maven-jaxb2-plugin</artifactId>
						<version>0.14.0</version>
						<executions>
							<execution>
								<id>cds5.0-schema-generation</id>
								<goals>
									<goal>generate</goal>
								</goals>
								<phase>generate-resources</phase>
								<configuration>
									<extension>true</extension>
									<!-- no header to prevent all the generated files being changed on automatic re-compile -->
									<noFileHeader>true</noFileHeader>
									<args>
										<arg>-Xinheritance</arg>
									</args>
									<bindingDirectory>${basedir}/src/main/resources/org/oscarehr/common/xml/cds/v5_0</bindingDirectory>
									<bindingIncludes>
										<include>bindings.xjb</include>
									</bindingIncludes>
									<schemaDirectory>${basedir}/src/main/resources/org/oscarehr/common/xml/cds/v5_0</schemaDirectory>
									<generateDirectory>${basedir}/src/gen/java</generateDirectory>
									<generatePackage>xml.cds.v5_0</generatePackage>
								</configuration>
							</execution>
							<execution>
								<id>hrm4.3-schema-generation</id>
								<goals>
									<goal>generate</goal>
								</goals>
								<phase>generate-resources</phase>
								<configuration>
									<extension>true</extension>
									<!-- no header to prevent all the generated files being changed on automatic re-compile -->
									<noFileHeader>true</noFileHeader>
									<args>
										<arg>-Xinheritance</arg>
									</args>
									<bindingDirectory>${basedir}/src/main/resources/org/oscarehr/common/xml/hrm/v4_3</bindingDirectory>
									<bindingIncludes>
										<include>bindings.xjb</include>
									</bindingIncludes>
									<schemaDirectory>${basedir}/src/main/resources/org/oscarehr/common/xml/hrm/v4_3</schemaDirectory>
									<generateDirectory>${basedir}/src/gen/java</generateDirectory>
									<generatePackage>xml.hrm.v4_3</generatePackage>
								</configuration>
							</execution>
                            				<execution>
                                				<id>hrm4.1-schema-generation</id>
                                				<goals>
                                    					<goal>generate</goal>
                                				</goals>
                                				<phase>generate-resources</phase>
                                				<configuration>
                                    					<extension>true</extension>
                                    					<!-- no header to prevent all the generated files being changed on automatic re-compile -->
                                    					<noFileHeader>true</noFileHeader>
                                    					<args>
                                        					<arg>-Xinheritance</arg>
                                    					</args>
                                    				<bindingDirectory>${basedir}/src/main/resources/org/oscarehr/common/xml/hrm/v4_1</bindingDirectory>
                                    					<bindingIncludes>
                                        					<include>bindings.xjb</include>
                                    					</bindingIncludes>
                                    					<schemaDirectory>${basedir}/src/main/resources/org/oscarehr/common/xml/hrm/v4_1</schemaDirectory>
                                    					<generateDirectory>${basedir}/src/gen/java</generateDirectory>
                                    					<generatePackage>xml.hrm.v4_1</generatePackage>
                                				</configuration>
                            			</execution>
						</executions>
						<configuration>
							<extension>true</extension>
							<plugins>
								<plugin>
									<groupId>org.jvnet.jaxb2_commons</groupId>
									<artifactId>jaxb2-basics</artifactId>
									<version>1.11.1</version>
								</plugin>
							</plugins>
						</configuration>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>
</project>