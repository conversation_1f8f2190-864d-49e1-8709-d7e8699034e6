CREATE TABLE `criteria_type` (
  `CRITERIA_TYPE_ID` int(11) NOT NULL AUTO_INCREMENT,
  `FIELD_NAME` varchar(128) NOT NULL,
  `FIELD_TYPE` varchar(128) NOT NULL,
  `DEFAULT_VALUE` varchar(255),
  `ACTIVE` tinyint(1) NOT NULL,
  `WL_PROGRAM_ID` int(11),
  `CAN_BE_ADHOC` tinyint(1) NOT NULL,
  PRIMARY KEY (`CRITERIA_TYPE_ID`)
);

CREATE TABLE `criteria_type_option` (
  `OPTION_ID` int(11) NOT NULL AUTO_INCREMENT,
  `CRITERIA_TYPE_ID` int(11) NOT NULL,
  `DISPLAY_ORDER_NUMBER` int(11) NOT NULL,
  `OPTION_LABEL` varchar(128) NOT NULL,
  `OPTION_VALUE` varchar(255),
  `RANGE_START_VALUE` int(11),
  `RANGE_END_VALUE` int(11),
  <PERSON>IMAR<PERSON> KEY (`OPTION_ID`)
);

CREATE TABLE `criteria` (
  `CRITERIA_ID` int(11) NOT NULL AUTO_INCREMENT,
  `CRITERIA_TYPE_ID` int(11) NOT NULL,
  `CRITERIA_VALUE` varchar(255),
  `RANGE_START_VALUE` int(11),
  `RANGE_END_VALUE` int(11),
  `TEMPLATE_ID` int(11),
  `VACANCY_ID` int(11),
  `MATCH_SCORE_WEIGHT` double NOT NULL,
  `CAN_BE_ADHOC` tinyint(1) NOT NULL,
  PRIMARY KEY (`CRITERIA_ID`)
);

CREATE TABLE `criteria_selection_option` (
  `SELECT_OPTION_ID` int(11) NOT NULL AUTO_INCREMENT,
  `CRITERIA_ID` int(11) NOT NULL,
  `OPTION_VALUE` varchar(255),
  PRIMARY KEY (`SELECT_OPTION_ID`)
);

CREATE TABLE `vacancy` (
  `VACANCY_ID` int(11) NOT NULL AUTO_INCREMENT,
  `TEMPLATE_ID` int(11) NOT NULL,
  `STATUS` varchar(24) NOT NULL,
  `DATE_CLOSED` timestamp NULL,
  `REASON_CLOSED` varchar(255),
  PRIMARY KEY (`VACANCY_ID`)
);


/*
-- Query: SELECT * FROM test.criteria_type
LIMIT 0, 1000

-- Date: 2012-04-14 14:14
*/
INSERT INTO `criteria_type` (`CRITERIA_TYPE_ID`,`FIELD_NAME`,`FIELD_TYPE`,`DEFAULT_VALUE`,`ACTIVE`,`WL_PROGRAM_ID`,`CAN_BE_ADHOC`) VALUES (1,'Agency','select_multiple',NULL,1,1,0);
INSERT INTO `criteria_type` (`CRITERIA_TYPE_ID`,`FIELD_NAME`,`FIELD_TYPE`,`DEFAULT_VALUE`,`ACTIVE`,`WL_PROGRAM_ID`,`CAN_BE_ADHOC`) VALUES (2,'Age','select_one',NULL,1,1,0);
INSERT INTO `criteria_type` (`CRITERIA_TYPE_ID`,`FIELD_NAME`,`FIELD_TYPE`,`DEFAULT_VALUE`,`ACTIVE`,`WL_PROGRAM_ID`,`CAN_BE_ADHOC`) VALUES (3,'Area','select_multiple',NULL,1,1,0);
INSERT INTO `criteria_type` (`CRITERIA_TYPE_ID`,`FIELD_NAME`,`FIELD_TYPE`,`DEFAULT_VALUE`,`ACTIVE`,`WL_PROGRAM_ID`,`CAN_BE_ADHOC`) VALUES (4,'Serious and Persistent Mental Illness','select_one',NULL,1,1,0);
INSERT INTO `criteria_type` (`CRITERIA_TYPE_ID`,`FIELD_NAME`,`FIELD_TYPE`,`DEFAULT_VALUE`,`ACTIVE`,`WL_PROGRAM_ID`,`CAN_BE_ADHOC`) VALUES (5,'Serious and Persistent Mental Illness Diagnosis','select_multiple',NULL,1,1,0);
INSERT INTO `criteria_type` (`CRITERIA_TYPE_ID`,`FIELD_NAME`,`FIELD_TYPE`,`DEFAULT_VALUE`,`ACTIVE`,`WL_PROGRAM_ID`,`CAN_BE_ADHOC`) VALUES (6,'Serious and Persistent Mental Illness Hospitalization','number','0',1,1,0);
INSERT INTO `criteria_type` (`CRITERIA_TYPE_ID`,`FIELD_NAME`,`FIELD_TYPE`,`DEFAULT_VALUE`,`ACTIVE`,`WL_PROGRAM_ID`,`CAN_BE_ADHOC`) VALUES (7,'Type of Program','select_multiple',NULL,1,1,0);
INSERT INTO `criteria_type` (`CRITERIA_TYPE_ID`,`FIELD_NAME`,`FIELD_TYPE`,`DEFAULT_VALUE`,`ACTIVE`,`WL_PROGRAM_ID`,`CAN_BE_ADHOC`) VALUES (8,'Referral Source','select_one',NULL,1,1,0);
INSERT INTO `criteria_type` (`CRITERIA_TYPE_ID`,`FIELD_NAME`,`FIELD_TYPE`,`DEFAULT_VALUE`,`ACTIVE`,`WL_PROGRAM_ID`,`CAN_BE_ADHOC`) VALUES (9,'Legal History','select_multiple',NULL,1,1,0);
INSERT INTO `criteria_type` (`CRITERIA_TYPE_ID`,`FIELD_NAME`,`FIELD_TYPE`,`DEFAULT_VALUE`,`ACTIVE`,`WL_PROGRAM_ID`,`CAN_BE_ADHOC`) VALUES (10,'Residence','select_one',NULL,1,1,0);
INSERT INTO `criteria_type` (`CRITERIA_TYPE_ID`,`FIELD_NAME`,`FIELD_TYPE`,`DEFAULT_VALUE`,`ACTIVE`,`WL_PROGRAM_ID`,`CAN_BE_ADHOC`) VALUES (11,'Other Health Issues','select_multiple',NULL,1,1,0);
INSERT INTO `criteria_type` (`CRITERIA_TYPE_ID`,`FIELD_NAME`,`FIELD_TYPE`,`DEFAULT_VALUE`,`ACTIVE`,`WL_PROGRAM_ID`,`CAN_BE_ADHOC`) VALUES (12,'Language','select_multiple',NULL,1,1,0);
INSERT INTO `criteria_type` (`CRITERIA_TYPE_ID`,`FIELD_NAME`,`FIELD_TYPE`,`DEFAULT_VALUE`,`ACTIVE`,`WL_PROGRAM_ID`,`CAN_BE_ADHOC`) VALUES (13,'Gender','select_one',NULL,1,1,0);
INSERT INTO `criteria_type` (`CRITERIA_TYPE_ID`,`FIELD_NAME`,`FIELD_TYPE`,`DEFAULT_VALUE`,`ACTIVE`,`WL_PROGRAM_ID`,`CAN_BE_ADHOC`) VALUES (14,'Gender','select_one',NULL,1,2,0);
INSERT INTO `criteria_type` (`CRITERIA_TYPE_ID`,`FIELD_NAME`,`FIELD_TYPE`,`DEFAULT_VALUE`,`ACTIVE`,`WL_PROGRAM_ID`,`CAN_BE_ADHOC`) VALUES (15,'Homeless','select_one',NULL,1,2,0);
INSERT INTO `criteria_type` (`CRITERIA_TYPE_ID`,`FIELD_NAME`,`FIELD_TYPE`,`DEFAULT_VALUE`,`ACTIVE`,`WL_PROGRAM_ID`,`CAN_BE_ADHOC`) VALUES (16,'Mental health diagnosis','select_multiple',NULL,1,2,0);
INSERT INTO `criteria_type` (`CRITERIA_TYPE_ID`,`FIELD_NAME`,`FIELD_TYPE`,`DEFAULT_VALUE`,`ACTIVE`,`WL_PROGRAM_ID`,`CAN_BE_ADHOC`) VALUES (17,'Housing type','select_one',NULL,1,2,0);
INSERT INTO `criteria_type` (`CRITERIA_TYPE_ID`,`FIELD_NAME`,`FIELD_TYPE`,`DEFAULT_VALUE`,`ACTIVE`,`WL_PROGRAM_ID`,`CAN_BE_ADHOC`) VALUES (18,'Referral source','select_one',NULL,1,2,0);
INSERT INTO `criteria_type` (`CRITERIA_TYPE_ID`,`FIELD_NAME`,`FIELD_TYPE`,`DEFAULT_VALUE`,`ACTIVE`,`WL_PROGRAM_ID`,`CAN_BE_ADHOC`) VALUES (19,'Support level','select_one',NULL,1,2,0);
INSERT INTO `criteria_type` (`CRITERIA_TYPE_ID`,`FIELD_NAME`,`FIELD_TYPE`,`DEFAULT_VALUE`,`ACTIVE`,`WL_PROGRAM_ID`,`CAN_BE_ADHOC`) VALUES (20,'Geographic location','select_one',NULL,1,2,0);
INSERT INTO `criteria_type` (`CRITERIA_TYPE_ID`,`FIELD_NAME`,`FIELD_TYPE`,`DEFAULT_VALUE`,`ACTIVE`,`WL_PROGRAM_ID`,`CAN_BE_ADHOC`) VALUES (21,'Age category','select_one',NULL,1,2,0);
INSERT INTO `criteria_type` (`CRITERIA_TYPE_ID`,`FIELD_NAME`,`FIELD_TYPE`,`DEFAULT_VALUE`,`ACTIVE`,`WL_PROGRAM_ID`,`CAN_BE_ADHOC`) VALUES (22,'Current involvement with Criminal Justice system','select_one',NULL,1,2,0);
INSERT INTO `criteria_type` (`CRITERIA_TYPE_ID`,`FIELD_NAME`,`FIELD_TYPE`,`DEFAULT_VALUE`,`ACTIVE`,`WL_PROGRAM_ID`,`CAN_BE_ADHOC`) VALUES (23,'SHPPSU criteria','select_one',NULL,1,2,0);
INSERT INTO `criteria_type` (`CRITERIA_TYPE_ID`,`FIELD_NAME`,`FIELD_TYPE`,`DEFAULT_VALUE`,`ACTIVE`,`WL_PROGRAM_ID`,`CAN_BE_ADHOC`) VALUES (24,'Accessible unit','select_one',NULL,1,2,0);

/*
-- Query: SELECT * FROM test.criteria_type_option
LIMIT 0, 1000

-- Date: 2012-04-14 15:35
*/
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (1,2,1,'Youth – 14 – 22',NULL,14,22);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (2,2,2,'Youth  - 16-24',NULL,16,24);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (3,3,1,'North York','North York',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (4,3,2,'Scarborough','Scarborough',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (5,3,3,'East York','East York',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (6,3,4,'Old City of York','Old City of York',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (7,3,5,'North Etobicoke','North Etobicoke',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (8,3,6,'South Etobicoke','South Etobicoke',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (9,3,7,'Downtown Toronto','Downtown Toronto',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (10,3,8,'East of Yonge','East of Yonge',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (11,3,9,'West of Yonge','West of Yonge',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (12,3,10,'Toronto','Toronto',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (13,2,3,'16 Years of age or older',NULL,16,120);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (14,2,4,'18 years of age or older',NULL,18,120);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (15,4,1,'Formal Diagnosis','Formal Diagnosis',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (16,4,2,'No formal Diagnosis','No formal Diagnosis',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (19,5,1,'Test diagnosis 1','Test diagnosis 1',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (20,5,2,'Test diagnosis 2','Test diagnosis 2',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (21,7,1,'Long-Term Case Management','Long-Term Case Management',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (22,7,2,'Short-term case management','Short-term case management',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (23,7,3,'Emergency Department Diversion Program','Emergency Department Diversion Program',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (24,7,4,'Assertive Community Treatment Team','Assertive Community Treatment Team',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (25,7,5,'Mental Health Outreach Program','Mental Health Outreach Program',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (26,7,6,'Language Specific Service (Across Boundaries, CRCT, WRAP, Pathways, Passages)','Language Specific Service',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (27,7,7,'Youth Programs','Youth Programs',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (28,7,8,'Early Intervention Programs','Early Intervention Programs',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (29,7,9,'Mental Health Prevention Program (short-term case management)','Mental Health Prevention Program (short-term case management)',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (30,7,10,'Seniors Case Management','Seniors Case Management',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (31,7,11,'TCAT (Addictions case management)','TCAT (Addictions case management)',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (32,7,12,'CATCH','CATCH',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (33,7,13,'CATCH - ED','CATCH - ED',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (34,1,1,'Across Boundaries','Across Boundaries',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (35,1,2,'Bayview Community Services','Bayview Community Services',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (36,8,1,'Organizational Referral Source','Organizational Referral Source',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (37,8,2,'Accredited Professional (i.e. private psychiatrist, family doctor etc)','Accredited Professional',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (38,8,3,' Self',' Self',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (39,8,4,'Family/Friend','Family/Friend',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (40,8,5,'Hospital (List of all hospitals)','Hospital',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (41,8,6,'Ontario Review Board','Ontario Review Board',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (42,8,7,'Alternative Access Route (i.e. internal referral, pre-existing agreement, alternate access route)','Alternative Access Route',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (43,9,1,'Test legal history 1','Test legal history 1',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (44,9,2,'Test legal history 2','Test legal history 2',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (45,10,1,'Housed','Housed',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (46,10,2,'Homeless','Homeless',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (47,10,3,'Transitional','Transitional',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (48,11,1,'Concurrent Disorder','Concurrent Disorder',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (49,11,2,'Dual Diagnosis','Dual Diagnosis',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (50,11,3,'Acquired brain injury','Acquired brain injury',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (51,11,4,'Psycho-geriatric  issues','Psycho-geriatric  issues',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (52,12,1,'English','English',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (53,12,2,'French','French',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (54,12,3,'Other','Other',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (55,13,1,'Male','Male',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (56,13,2,'Female','Female',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (57,14,1,'Male','Male',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (58,14,2,'Female','Female',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (59,15,1,'Homeless','Homeless',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (60,15,2,'At risk','At risk',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (61,15,3,'Housed','Housed',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (62,16,1,'Formal Diagnosis','Formal Diagnosis',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (63,16,2,'No formal Diagnosis','No formal Diagnosis',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (65,17,1,'Shared','Shared',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (66,17,2,'Independent','Independent',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (67,18,1,'Organizational Referral Source','Organizational Referral Source',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (68,18,2,'Accredited Professional (i.e. private psychiatrist, family doctor etc)','Accredited Professional',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (69,19,1,'Test level 1','Test level 1',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (70,19,2,'Test level 2','Test level 2',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (75,22,1,'Test involvement 1','Test involvement 1',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (76,22,2,'Test involvement 2','Test involvement 2',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (77,23,1,'Test SHPPSU criteria 1','Test SHPPSU criteria 1',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (78,23,2,'Test SHPPSU criteria 2','Test SHPPSU criteria 2',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (79,24,1,'Accessible unit required','Accessible unit required',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (80,24,2,'Accessible unit not required','Accessible unit not required',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (82,1,3,'CMHA (Toronto East)','CMHA (Toronto East)',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (83,1,4,'CMHA (Toronto West)','CMHA (Toronto West)',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (84,1,5,'COTA Health','COTA Health',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (85,1,6,'Community Resource Connections of Toronto','Community Resource Connections of Toronto',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (86,1,7,'Griffin Centre & Community Support Network','Griffin Centre & Community Support Network',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (87,1,8,'North York General Hospital','North York General Hospital',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (88,1,9,'Reconnect Mental Health Services','Reconnect Mental Health Services',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (89,1,10,'Saint Elizabeth Health Care','Saint Elizabeth Health Care',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (90,1,11,'Scarborough Hospital','Scarborough Hospital',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (91,1,12,'Sunnybrook Hospital','Sunnybrook Hospital',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (92,1,13,'Toronto North Support Services','Toronto North Support Services',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (93,13,3,'Transgender','Transgender',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (94,13,4,'Transsexual','Transsexual',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (95,13,5,'Other','Other',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (96,14,3,'Transgender','Transgender',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (97,14,4,'Transsexual','Transsexual',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (98,14,5,'Other','Other',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (99,18,3,' Self',' Self',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (100,18,4,'Family/Friend','Family/Friend',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (101,18,5,'Hospital (List of all hospitals)','Hospital',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (102,18,6,'Ontario Review Board','Ontario Review Board',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (103,18,7,'Alternative Access Route (i.e. internal referral, pre-existing agreement, alternate access route)','Alternative Access Route',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (104,20,1,'West End of Toronto (Bathurst to Islington, Lawrence to Lakeshore) ','West End of Toronto',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (105,20,1,'East End of Toronto (Don Valley to Victoria Park, Lawrence to Lakeshore)','East End of Toronto',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (106,20,1,'Downtown Core of Toronto (Bathurst to Don Valley, Lawrence to Lakeshore)','Downtown Core of Toronto',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (107,20,1,'North York East (North of Lawrence, East of Yonge to Victoria Park) ','North York East',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (108,20,1,'North York West (North of Lawrence, West of Yonge to Islington)','North York West',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (109,20,1,'Etobicoke (West of Islington) ','Etobicoke',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (110,20,1,'Scarborough (East of Victoria Park)','Scarborough',NULL,NULL);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (111,21,1,'Youth – 14 – 22',NULL,14,22);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (112,21,2,'Youth  - 16-24',NULL,16,24);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (113,21,3,'16 Years of age or older',NULL,16,120);
INSERT INTO `criteria_type_option` (`OPTION_ID`,`CRITERIA_TYPE_ID`,`DISPLAY_ORDER_NUMBER`,`OPTION_LABEL`,`OPTION_VALUE`,`RANGE_START_VALUE`,`RANGE_END_VALUE`) VALUES (114,21,4,'18 years of age or older',NULL,18,120);
