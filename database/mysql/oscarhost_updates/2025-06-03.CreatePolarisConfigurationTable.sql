CREATE TABLE IF NOT EXISTS polaris_configuration
(
    id              INT AUTO_INCREMENT,
    vendor_name     VARCHAR(255) NOT NULL,
    product_name    VARCHAR(255) NOT NULL,
    product_version VARCHAR(255),
    type            VARCHAR(255) NOT NULL,
    organization_id CHAR(36),
    PRIMARY KEY (id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;

INSERT INTO polaris_configuration (vendor_name, product_name, type)
SELECT 'WELLSTAR-OSCAR EMR', 'Juno EMR', 'software'
WHERE NOT EXISTS (
    SELECT 1 FROM polaris_configuration
);
