BEGIN;

UPDATE indicatorTemplate SET template = '<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<indicatorTemplateXML>\n	<author>OSCAR BC</author>\n	<uid></uid>\n	<heading>\n		<category>General</category>\n		<subCategory>Patient Population</subCategory>\n		<name>Aggregated Patient Contact</name>\n		<definition># of active patients with contact information in comparison to # all active patients\n		</definition>\n		<framework>DoBC CPQI PSP Panel</framework>\n		<frameworkVersion>08-02-2017</frameworkVersion>\n		<notes>\n		  i)  # of active patients without contact information\n		  ii) # of active patients with contact information\n		</notes>\n	</heading>\n	<indicatorQuery>\n		<version>03-13-2018</version>\n		<params>\n			<!-- \n				Use this parameter in the query as ${provider}\n				This parameter should be used for fetching patient\'s assigned to a MRP.\n				ie: WHERE demographic.provider_no = ${provider}\n			-->\n			<parameter id=\"provider\" name=\"provider_no\" value=\"loggedInProvider\" />\n			<parameter id=\"pstatus\" name=\"Patient Status\" value=\"\'AC\'\" />\n		</params>\n		<query>\n		  <!-- Indicator SQL Query here -->\nSELECT\n	SUM(fin.NO_CONTACT_INFO) AS \"% Active, No Contact Information\",\n	SUM(fin.HAS_CONTACT_INFO) AS \"% Active, With Contact Information\"\nFROM (\n	SELECT\n IF( (LENGTH(d.phone) &lt;= 3 OR d.phone IS NULL) AND (LENGTH(d.phone2) &lt;= 3 OR d.phone2 IS NULL) AND (LENGTH(de_cell.value) &lt;= 3 OR de_cell.value IS NULL), 1, 0) AS NO_CONTACT_INFO,		IF( LENGTH(d.phone) &gt; 3 OR LENGTH(d.phone2) &gt; 3 OR LENGTH(de_cell.value) &gt; 3, 1, 0) AS HAS_CONTACT_INFO\n	FROM demographic d\n LEFT JOIN demographicExt de_cell ON (d.demographic_no = de_cell.demographic_no AND de_cell.key_val = "demo_cell")\n	WHERE d.provider_no LIKE ${provider}\n	AND d.patient_status LIKE ${pstatus}\n) fin;\n		</query>\n	</indicatorQuery>\n	<drillDownQuery>\n		<version>03-13-2018</version>\n		<params>\n			<parameter id=\"provider\" name=\"provider_no\" value=\"loggedInProvider\" />	\n			<parameter id=\"pstatus\" name=\"Patient Status\" value=\"\'AC\'\" />\n		</params>\n		<displayColumns>\n		        <column id=\"noContact\" name=\"SUM(fin.NO_CONTACT_INFO)\" title=\"Active Without Contact Info\" primary=\"false\" />\n		  	<column id=\"allActive\" name=\"SUM(fin.ALLACTIVE)\" title=\"Active Patient Total\" primary=\"false\" />\n		</displayColumns>\n		<exportColumns>\n		        <column id=\"noContact\" name=\"SUM(fin.NO_CONTACT_INFO)\" title=\"Active Without Contact Info\" primary=\"false\" />\n		  	<column id=\"allActive\" name=\"SUM(fin.ALLACTIVE)\" title=\"Active Patient Total\" primary=\"false\" />\n		</exportColumns>\n		<query>\n			<!-- Drilldown SQL Query here -->\nSELECT\nfin.*\nFROM (\n	SELECT\n IF( ((LENGTH(d.phone) &lt;= 3 OR d.phone IS NULL) AND (LENGTH(d.phone2) &lt;= 3 OR d.phone2 IS NULL) AND (LENGTH(de_cell.value) &lt;= 3 OR de_cell.value IS NULL)), 1, 0) AS NO_CONTACT_INFO,\n		IF( true, 1, 0 ) AS ALLACTIVE\n	FROM demographic d JOIN demographicExt de_cell ON (d.demographic_no = de_cell.demographic_no AND de.key_val = "demo_cell") \n	WHERE d.provider_no LIKE ${provider}\n	AND d.patient_status LIKE ${pstatus}\n) fin\n		</query>\n	</drillDownQuery>\n</indicatorTemplateXML>\n'
WHERE id = 63 AND dashboardID = 7 AND name = "Aggregated Patient Contact";

UPDATE indicatorTemplate SET template = '<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<indicatorTemplateXML>\n	<author>OSCAR BC</author>\n	<uid></uid>\n	<heading>\n		<category>General</category>\n		<subCategory>Patient Population</subCategory>\n		<name>Patient Contact</name>\n		<definition># of active patients with contact information in comparison to # all active patients\n		</definition>\n		<framework>DoBC CPQI PSP Panel</framework>\n		<frameworkVersion>08-02-2017</frameworkVersion>\n		<notes>\n		  i)  # of active patients without contact information\n		  ii) # of active patients with contact information\n		</notes>\n	</heading>\n	<indicatorQuery>\n		<version>03-13-2018</version>\n		<params>\n			<!-- \n				Use this parameter in the query as ${provider}\n				This parameter should be used for fetching patient\'s assigned to a MRP.\n				ie: WHERE demographic.provider_no = ${provider}\n			-->\n			<parameter id=\"provider\" name=\"provider_no\" value=\"loggedInProvider\" />\n			<parameter id=\"pstatus\" name=\"Patient Status\" value=\"\'AC\'\" />\n		</params>\n		<query>\n		  <!-- Indicator SQL Query here -->\nSELECT\n	SUM(fin.NO_CONTACT_INFO) AS \"% Active, No Contact Information\",\n	SUM(fin.HAS_CONTACT_INFO) AS \"% Active, With Contact Information\"\nFROM (\n	SELECT\n IF( ((LENGTH(d.phone) &lt;= 3 OR d.phone IS NULL) AND (LENGTH(d.phone2) &lt;= 3 OR d.phone2 IS NULL) AND (LENGTH(de_cell.value) &lt;= 3 OR de_cell.value IS NULL)), 1, 0) AS NO_CONTACT_INFO,		IF( LENGTH(d.phone) &gt; 3 OR LENGTH(d.phone2) &gt; 3 OR LENGTH(de_cell.value) &gt; 3, 1, 0) AS HAS_CONTACT_INFO\n	FROM demographic d\n LEFT JOIN demographicExt de_cell ON (d.demographic_no = de_cell.demographic_no AND de_cell.key_val = "demo_cell")\n	WHERE d.provider_no LIKE ${provider}\n	AND d.patient_status LIKE ${pstatus}\n) fin;\n		</query>\n	</indicatorQuery>\n	<drillDownQuery>\n		<version>03-13-2018</version>\n		<params>\n			<parameter id=\"provider\" name=\"provider_no\" value=\"loggedInProvider\" />	\n			<parameter id=\"pstatus\" name=\"Patient Status\" value=\"\'AC\'\" />\n		</params>\n		<displayColumns>\n			<column id=\"demographic\" name=\"d.demographic_no\" title=\"Patient Id\" primary=\"true\" />\n			<column id=\"name\" name=\"CONCAT( d.last_name, \', \', d.first_name )\" title=\"Patient Name\" primary=\"false\" />\n			<column id=\"dob\" name=\"DATE_FORMAT( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth), \'%m-%d-%Y\' )\" title=\"Date of Birth (mm-dd-yy)\" primary=\"false\" />\n			<column id=\"age\" name=\"TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )\" title=\"Age\" primary=\"false\" />\n			<column id=\"phone\" name=\"d.phone\" title=\"Phone#\" primary=\"false\" />\n\n		</displayColumns>\n		<exportColumns>\n		        <column id=\"demographic\" name=\"d.demographic_no\" title=\"Patient Id\" primary=\"true\" />\n		  	<column id=\"firstName\" name=\"d.first_name\" title=\"First Name\" primary=\"false\" />\n                        <column id=\"lastName\" name=\"d.last_name\" title=\"Last Name\" primary=\"false\" />\n			<column id=\"dob\" name=\"DATE_FORMAT( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth), \'%m-%d-%Y\' )\" title=\"Date of Birth (mm-dd-yy)\" primary=\"false\" />\n			<column id=\"age\" name=\"TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )\" title=\"Age\" primary=\"false\" />\n			<column id=\"phone\" name=\"d.phone\" title=\"Phone#\" primary=\"false\" />\n\n		</exportColumns>\n		<query>\n			<!-- Drilldown SQL Query here -->\nSELECT\nd.*\nFROM demographic d\nLEFT JOIN demographicExt de_cell ON (d.demographic_no = de_cell.demographic_no AND de_cell.key_val = "demo_cell") \nWHERE d.provider_no LIKE ${provider}\nAND d.patient_status LIKE ${pstatus}\nAND (   (LENGTH(phone) &lt;= 3 OR phone IS NULL) AND (LENGTH(phone2) &lt;= 3 OR phone2 IS NULL) AND (de_cell.value &lt;= 3 OR de_cell.value IS NULL)) );\n		</query>\n	</drillDownQuery>\n</indicatorTemplateXML>\n'
WHERE id = 65 AND dashboardID = 7 AND name = "Patient Contact";

COMMIT;
