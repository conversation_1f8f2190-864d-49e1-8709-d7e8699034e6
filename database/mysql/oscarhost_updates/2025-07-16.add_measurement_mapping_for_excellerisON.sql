ALTER TABLE measurementMap MODIFY COLUMN lab_type varchar(15) NOT NULL;

BEGIN;

INSERT INTO measurementMap (loinc_code, ident_code, name, lab_type)
SELECT
    m.loinc_code,
    m.loinc_code as ident_code,
    m.name,
    'ExcellerisON' as lab_type
FROM measurementMap m
WHERE m.lab_type = 'MDS'
AND NOT EXISTS (
    SELECT 1
    FROM measurementMap m2
    WHERE m2.loinc_code = m.loinc_code
    AND m2.lab_type = 'ExcellerisON'
);

COMMIT;