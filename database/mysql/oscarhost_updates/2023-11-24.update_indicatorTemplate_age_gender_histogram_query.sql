BEGIN;

UPDATE indicatorTemplate SET template = '<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<indicatorTemplateXML>\n	<author>OSCAR BC</author>\n	<uid></uid>\n	<heading>\n		<category>General</category>\n		<subCategory>Patient Population Histogram</subCategory>\n		<name>Age and Gender Histogram Report</name>\n		<definition>Age and Gender Histogram report (5 year intervals)</definition>\n		<framework>DoBC CPQI PSP Panel</framework>\n		<frameworkVersion>08-02-2017</frameworkVersion>\n		<notes>\n		  Age and Gender Histogram report (5 year intervals)\n		</notes>\n	</heading>\n	<indicatorQuery>\n		<version>03-13-2018</version>\n		<params>\n			<!-- \n				Use this parameter in the query as ${provider}\n				This parameter should be used for fetching patient\'s assigned to a MRP.\n				ie: WHERE demographic.provider_no = ${provider}\n			-->\n			<parameter id=\"provider\" name=\"provider_no\" value=\"loggedInProvider\" />\n			<parameter id=\"pstatus\" name=\"Patient Status\" value=\"\'AC\'\" />\n		</params>\n		<query>\n		  <!-- Indicator SQL Query here -->\nSELECT\n	IF( COUNT(fin.patient) &gt; 0, SUM( CASE WHEN (fin.gender = \'M\' AND (0 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(fin.year,\'-\',fin.month,\'-\',fin.day) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(fin.year,\'-\',fin.month,\'-\',fin.day) ), CURDATE() ) &lt; 20 )) THEN 1 ELSE 0 END) , 0) AS \"M0-19\",\n	IF( COUNT(fin.patient) &gt; 0, SUM( CASE WHEN (fin.gender = \'M\' AND (20 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(fin.year,\'-\',fin.month,\'-\',fin.day) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(fin.year,\'-\',fin.month,\'-\',fin.day) ), CURDATE() ) &lt; 60 )) THEN 1 ELSE 0 END) , 0) AS \"M20-59\",\n	IF( COUNT(fin.patient) &gt; 0, SUM( CASE WHEN (fin.gender = \'M\' AND (60 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(fin.year,\'-\',fin.month,\'-\',fin.day) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(fin.year,\'-\',fin.month,\'-\',fin.day) ), CURDATE() ) &lt; 999 )) THEN 1 ELSE 0 END) , 0) AS \"M60+\",\n	IF( COUNT(fin.patient) &gt; 0, SUM( CASE WHEN (fin.gender = \'F\' AND (0 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(fin.year,\'-\',fin.month,\'-\',fin.day) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(fin.year,\'-\',fin.month,\'-\',fin.day) ), CURDATE() ) &lt; 20 )) THEN 1 ELSE 0 END) , 0) AS \"F0-19\",\n	IF( COUNT(fin.patient) &gt; 0, SUM( CASE WHEN (fin.gender = \'F\' AND (20 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(fin.year,\'-\',fin.month,\'-\',fin.day) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(fin.year,\'-\',fin.month,\'-\',fin.day) ), CURDATE() ) &lt; 60 )) THEN 1 ELSE 0 END) , 0) AS \"F20-59\",\n	IF( COUNT(fin.patient) &gt; 0, SUM( CASE WHEN (fin.gender = \'F\' AND (60 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(fin.year,\'-\',fin.month,\'-\',fin.day) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(fin.year,\'-\',fin.month,\'-\',fin.day) ), CURDATE() ) &lt; 999 )) THEN 1 ELSE 0 END) , 0) AS \"F60+\"\nFROM (\nSELECT\nd.demographic_no as patient, d.year_of_birth as year, d.month_of_birth as month, d.date_of_birth as day, d.sex as gender\nFROM\ndemographic d\nWHERE d.provider_no LIKE ${provider}\nAND d.patient_status LIKE ${pstatus}\n) fin;\n		</query>\n	</indicatorQuery>\n	<drillDownQuery>\n		<version>03-13-2018</version>\n		<params>\n			<parameter id=\"provider\" name=\"provider_no\" value=\"loggedInProvider\" />	\n			<parameter id=\"pstatus\" name=\"Patient Status\" value=\"\'AC\'\" />\n		</params>\n		<displayColumns>\n			<column id=\'M0-4\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (0 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 5 )) THEN 1 ELSE 0 END) , 0)\" title=\"M0-4\" primary=\"false\" />\n			<column id=\'M5-9\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (5 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 10 )) THEN 1 ELSE 0 END) , 0)\" title=\"M5-9\" primary=\"false\" />\n			<column id=\'M10-14\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (10 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 15 )) THEN 1 ELSE 0 END) , 0)\" title=\"M10-14\" primary=\"false\" />\n			<column id=\'M15-20\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (15 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 20 )) THEN 1 ELSE 0 END) , 0)\" title=\"M15-19\" primary=\"false\" />\n			<column id=\'M20-24\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (20 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 25 )) THEN 1 ELSE 0 END) , 0)\" title=\"M20-24\" primary=\"false\" />\n			<column id=\'M25-29\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (25 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 30 )) THEN 1 ELSE 0 END) , 0)\" title=\"M25-29\" primary=\"false\" />\n			<column id=\'M30-34\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (30 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 35 )) THEN 1 ELSE 0 END) , 0)\" title=\"M30-34\" primary=\"false\" />\n			<column id=\'M35-39\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (35 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 40 )) THEN 1 ELSE 0 END) , 0)\" title=\"M35-39\" primary=\"false\" />\n			<column id=\'M40-44\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (40 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 45 )) THEN 1 ELSE 0 END) , 0)\" title=\"M40-44\" primary=\"false\" />\n			<column id=\'M45-49\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (45 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 50 )) THEN 1 ELSE 0 END) , 0)\" title=\"M45-49\" primary=\"false\" />\n			<column id=\'M50-54\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (50 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 55 )) THEN 1 ELSE 0 END) , 0)\" title=\"M50-54\" primary=\"false\" />\n			<column id=\'M55-59\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (55 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 60 )) THEN 1 ELSE 0 END) , 0)\" title=\"M55-59\" primary=\"false\" />\n			<column id=\'M60-64\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (60 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 65 )) THEN 1 ELSE 0 END) , 0)\" title=\"M60-64\" primary=\"false\" />\n			<column id=\'M65-69\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (65 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 70 )) THEN 1 ELSE 0 END) , 0)\" title=\"M65-69\" primary=\"false\" />\n			<column id=\'M70-74\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (70 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 75 )) THEN 1 ELSE 0 END) , 0)\" title=\"M70-74\" primary=\"false\" />\n			<column id=\'M75-79\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (75 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 80 )) THEN 1 ELSE 0 END) , 0)\" title=\"M75-79\" primary=\"false\" />\n			<column id=\'M80-84\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (80 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 85 )) THEN 1 ELSE 0 END) , 0)\" title=\"M80-84\" primary=\"false\" />\n			<column id=\'M85-89\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (85 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 90 )) THEN 1 ELSE 0 END) , 0)\" title=\"M85-89\" primary=\"false\" />\n			<column id=\'M90-94\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (90 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 95 )) THEN 1 ELSE 0 END) , 0)\" title=\"M90-94\" primary=\"false\" />\n			<column id=\'M95-99\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (95 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 100 )) THEN 1 ELSE 0 END) , 0)\" title=\"M95-99\" primary=\"false\" />\n			<column id=\'M100-104\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (100 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 105 )) THEN 1 ELSE 0 END) , 0)\" title=\"M100-104\" primary=\"false\" />\n			<column id=\'M105-109\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (105 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 110 )) THEN 1 ELSE 0 END) , 0)\" title=\"M105-109\" primary=\"false\" />\n			<column id=\'M110-114\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (110 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 115 )) THEN 1 ELSE 0 END) , 0)\" title=\"M110-114\" primary=\"false\" />\n			<column id=\'M115-119\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (115 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 120 )) THEN 1 ELSE 0 END) , 0)\" title=\"M115-119\" primary=\"false\" />\n			<column id=\'M120-124\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (120 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 125 )) THEN 1 ELSE 0 END) , 0)\" title=\"M120-124\" primary=\"false\" />\n			<column id=\'M125+\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (125 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 999 )) THEN 1 ELSE 0 END) , 0)\" title=\"M125+\" primary=\"false\" />\n			\n			<column id=\'F0-4\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (0 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 5 )) THEN 1 ELSE 0 END) , 0)\" title=\"F0-4\" primary=\"false\" />\n			<column id=\'F5-9\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (5 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 10 )) THEN 1 ELSE 0 END) , 0)\" title=\"F5-9\" primary=\"false\" />\n			<column id=\'F10-14\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (10 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 15 )) THEN 1 ELSE 0 END) , 0)\" title=\"F10-14\" primary=\"false\" />\n			<column id=\'F15-20\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (15 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 20 )) THEN 1 ELSE 0 END) , 0)\" title=\"F15-19\" primary=\"false\" />\n			<column id=\'F20-24\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (20 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 25 )) THEN 1 ELSE 0 END) , 0)\" title=\"F20-24\" primary=\"false\" />\n			<column id=\'F25-29\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (25 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 30 )) THEN 1 ELSE 0 END) , 0)\" title=\"F25-29\" primary=\"false\" />\n			<column id=\'F30-34\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (30 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 35 )) THEN 1 ELSE 0 END) , 0)\" title=\"F30-34\" primary=\"false\" />\n			<column id=\'F35-39\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (35 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 40 )) THEN 1 ELSE 0 END) , 0)\" title=\"F35-39\" primary=\"false\" />\n			<column id=\'F40-44\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (40 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 45 )) THEN 1 ELSE 0 END) , 0)\" title=\"F40-44\" primary=\"false\" />\n			<column id=\'F45-49\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (45 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 50 )) THEN 1 ELSE 0 END) , 0)\" title=\"F45-49\" primary=\"false\" />\n			<column id=\'F50-54\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (50 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 55 )) THEN 1 ELSE 0 END) , 0)\" title=\"F50-54\" primary=\"false\" />\n			<column id=\'F55-59\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (55 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 60 )) THEN 1 ELSE 0 END) , 0)\" title=\"F55-59\" primary=\"false\" />\n			<column id=\'F60-64\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (60 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 65 )) THEN 1 ELSE 0 END) , 0)\" title=\"F60-64\" primary=\"false\" />\n			<column id=\'F65-69\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (65 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 70 )) THEN 1 ELSE 0 END) , 0)\" title=\"F65-69\" primary=\"false\" />\n			<column id=\'F70-74\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (70 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 75 )) THEN 1 ELSE 0 END) , 0)\" title=\"F70-74\" primary=\"false\" />\n			<column id=\'F75-79\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (75 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 80 )) THEN 1 ELSE 0 END) , 0)\" title=\"F75-79\" primary=\"false\" />\n			<column id=\'F80-84\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (80 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 85 )) THEN 1 ELSE 0 END) , 0)\" title=\"F80-84\" primary=\"false\" />\n			<column id=\'F85-89\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (85 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 90 )) THEN 1 ELSE 0 END) , 0)\" title=\"F85-89\" primary=\"false\" />\n			<column id=\'F90-94\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (90 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 95 )) THEN 1 ELSE 0 END) , 0)\" title=\"F90-94\" primary=\"false\" />\n			<column id=\'F95-99\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (95 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 100 )) THEN 1 ELSE 0 END) , 0)\" title=\"F95-99\" primary=\"false\" />\n			<column id=\'F100-104\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (100 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 105 )) THEN 1 ELSE 0 END) , 0)\" title=\"F100-104\" primary=\"false\" />\n			<column id=\'F105-109\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (105 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 110 )) THEN 1 ELSE 0 END) , 0)\" title=\"F105-109\" primary=\"false\" />\n			<column id=\'F110-114\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (110 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 115 )) THEN 1 ELSE 0 END) , 0)\" title=\"F110-114\" primary=\"false\" />\n			<column id=\'F115-119\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (115 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 120 )) THEN 1 ELSE 0 END) , 0)\" title=\"F115-119\" primary=\"false\" />\n			<column id=\'F120-124\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (120 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 125 )) THEN 1 ELSE 0 END) , 0)\" title=\"F120-124\" primary=\"false\" />\n			<column id=\'F125+\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (125 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 999 )) THEN 1 ELSE 0 END) , 0)\" title=\"F125+\" primary=\"false\" />\n			\n		</displayColumns>\n		<exportColumns>\n			<column id=\'M0-4\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (0 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 5 )) THEN 1 ELSE 0 END) , 0)\" title=\"M0-4\" primary=\"false\" />\n			<column id=\'M5-9\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (5 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 10 )) THEN 1 ELSE 0 END) , 0)\" title=\"M5-9\" primary=\"false\" />\n			<column id=\'M10-14\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (10 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 15 )) THEN 1 ELSE 0 END) , 0)\" title=\"M10-14\" primary=\"false\" />\n			<column id=\'M15-20\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (15 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 20 )) THEN 1 ELSE 0 END) , 0)\" title=\"M15-19\" primary=\"false\" />\n			<column id=\'M20-24\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (20 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 25 )) THEN 1 ELSE 0 END) , 0)\" title=\"M20-24\" primary=\"false\" />\n			<column id=\'M25-29\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (25 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 30 )) THEN 1 ELSE 0 END) , 0)\" title=\"M25-29\" primary=\"false\" />\n			<column id=\'M30-34\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (30 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 35 )) THEN 1 ELSE 0 END) , 0)\" title=\"M30-34\" primary=\"false\" />\n			<column id=\'M35-39\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (35 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 40 )) THEN 1 ELSE 0 END) , 0)\" title=\"M35-39\" primary=\"false\" />\n			<column id=\'M40-44\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (40 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 45 )) THEN 1 ELSE 0 END) , 0)\" title=\"M40-44\" primary=\"false\" />\n			<column id=\'M45-49\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (45 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 50 )) THEN 1 ELSE 0 END) , 0)\" title=\"M45-49\" primary=\"false\" />\n			<column id=\'M50-54\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (50 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 55 )) THEN 1 ELSE 0 END) , 0)\" title=\"M50-54\" primary=\"false\" />\n			<column id=\'M55-59\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (55 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 60 )) THEN 1 ELSE 0 END) , 0)\" title=\"M55-59\" primary=\"false\" />\n			<column id=\'M60-64\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (60 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 65 )) THEN 1 ELSE 0 END) , 0)\" title=\"M60-64\" primary=\"false\" />\n			<column id=\'M65-69\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (65 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 70 )) THEN 1 ELSE 0 END) , 0)\" title=\"M65-69\" primary=\"false\" />\n			<column id=\'M70-74\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (70 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 75 )) THEN 1 ELSE 0 END) , 0)\" title=\"M70-74\" primary=\"false\" />\n			<column id=\'M75-79\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (75 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 80 )) THEN 1 ELSE 0 END) , 0)\" title=\"M75-79\" primary=\"false\" />\n			<column id=\'M80-84\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (80 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 85 )) THEN 1 ELSE 0 END) , 0)\" title=\"M80-84\" primary=\"false\" />\n			<column id=\'M85-89\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (85 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 90 )) THEN 1 ELSE 0 END) , 0)\" title=\"M85-89\" primary=\"false\" />\n			<column id=\'M90-94\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (90 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 95 )) THEN 1 ELSE 0 END) , 0)\" title=\"M90-94\" primary=\"false\" />\n			<column id=\'M95-99\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (95 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 100 )) THEN 1 ELSE 0 END) , 0)\" title=\"M95-99\" primary=\"false\" />\n			<column id=\'M100-104\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (100 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 105 )) THEN 1 ELSE 0 END) , 0)\" title=\"M100-104\" primary=\"false\" />\n			<column id=\'M105-109\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (105 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 110 )) THEN 1 ELSE 0 END) , 0)\" title=\"M105-109\" primary=\"false\" />\n			<column id=\'M110-114\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (110 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 115 )) THEN 1 ELSE 0 END) , 0)\" title=\"M110-114\" primary=\"false\" />\n			<column id=\'M115-119\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (115 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 120 )) THEN 1 ELSE 0 END) , 0)\" title=\"M115-119\" primary=\"false\" />\n			<column id=\'M120-124\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (120 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 125 )) THEN 1 ELSE 0 END) , 0)\" title=\"M120-124\" primary=\"false\" />\n			<column id=\'M125+\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'M\' AND (125 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 999 )) THEN 1 ELSE 0 END) , 0)\" title=\"M125+\" primary=\"false\" />\n			\n			<column id=\'F0-4\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (0 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 5 )) THEN 1 ELSE 0 END) , 0)\" title=\"F0-4\" primary=\"false\" />\n			<column id=\'F5-9\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (5 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 10 )) THEN 1 ELSE 0 END) , 0)\" title=\"F5-9\" primary=\"false\" />\n			<column id=\'F10-14\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (10 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 15 )) THEN 1 ELSE 0 END) , 0)\" title=\"F10-14\" primary=\"false\" />\n			<column id=\'F15-20\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (15 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 20 )) THEN 1 ELSE 0 END) , 0)\" title=\"F15-19\" primary=\"false\" />\n			<column id=\'F20-24\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (20 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 25 )) THEN 1 ELSE 0 END) , 0)\" title=\"F20-24\" primary=\"false\" />\n			<column id=\'F25-29\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (25 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 30 )) THEN 1 ELSE 0 END) , 0)\" title=\"F25-29\" primary=\"false\" />\n			<column id=\'F30-34\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (30 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 35 )) THEN 1 ELSE 0 END) , 0)\" title=\"F30-34\" primary=\"false\" />\n			<column id=\'F35-39\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (35 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 40 )) THEN 1 ELSE 0 END) , 0)\" title=\"F35-39\" primary=\"false\" />\n			<column id=\'F40-44\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (40 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 45 )) THEN 1 ELSE 0 END) , 0)\" title=\"F40-44\" primary=\"false\" />\n			<column id=\'F45-49\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (45 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 50 )) THEN 1 ELSE 0 END) , 0)\" title=\"F45-49\" primary=\"false\" />\n			<column id=\'F50-54\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (50 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 55 )) THEN 1 ELSE 0 END) , 0)\" title=\"F50-54\" primary=\"false\" />\n			<column id=\'F55-59\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (55 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 60 )) THEN 1 ELSE 0 END) , 0)\" title=\"F55-59\" primary=\"false\" />\n			<column id=\'F60-64\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (60 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 65 )) THEN 1 ELSE 0 END) , 0)\" title=\"F60-64\" primary=\"false\" />\n			<column id=\'F65-69\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (65 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 70 )) THEN 1 ELSE 0 END) , 0)\" title=\"F65-69\" primary=\"false\" />\n			<column id=\'F70-74\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (70 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 75 )) THEN 1 ELSE 0 END) , 0)\" title=\"F70-74\" primary=\"false\" />\n			<column id=\'F75-79\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (75 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 80 )) THEN 1 ELSE 0 END) , 0)\" title=\"F75-79\" primary=\"false\" />\n			<column id=\'F80-84\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (80 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 85 )) THEN 1 ELSE 0 END) , 0)\" title=\"F80-84\" primary=\"false\" />\n			<column id=\'F85-89\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (85 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 90 )) THEN 1 ELSE 0 END) , 0)\" title=\"F85-89\" primary=\"false\" />\n			<column id=\'F90-94\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (90 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 95 )) THEN 1 ELSE 0 END) , 0)\" title=\"F90-94\" primary=\"false\" />\n			<column id=\'F95-99\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (95 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 100 )) THEN 1 ELSE 0 END) , 0)\" title=\"F95-99\" primary=\"false\" />\n			<column id=\'F100-104\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (100 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 105 )) THEN 1 ELSE 0 END) , 0)\" title=\"F100-104\" primary=\"false\" />\n			<column id=\'F105-109\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (105 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 110 )) THEN 1 ELSE 0 END) , 0)\" title=\"F105-109\" primary=\"false\" />\n			<column id=\'F110-114\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (110 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 115 )) THEN 1 ELSE 0 END) , 0)\" title=\"F110-114\" primary=\"false\" />\n			<column id=\'F115-119\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (115 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 120 )) THEN 1 ELSE 0 END) , 0)\" title=\"F115-119\" primary=\"false\" />\n			<column id=\'F120-124\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (120 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 125 )) THEN 1 ELSE 0 END) , 0)\" title=\"F120-124\" primary=\"false\" />\n			<column id=\'F125+\' name=\"IF( COUNT(d.demographic_no) &gt; 0, SUM( CASE WHEN (d.sex = \'F\' AND (125 &lt;= TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() )) AND (TIMESTAMPDIFF(YEAR, DATE( CONCAT(d.year_of_birth,\'-\',d.month_of_birth,\'-\',d.date_of_birth) ), CURDATE() ) &lt; 999 )) THEN 1 ELSE 0 END) , 0)\" title=\"F125+\" primary=\"false\" />\n		</exportColumns>\n		<query>\n			<!-- Drilldown SQL Query here -->\nSELECT\n    d.*,\n    d.patient_status AS pt_status\nFROM demographic d\nWHERE d.provider_no LIKE ${provider}\nAND d.patient_status LIKE ${pstatus}\n		</query>\n	</drillDownQuery>\n</indicatorTemplateXML>\n'
WHERE id = 60 AND dashboardId = 7;

COMMIT;