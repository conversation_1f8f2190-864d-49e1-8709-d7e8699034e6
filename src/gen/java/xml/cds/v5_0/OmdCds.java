
package xml.cds.v5_0;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element ref="{cds}PatientRecord"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "patientRecord"
})
@XmlRootElement(name = "OmdCds", namespace = "cds")
public class OmdCds {

    @XmlElement(name = "PatientRecord", namespace = "cds", required = true)
    protected PatientRecord patientRecord;

    /**
     * Gets the value of the patientRecord property.
     * 
     * @return
     *     possible object is
     *     {@link PatientRecord }
     *     
     */
    public PatientRecord getPatientRecord() {
        return patientRecord;
    }

    /**
     * Sets the value of the patientRecord property.
     * 
     * @param value
     *     allowed object is
     *     {@link PatientRecord }
     *     
     */
    public void setPatientRecord(PatientRecord value) {
        this.patientRecord = value;
    }

}
