<%@page import="net.sf.json.JSONObject"%>
<%@page import="net.sf.json.JSONArray"%>
<%@page import="net.sf.json.JSONSerializer"%>
<%@page import="org.oscarehr.util.SpringUtils"%>
<%@page import="org.oscarehr.common.dao.ServiceClientDao"%>
<%@page import="org.oscarehr.common.model.ServiceClient"%>
<%@page import="java.util.List"%>

<%
	ServiceClientDao serviceClientDao = SpringUtils.getBean(ServiceClientDao.class);
%>
<%
	boolean success = false;
    String error = "";
	String method = request.getParameter("method");	
	
	JSONObject json = new JSONObject();
	json.put("method", method);

	if ("add".equals(method)) {
		String valNew = request.getParameter("name");
		String uri = request.getParameter("uri");
		
		json.put("name", valNew);
		
		if (valNew.equals("")) {
			success = false;
			error = "Add Failure: Cannot add Client with empty value.";
		} else {
		
			ServiceClient t = serviceClientDao.findByName(valNew);
			if(t != null) {
				success = false;
				error = "Add Failure: Could not add entry to database.Name already being used.";
			} else {
				ServiceClient sc = new ServiceClient();
				sc.setName(valNew);
				sc.setKey(randomString(16,"0123456789abcdefghijklmnopqrstuvwxyz"));
				sc.setSecret(randomString(16,"0123456789abcdefghijklmnopqrstuvwxyz"));
				sc.setUri(uri);
				serviceClientDao.persist(sc);
				
				int j = sc.getId();
				if (j==0) {		
					success = false;
					error = "Add Failure: Could not add entry to database.";
				} else {
					success = true;		
				}
			}
		}
	}
	else if ("delete".equals(method)) {
		String id = request.getParameter("id");
		
		json.put("id", id);
		
		if (id.equals("")) {
			success = false;
			error = "Add Failure: Cannot remove Client with empty id.";
		} else {
			serviceClientDao.remove(Integer.parseInt(id));
			success=true;
		}
	}
	else if("list".equals(method)) {
		List<ServiceClient> clients = serviceClientDao.findAll();
		JSONArray x = JSONArray.fromObject(clients);
		response.setContentType("text/x-json");    
        response.getWriter().print(x.toString());
	}
	else {
		success = false;
		error = "Invalid method supplied.";
	}
	if(!"list".equals(method)) {
	    response.setContentType("text/x-json");    
		
		json.put("success", success);
		json.put("error", error);
		
	    json.write(out);    	
	}
%>

<%!
public String randomString(int length, String chars) {
	    String result = "";
	    for (int i = length; i > 0; --i) 
	    result += chars.charAt((int)(Math.round(Math.random() * (chars.length() - 1))));
	    return result;
	}
	
%>