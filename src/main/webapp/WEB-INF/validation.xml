<?xml version="1.0" encoding="ISO-8859-1" ?>
<!DOCTYPE form-validation PUBLIC
          "-//Apache Software Foundation//DTD Commons Validator Rules Configuration 1.1.3//EN"
          "http://jakarta.apache.org/commons/dtds/validator_1_1_3.dtd">
<form-validation>
	<global>
		<constant>
			<constant-name>currency</constant-name>
			<constant-value>^\d{1,3}(,?\d{1,3})*\.?(\d{1,2})?$</constant-value>
		</constant>
		<constant>
			<constant-name>postalCode</constant-name>
			<constant-value>^\d{5}\d*$</constant-value>
		</constant>
	</global>
	<formset>
		<form name="issueAdminForm">
			<field property="issueAdmin.code" depends="required">
				<arg0 key="issueAdmin.code" />
			</field>
			<field property="issueAdmin.description" depends="required">
				<arg0 key="issueAdmin.description" />
			</field>
			<field property="issueAdmin.role" depends="required">
				<arg0 key="issueAdmin.role" />
			</field>
		</form>
	</formset>
</form-validation>
