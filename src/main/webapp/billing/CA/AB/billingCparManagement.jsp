<%--

    Copyright (c) 2025 WELL EMR Group Inc.
    This software is made available under the terms of the
    GNU General Public License, Version 2, 1991 (GPLv2).
    License details are available via "gnu.org/licenses/gpl-2.0.html".

--%>
<!DOCTYPE html>
<%@ page import="org.oscarehr.integration.polaris.service.CparUiService" %>
<%@ page import="org.oscarehr.util.SpringUtils" %>
<%@ page import="org.oscarehr.integration.polaris.model.CparUiAttributes" %>
<%@ taglib uri="/WEB-INF/security.tld" prefix="security" %>
<%@page import="org.apache.log4j.Logger" %>
<%@ page import="org.oscarehr.util.MiscUtils" %>
<%
	String roleName$ = session.getAttribute("userrole") + "," + session.getAttribute("user");
	CparUiService cparUiService = SpringUtils.getBean(CparUiService.class);
	CparUiAttributes attributes = null;
	Logger logger = MiscUtils.getLogger();
	try {
		attributes = cparUiService.getCparUiAttributes();
	} catch (Exception exception) {
		logger.error("Error retrieving CPAR UI attributes", exception);
	}

	boolean authed = true;
%>
<security:oscarSec roleName="<%=roleName$%>" objectName="_admin.billing,_admin" rights="w"
				   reverse="<%=true%>">
	<%authed = false; %>
	<%response.sendRedirect("../../../../securityError.jsp?type=_admin&type=_admin.billing");%>
</security:oscarSec>
<%
	if (!authed) {
		return;
	}
%>

<%@ taglib uri="/WEB-INF/struts-bean.tld" prefix="bean" %>
<%@ taglib uri="/WEB-INF/struts-html.tld" prefix="html" %>
<script type="text/javascript"
		src="<%= attributes.getComponentUrl() %>"></script>
<html:html locale="true">

	<head>
		<title><bean:message key="admin.admin.billing.cparManagement"/></title>
		<link href="<%=request.getContextPath() %>/css/bootstrap.min.css" rel="stylesheet">
		<script type="text/javascript"
				src="<%= request.getContextPath() %>/js/jquery-1.9.1.min.js"></script>
		<script type="text/javascript"
				src="<%= request.getContextPath() %>/js/jquery-ui-1.10.2.custom.min.js"></script>
		<style>
		</style>

	<body>
	<div style="padding: 10px; margin: 10px;">
		<% if (attributes == null) { %>
		<h3>
			Cpar is not configured correctly
		</h3>
		<% } else { %>
		<cpar-panel-management
				organization-identifier-system="<%= attributes.getOrganizationIdentifierSystem() %>"
				organization-identifier-value="<%= attributes.getOrganizationIdentifierValue() %>"
				date-format="YYYY-MM-DD"
				medplum-base-url="<%= attributes.getMedplumBaseUrl() %>"
				theme="<%= attributes.getTheme() %>"
				medplum-token="<%= attributes.getMedplumAccessToken() %>">
		</cpar-panel-management>
		<% } %>
	</div>
	</body>
</html:html>