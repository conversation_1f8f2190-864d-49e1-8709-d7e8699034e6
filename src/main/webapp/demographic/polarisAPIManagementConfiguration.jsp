<%--

    Copyright (c) 2025 WELL EMR Group Inc.
    This software is made available under the terms of the
    GNU General Public License, Version 2, 1991 (GPLv2).
    License details are available via "gnu.org/licenses/gpl-2.0.html".

--%>
<!DOCTYPE html>
<%@ taglib uri="/WEB-INF/security.tld" prefix="security" %>
<%
    String roleName$ = session.getAttribute("userrole") + "," + session.getAttribute("user");
    boolean authed = true;
%>
<security:oscarSec roleName="<%=roleName$%>" objectName="_demographic,_demographicExport" rights="w"
                   reverse="<%=true%>">
    <%authed = false; %>
    <%response.sendRedirect(request.getContextPath()
            + "/securityError.jsp?type=_demographic&type=_demographicExport");
    %>
</security:oscarSec>
<%
    if (!authed) {
        return;
    }
%>

<%@page import="org.oscarehr.util.SpringUtils" %>
<%@ page import="org.oscarehr.managers.SecurityInfoManager" %>
<%@ page import="org.oscarehr.integration.polaris.service.PolarisConfigurationService" %>
<%@ taglib uri="/WEB-INF/struts-bean.tld" prefix="bean" %>
<%@ taglib uri="/WEB-INF/struts-html.tld" prefix="html" %>
<%@ taglib uri="/WEB-INF/oscar-tag.tld" prefix="oscar" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="ctx" value="${pageContext.request.contextPath}" scope="request"/>

<%
    // Check for SuperAdmin status
    SecurityInfoManager securityInfoManager = SpringUtils.getBean(SecurityInfoManager.class);
    String providerNo = (String) session.getAttribute("user");
    boolean isSuperAdmin = securityInfoManager.isSuperAdmin(providerNo);

    if (!isSuperAdmin) {
        response.sendRedirect(request.getContextPath() + "/securityError.jsp?type=_admin");
        return;
    }
%>

<%
    PolarisConfigurationService polarisConfigService = SpringUtils.getBean(
        PolarisConfigurationService.class);
    String polarisFhirEndpointPath = polarisConfigService.getAdminPolarisFhirEndpoint();
    String cparComponentUrl = polarisConfigService.getCparComponentUrl();

    // Check if organization UUID exists
    String organizationUuid = polarisConfigService.getPolarisUuid();

    // Oranization UUID generation
    String generateUuidAction = request.getParameter("generateUuidAction");
    if (organizationUuid == null && "generate".equals(generateUuidAction)) {
        organizationUuid = polarisConfigService.generatePolarisOrganizationId();
    }

    // CPAR feature flag
    boolean cparEnabled = polarisConfigService.isCparEnabled();

    // Handle CPAR toggle form submission
    String cparToggleAction = request.getParameter("cparToggleAction");
    if (cparToggleAction != null && cparToggleAction.equals("toggle")) {
        String cparEnabledParam = request.getParameter("cparEnabled");
        boolean isEnabled = "true".equals(cparEnabledParam);
        polarisConfigService.setCparEnabled(isEnabled);
        cparEnabled = isEnabled;
    }

    // Handle CPAR component URL form submission
    String cparComponentUrlAction = request.getParameter("cparComponentUrlAction");
    if (cparComponentUrlAction != null && cparComponentUrlAction.equals("update")) {
        String newCparComponentUrl = request.getParameter("cparComponentUrl");
        if (newCparComponentUrl != null && !newCparComponentUrl.trim().isEmpty()) {
            polarisConfigService.setCparComponentUrl(newCparComponentUrl);
            cparComponentUrl = newCparComponentUrl;
        }
    }
%>

<html:html locale="true">
    <head>
        <title><bean:message key="demographic.demographicexport.title"/></title>

        <link href="<%=request.getContextPath() %>/css/bootstrap.min.css" rel="stylesheet">

        <style type="text/css">
          input[type="checkbox"] {
            line-height: normal;
            margin: 4px 4px 4px;
          }

          body {
            font-family: sans-serif;
            margin: 2rem;
          }

          h2, h3 {
            margin-bottom: 1rem;
          }

          form {
            max-width: 600px;
            background: #f9f9f9;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }

          label {
            display: block;
            margin-top: 1rem;
            font-weight: bold;
          }

          input[type="text"],
          input[type="password"] {
            width: 100%;
            padding: 0.5rem;
            margin-top: 0.25rem;
            border: 1px solid #ccc;
            border-radius: 4px;
          }

          button {
            margin-top: 1.5rem;
            padding: 0.75rem 1.5rem;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
          }

          button:hover {
            background-color: #0056b3;
          }

          .toggle-container {
            margin-top: 2rem;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            max-width: 600px;
          }

          .toggle-form {
            padding: 0;
            box-shadow: none;
            background: transparent;
            display: flex;
            align-items: center;
            height: 100%;
			margin: 0;
          }

          .toggle-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 36px;
            width: 100%;
          }

          .toggle-title {
            font-weight: bold;
            font-size: 1.1em;
            margin-right: 20px;
            flex-grow: 1;
            display: flex;
            align-items: center;
          }

          .toggle-switch {
            margin-right: 20px;
			margin-bottom: 9px;
          }

          /* Toggle Switch Styles */
          .switch {
            position: relative;
            display: inline-block;
            width: 46px;
            height: 24px;
          }

          .switch input {
            opacity: 0;
            width: 0;
            height: 0;
          }

          .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
          }

          .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
          }

          input:checked + .slider {
            background-color: #2196F3;
          }

          input:focus + .slider {
            box-shadow: 0 0 1px #2196F3;
          }

          input:checked + .slider:before {
            transform: translateX(22px);
          }

          .slider.round {
            border-radius: 24px;
          }

          .slider.round:before {
            border-radius: 50%;
          }

          .toggle-button {
            margin-top: 0;
            white-space: nowrap;
            height: 24px;
            padding: 0 12px;
            font-size: 0.9em;
            line-height: 24px;
          }

          .section-divider {
            margin: 2rem 0;
            border-top: 1px solid #ddd;
            max-width: 600px;
          }

          button:disabled {
            background-color: #cccccc;
            color: #666666;
            cursor: not-allowed;
          }

          .uuid-display {
            padding: 0.25rem 0.5rem;
            margin-right: 20px;
            background-color: #f0f0f0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.9em;
            max-width: 250px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        </style>
        <script type="text/javascript">
          function checkRequiredFields() {
            const fhirEndpointInput = document.getElementById("fhirEndpoint");
            const apiKeyInput = document.getElementById("apiKey");

            return fhirEndpointInput && fhirEndpointInput.value
                && fhirEndpointInput.value.trim().length !== 0 && apiKeyInput && apiKeyInput.value
                && apiKeyInput.value.trim().length !== 0;
          }
        </script>

    </head>

    <body>
    <div class="container-fluid well">
        <h3><bean:message key="polarisAdmin.configuration.title"/></h3>
        <!-- Organization UUID Section -->
        <div class="toggle-container">
            <form method="post" action="polarisAPIManagementConfiguration.jsp" class="toggle-form">
                <div class="toggle-row">
                    <div class="toggle-title">Generate Organization UUID</div>
                    <% if (organizationUuid != null) { %>
                        <div class="uuid-display"><%= organizationUuid %></div>
                        <button type="button" class="toggle-button" disabled title="Already generated">Generate</button>
                    <% } else { %>
                        <input type="hidden" name="generateUuidAction" value="generate" />
                        <button type="submit" class="toggle-button">Generate</button>
                    <% } %>
                </div>
            </form>
        </div>

        <!-- Polaris API Configuration Form -->
        <html:form method="post" enctype="multipart/form-data" action="/admin/polarisApi.do"
                onsubmit="return checkRequiredFields()">
        <label for="fhirEndpoint">Polaris FHIR Endpoint Path</label>
        <input
                type="text"
                id="fhirEndpoint"
                name="polarisFhirEndpoint"
                placeholder="https://polaris.apps.health/fhir/R4/"
                value="<%=polarisFhirEndpointPath%>" />
        <label for="clientId">Polaris Client ID</label>
        <input
                type="text"
                id="clientId"
                name="polarisClientId"
                placeholder="Enter new client ID" />

        <label for="clientSecret">Polaris Client Secret</label>
        <input
                type="password"
                id="clientSecret"
                name="polarisClientSecret"
                placeholder="Enter new client Secret"/>

        <button type="submit">Save Polaris API Settings</button>
        </html:form>
            <html:errors bundle="polaris"/>

        <!-- CPAR Feature Toggle Form -->
        <div class="toggle-container">
            <form method="post" action="polarisAPIManagementConfiguration.jsp" class="toggle-form">
                <div class="toggle-row">
                    <div class="toggle-title">Enable CPAR Components</div>
                    <div class="toggle-switch">
                        <label class="switch">
                            <input type="checkbox" name="cparEnabled" value="true" <%= cparEnabled ? "checked" : "" %> />
                            <span class="slider round"></span>
                        </label>
                    </div>
                    <input type="hidden" name="cparToggleAction" value="toggle" />
                    <button type="submit" class="toggle-button">Save CPAR Setting</button>
                </div>
            </form>
        </div>

        <!-- CPAR Component URL Form -->
        <div class="toggle-container">
            <form method="post" action="polarisAPIManagementConfiguration.jsp">
                <label for="cparComponentUrl">Polaris Component URL</label>
                <input
                        type="text"
                        id="cparComponentUrl"
                        name="cparComponentUrl"
                        placeholder="https://packages.staging.apps.health/cpar/0.0.0/cpar.umd.js"
                        value="<%= cparComponentUrl %>" />
                <input type="hidden" name="cparComponentUrlAction" value="update" />
                <button type="submit">Save Component URL</button>
            </form>
        </div>

        <script type="text/javascript"
                src="<%=request.getContextPath() %>/js/jquery-1.9.1.min.js"></script>
        <script src="<%=request.getContextPath() %>/js/bootstrap.min.js"></script>

    </body>
</html:html>
