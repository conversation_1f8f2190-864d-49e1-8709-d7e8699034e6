<%--

    Copyright (c) 2025 WELL EMR Group Inc.
    This software is made available under the terms of the
    GNU General Public License, Version 2, 1991 (GPLv2).
    License details are available via "gnu.org/licenses/gpl-2.0.html".

--%>
<%@page import="org.apache.log4j.Logger" %>
<%@page import="org.oscarehr.integration.polaris.service.CparUiService" %>
<%@page import="org.oscarehr.integration.polaris.model.CparUiAttributes" %>
<%@page import="org.oscarehr.util.MiscUtils" %>
<%@page import="org.oscarehr.util.SpringUtils" %>
<%
	Logger log = MiscUtils.getLogger();
	CparUiService cparUiService = SpringUtils.getBean(CparUiService.class);
	CparUiAttributes cparUiAttributes = null;
	try {
		cparUiAttributes = cparUiService.getCparUiAttributes();
	} catch (Exception e) {
		log.error("Error retrieving CPAR UI attributes", e);
	}
%>
<iframe id="cparIframe"
		style="position: absolute; top: 0; right: 0; height: 300px; border: none;"
		title="CPAR Component"
></iframe>
<script type="text/javascript">
	document.addEventListener('DOMContentLoaded', function() {
		const iframe = document.getElementById('cparIframe');
		const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

		iframeDoc.open();
		iframeDoc.write(`
			<!DOCTYPE html>
			<html>
			<head>
				<title>CPAR Component</title>
				<script src="<%= cparUiAttributes.getComponentUrl() %>"><\/script>
			</head>
			<style>
				body {
					background: none transparent;
					margin: 10px auto 0;
				}
			</style>
			<body>
			<% if (cparUiAttributes == null) { %>
				<div>Unable to load the CPAR component.</div>
			<% } else { %>
				<cpar-confirm-patient
					patient-identifier-system="<%= cparUiAttributes.getIdentifierSystem() %>"
					patient-identifier-value="<%= request.getParameter("demographic_no") %>"
					organization-identifier-system="<%= cparUiAttributes.getOrganizationIdentifierSystem() %>"
					organization-identifier-value="<%= cparUiAttributes.getOrganizationIdentifierValue() %>"
					medplum-base-url="<%= cparUiAttributes.getMedplumBaseUrl() %>"
					theme="<%= cparUiAttributes.getTheme() %>"
					medplum-token="<%= cparUiAttributes.getMedplumAccessToken() %>"
					invert-colors="true">
				</cpar-confirm-patient>
			<% } %>
			</body>
			</html>
		`);
		iframeDoc.close();
	});
</script>