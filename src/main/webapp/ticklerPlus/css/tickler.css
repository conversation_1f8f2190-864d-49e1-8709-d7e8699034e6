BODY {
	FONT-SIZE: Normal;
	FONT-FAMILY: Verdana, Tahoma, Arial, sans-serif;
}

TABLE {
	font-family: Arial, Verdana, Tahoma, Helvetica, sans-serif;
}

TD {
	font-size: 11pt;
}

TH {
	font-size: 11pt;
}

.blueText {
	font-size: 9pt;
	vertical-align: top;
}

.mbttn {
	background: #D7DBF2;
	border-bottom: 1px solid #104A7B;
	border-right: 1px solid #104A7B;
	border-left: 1px solid #AFC4D5;
	border-top: 1px solid #AFC4D5;
	color: #000066;
	height: 19px;
	text-decoration: none;
	cursor: hand
}

.subject {
	background-color: #003399;
	color: #FFFFFF;
	font-size: 14pt;
	font-weight: bold;
	text-align: centre;
}

.searchTitle {
	background-color: #6699CC;
	color: #000000;
	font-weight: bold;
	text-align: left;
	height
	="20px"
}

.title {
	background-color: #6699CC;
	color: #000000;
	font-weight: bold;
	text-align: center;
	height
	="20px"
}

.fieldTitle {
	background-color: #EEEEFF;
	font-weight: bold;
}

.fieldValue {
	background-color: #EEEEFF;
	text-align: left;
}

.error {
	color: #FF0000;
	background-color: #EEEEFF;
	text-align: left;
}

.message {
	color: #000000;
	background-color: #EEEEFF;
	text-align: left;
}

.fieldTitle {
	background-color: #EEEEFF;
	font-weight: bold;
}

.fieldValue {
	background-color: #EEEEFF;
	text-align: left;
}