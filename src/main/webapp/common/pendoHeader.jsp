<%--

    Copyright (c) 2025 WELL EMR Group Inc.
    This software is made available under the terms of the
    GNU General Public License, Version 2, 1991 (GPLv2).
    License details are available via "gnu.org/licenses/gpl-2.0.html".

--%>

<%@ page import="org.apache.commons.lang.StringEscapeUtils" %>
<%@ page import="org.oscarehr.common.service.PendoMetadataService" %>
<%@ page import="org.oscarehr.common.model.PendoVisitorData" %>
<%@ page import="org.oscarehr.common.model.PendoAccountData" %>
<%@ page import="org.oscarehr.common.model.Provider" %>
<%@ page import="org.apache.commons.lang.StringUtils" %>
<%@ page import="org.oscarehr.util.SpringUtils" %>
<%@ page import="com.fasterxml.jackson.databind.ObjectMapper" %>

<%
	final PendoMetadataService pendoMetadataService = SpringUtils.getBean(
			PendoMetadataService.class);
	final boolean isPendoEnabled = pendoMetadataService.isPendoEnabled();
	final String pendoApiKey = pendoMetadataService.getPendoApiKey();
	final String escapedApiKey = StringEscapeUtils.escapeJavaScript(pendoApiKey);

	if (isPendoEnabled && StringUtils.isNotEmpty(pendoApiKey)) {
		final Provider pendoProvider = (Provider) session.getAttribute("provider");
		final PendoVisitorData visitorData = pendoMetadataService.getVisitorData(pendoProvider, session);
		final PendoAccountData accountData = pendoMetadataService.getAccountData();

		final ObjectMapper objectMapper = new ObjectMapper();
		final String visitorDataJson = StringEscapeUtils.escapeJavaScript(
				objectMapper.writeValueAsString(visitorData));
		final String accountDataJson = StringEscapeUtils.escapeJavaScript(
				objectMapper.writeValueAsString(accountData));
%>

<script type="text/javascript">
  function safePendoStringify(value, replacer, space) {
    const nativeJSONStringify = JSON.stringify;

    const arrayToJSON = Array.prototype.hasOwnProperty('toJSON') ? Array.prototype.toJSON : undefined;
    const objectToJSON = Object.prototype.hasOwnProperty('toJSON') ? Object.prototype.toJSON : undefined;

    if (arrayToJSON !== undefined) {
      delete Array.prototype.toJSON;
    }
    if (objectToJSON !== undefined) {
      delete Object.prototype.toJSON;
    }

    let result;
    try {
      result = nativeJSONStringify(value, replacer, space);
    } finally {
      if (arrayToJSON !== undefined) {
        Array.prototype.toJSON = arrayToJSON;
      }
      if (objectToJSON !== undefined) {
        Object.prototype.toJSON = objectToJSON;
      }
    }

    return result;
  }
</script>

<script>
  (function (apiKey) {
    (function (p, e, n, d, o) {
      let v, w, x, y, z;
      o = p[d] = p[d] || {};
      o._q = o._q || [];
      v = ['initialize', 'identify', 'updateOptions', 'pageLoad', 'track'];
      for (w = 0, x = v.length; w < x; ++w) (function (m) {
        o[m] = o[m] || function () {
          o._q[m === v[0] ? 'unshift' : 'push']([m].concat([].slice.call(arguments, 0)));
        };
      })(v[w]);
      y = e.createElement(n);
      y.async = !0;
      y.src = 'https://cdn.pendo.io/agent/static/' + apiKey + '/pendo.js';
      z = e.getElementsByTagName(n)[0];
      z.parentNode.insertBefore(y, z);
    })(window, document, 'script', 'pendo');

    // Use String, int, or bool for value types.
    // Parse the safely escaped JSON data
    const visitorData = JSON.parse('<%= visitorDataJson %>');
    const accountData = JSON.parse('<%= accountDataJson %>');

    // Make safePendoStringify available to Pendo
    pendo.safePendoStringify = safePendoStringify;

    pendo.initialize({
      excludeAllText: true,
      visitor: {
        id: visitorData.visitorId,
        full_name: visitorData.fullName,
        role: visitorData.role, // Optional
        isSuperUser: visitorData.isSuperUser,
        providerType: visitorData.providerType,
        hasCollegeNumber: visitorData.hasCollegeNumber,
        collegeType: visitorData.collegeType,
      },
      account: {
        id: accountData.practiceId,
        emrVersion: accountData.emrVersion,
        province: accountData.province
      },
      location: {
        transforms: [
          {
            attr: 'search',
            action: 'ExcludeKeys',
            data: function (hostname, url) {
				return ['demographic_name', 'last_name', 'first_name', 'hin', 'dob', 'name',
					`keyword`];
            }
          },
          {
            attr: 'search',
            action: 'ExcludeKeys',
            data: function (hostname, url) {
              const searchParams = new URL(url).searchParams;
              return Array.from(searchParams.keys()).filter((key) => {
                return typeof key === 'string' && key.startsWith('utm');
              });
            },
          }
        ]
      }
    });
  })('<%=escapedApiKey%>');
</script>
<% } %>
