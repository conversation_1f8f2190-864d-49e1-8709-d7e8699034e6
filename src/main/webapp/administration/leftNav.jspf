<%--

    Copyright (c) 2001-2002. Department of Family Medicine, McMaster University. All Rights Reserved.
    This software is published under the GPL GNU General Public License.
    This program is free software; you can redistribute it and/or
    modify it under the terms of the GNU General Public License
    as published by the Free Software Foundation; either version 2
    of the License, or (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.

    This software was written for the
    Department of Family Medicine
    McMaster University
    Hamilton
    Ontario, Canada

--%>
<%@page import="org.oscarehr.myoscar.utils.MyOscarLoggedInInfo"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="/WEB-INF/oscar-tag.tld" prefix="oscar" %>
<%@ page import="org.apache.commons.lang.StringUtils"%>
<%@ page import="org.oscarehr.preferences.service.SystemPreferenceService" %>
<%@ page import="org.oscarehr.integration.polaris.service.PolarisConfigurationService" %>

<%
	String curProvider_no = (String)session.getAttribute("user");
	OscarProperties props = OscarProperties.getInstance();
	SystemPreferenceService systemPreferenceService = SpringUtils.getBean(SystemPreferenceService.class);
	PolarisConfigurationService polarisConfigurationService = SpringUtils.getBean(PolarisConfigurationService.class);

	boolean isCparIntegrationEnabled = polarisConfigurationService.isCparEnabled();
%>


<c:set var="ctx" value="${pageContext.request.contextPath}" scope="request" />

<div class="span2 hidden-print" id="side">

    <!--Sidebar content-->
		<div class="accordion" id="adminNav">

			<div class="accordion-group nav nav-tabs">

			<div class="accordion-heading well-small" style="margin-bottom:0px;color:#000">
			<a href="${ctx}/administration/"><i class="icon-cog"></i> <bean:message key="admin.admin.page.title" /></a>
			</div>

<!-- #USER MANAGEMENT -->
<security:oscarSec roleName="<%=roleName$%>"
	objectName="_admin,_admin.userAdmin,_admin.provider"
	rights="r" reverse="<%=false%>">
				<div class="accordion-heading">
				<a class="accordion-toggle" data-toggle="collapse" data-parent="#adminNav" href="#collapseOne">
				<bean:message key="admin.admin.UserManagement" />
				<i class="icon-chevron-right"></i>
				</a>
				</div>
				<div id="collapseOne" class="accordion-body collapse">
				<div class="accordion-inner">
				<ul>
					<li><a href='javascript:void(0);' class="xlink" rel="${ctx}/admin/provideraddarecordhtm.jsp"><bean:message key="admin.admin.btnAddProvider" /></a></li>


					<li><a href='javascript:void(0);' class="xlink" rel="${ctx}/admin/providersearchrecordshtm.jsp">
						<bean:message key="admin.admin.btnSearchProvider" />
					</a></li>
					<li><a href='javascript:void(0);' class="xlink" rel="${ctx}/admin/securityaddarecord.jsp">
						<bean:message key="admin.admin.btnAddLogin" />
					</a></li>
					<li><a href='javascript:void(0);' class="xlink" rel="${ctx}/admin/securitysearchrecordshtm.jsp">
						<bean:message key="admin.admin.btnSearchLogin" />
					</a></li>

					<li><a href='javascript:void(0);' class="xlink" rel="${ctx}/admin/providerRole.jsp">
					<bean:message key="admin.admin.assignRole"/></a></li>

					<security:oscarSec roleName="<%=roleName$%>"
						objectName="_admin,_admin.unlockAccount" rights="r">
						<li><a href='javascript:void(0);' class="xlink" rel="${ctx}/admin/unLock.jsp">
						<bean:message key="admin.admin.unlockAcct"/></a></li>
					</security:oscarSec>
				</ul>
				</div>
				</div>
</security:oscarSec>
<!-- #USER MANAGEMENT END -->

<!-- #BILLING -->
<security:oscarSec roleName="<%=roleName$%>" objectName="_admin.invoices,_admin,_admin.billing" rights="r" reverse="<%=false%>">
				<div class="accordion-heading">
				<a class="accordion-toggle" data-toggle="collapse" data-parent="#adminNav" href="#collapseTwo">
				<bean:message key="admin.admin.billing" />
				<i class="icon-chevron-right"></i>
				</a>
				</div>
				<div id="collapseTwo" class="accordion-body collapse">
				<div class="accordion-inner">
				<ul>
		            <security:oscarSec roleName="<%=roleName$%>" objectName="_admin,_admin.billing" rights="r" reverse="<%=false%>">

					<%
						if(props.isClinicaidBillingType())
						{
					%>
							<li><a href="../billing.do?billRegion=CLINICAID&action=invoice_reports" target="_blank">Manage Invoices</a></li>

					<%
							if(props.isBritishColumbiaInstanceType())
							{
					%>
								<!--oscar:oscarPropertiesCheck property="instance_type" value="BC"-->
								<li><a href='javascript:void(0);' rel="${ctx}/billing/CA/BC/billingManageReferralDoc.jsp" class="xlink"><bean:message key="admin.admin.ManageReferralDoc"/></a></li>
					<%
							}
						}


						if(props.isBritishColumbiaBillingType())
						{
					%>
							<li><a href='javascript:void(0);'
								class="xlink" rel="${ctx}/billing/manageBillingform.jsp"><bean:message key="admin.admin.ManageBillFrm"/></a></li>
							<li><a href='javascript:void(0);'
								class="xlink" rel="${ctx}/billing/CA/BC/billingPrivateCodeAdjust.jsp"><bean:message key="admin.admin.ManagePrivFrm"/></a></li>
							<oscar:oscarPropertiesCheck property="BC_BILLING_CODE_MANAGEMENT"
								value="yes">
								<li><a href='javascript:void(0);'
									class="xlink" rel="${ctx}/billing/CA/BC/billingCodeAdjust.jsp"><bean:message key="admin.admin.ManageBillCodes"/></a></li>
							</oscar:oscarPropertiesCheck>
							<li><a href='javascript:void(0);'
								class="xlink" rel="${ctx}/billing/CA/BC/showServiceCodeAssocs.do"><bean:message key="admin.admin.ManageServiceDiagnosticCodeAssoc"/></a></li>
							<li><a href='javascript:void(0);'
								class="xlink" rel="${ctx}/billing/CA/BC/supServiceCodeAssocAction.do"><bean:message key="admin.admin.ManageProcedureFeeCodeAssoc"/></a></li>

							<li><a href='javascript:void(0);' rel="${ctx}/billing/CA/BC/billingManageReferralDoc.jsp" class="xlink"><bean:message key="admin.admin.ManageReferralDoc"/></a></li>

							<li>
								<a href="javascript:void(0);" class="xlink" rel="${ ctx }/quickBillingBC.do" >
									<bean:message key="admin.admin.bcQuickBilling" />
								</a>
							</li>

							<oscar:oscarPropertiesCheck property="NEW_BC_TELEPLAN" value="no" defaultVal="true">
								<li><a href='javascript:void(0);'
									class="xlink" rel="${ctx}/billing/CA/BC/billingSim.jsp"><bean:message key="admin.admin.SimulateSubFile"/></a></li>
								<li><a href='javascript:void(0);'
									class="xlink" rel="${ctx}/billing/CA/BC/billingTeleplanGroupReport.jsp"><bean:message key="admin.admin.genTeleplanFile"/></a></li>
							</oscar:oscarPropertiesCheck>
							<oscar:oscarPropertiesCheck property="NEW_BC_TELEPLAN" value="yes">
								<li><a href='javascript:void(0);'
									class="xlink" rel="${ctx}/billing/CA/BC/TeleplanSimulation.jsp"><bean:message key="admin.admin.simulateSubFile2"/></a></li>
								<li><a href='javascript:void(0);'
									class="xlink" rel="${ctx}/billing/CA/BC/TeleplanSubmission.jsp"><bean:message key="admin.admin.genTeleplanFile2"/></a></li>
								<li><a href='javascript:void(0);'
									class="xlink" rel="${ctx}/billing/CA/BC/teleplan/ManageTeleplan.jsp"><bean:message key="admin.admin.manageTeleplan"/></a></li>
							</oscar:oscarPropertiesCheck>
							<oscar:oscarPropertiesCheck property="NEW_BC_TELEPLAN" value="no"
								defaultVal="true">
								<li><a href='javascript:void(0);'
									class="xlink" rel="${ctx}/billing/CA/BC/billingTA.jsp"><bean:message key="admin.admin.uploadRemittance"/></a></li>
							</oscar:oscarPropertiesCheck>
							<li><a href='javascript:void(0);'
								class="xlink" rel="${ctx}/billing/CA/BC/viewReconcileReports.jsp"><bean:message key="admin.admin.reconciliationReports"/></a></li>
							<li><a href='javascript:void(0);'
								class="xlink" rel="${ctx}/billing/CA/BC/billingAccountReports.jsp"><bean:message key="admin.admin.AccountingRpts"/></a></li>
							<li><a href='javascript:void(0);'
								class="xlink" rel="${ctx}/billing/CA/BC/billStatus.jsp"><bean:message key="admin.admin.editInvoices"/></a></li>
							<li><a href='javascript:void(0);'
								class="xlink" rel="${ctx}/billing/CA/BC/settleBG.jsp"><bean:message key="admin.admin.settlePaidClaims"/></a></li>
					<%
						}

						if(props.isOntarioBillingType())
						{
					%>
							<li><a href='javascript:void(0);'
								class="xlink" rel="${ctx}/billing/CA/ON/ScheduleOfBenefitsUpload.jsp"><bean:message key="admin.admin.scheduleOfBenefits"/></a></li>
							<li><a href='javascript:void(0);'
								class="xlink" rel="${ctx}/billing/CA/ON/addEditServiceCode.jsp"><bean:message key="admin.admin.manageBillingServiceCode"/></a></li>
							<li><a href='javascript:void(0);'
								class="xlink" rel="${ctx}/billing/CA/ON/billingONEditPrivateCode.jsp"><bean:message key="admin.admin.managePrivBillingCode"/></a></li>
							<li><a href='javascript:void(0);'
								class="xlink" rel="${ctx}/admin/manageCSSStyles.do"><bean:message key="admin.admin.manageCodeStyles"/></a></li>
							<li><a href='#'
								class="xlink" rel="${ctx}/admin/gstControl.jsp"><bean:message key="admin.admin.manageGSTControl"/></a></li>
							<li><a href='javascript:void(0);'
								class="xlink" rel="${ctx}/admin/gstreport.jsp"><bean:message key="admin.admin.gstReport"/></a></li>
							<li><a href='#'
								class="xlink" rel="${ctx}/billing/CA/ON/manageBillingLocation.jsp"><bean:message
								key="admin.admin.btnAddBillingLocation" /></a></li>
							<li><a href='javascript:void(0);'
								class="xlink" rel="${ctx}/billing/CA/ON/manageBillingform.jsp"><bean:message
								key="admin.admin.btnManageBillingForm" /></a></li>

							<li><a href="${ctx}/billing/CA/ON/billingOHIPsimulation.jsp?html=" class="contentLink"><bean:message
								key="admin.admin.btnSimulationOHIPDiskette" /></a></li>

							<li><a href='javascript:void(0);'
								class="xlink" rel="${ctx}/billing/CA/ON/billingOHIPreport.jsp"><bean:message
								key="admin.admin.btnGenerateOHIPDiskette" /></a></li>
							<li><a href='javascript:void(0);'
								class="xlink" rel="${ctx}/billing/CA/ON/billingCorrection.jsp?admin&billing_no="><bean:message
								key="admin.admin.btnBillingCorrection" /></a></li>
							<li><a href='javascript:void(0);'
								class="xlink" rel="${ctx}/billing/CA/ON/batchBilling.jsp?service_code=all"><bean:message
								key="admin.admin.btnBatchBilling" /></a></li>
							<li><a href='javascript:void(0);'
								class="xlink" rel="${ctx}/billing/CA/ON/inr/reportINR.jsp?provider_no=all"><bean:message
								key="admin.admin.btnINRBatchBilling" /></a></li>
							<li><a href='javascript:void(0);'
								class="xlink" rel="${ctx}/billing/CA/ON/billingONUpload.jsp"><bean:message key="admin.admin.uploadMOHFile"/></a></li>


							<oscar:oscarPropertiesCheck property="moh_file_management_enabled" value="true" defaultVal="false">
							<li><a href='javascript:void(0);' class="xlink" rel="${ctx}/billing/CA/ON/viewMOHFiles.jsp"><bean:message key="admin.admin.viewMOHFiles"/></a></li>
							</oscar:oscarPropertiesCheck>

							<oscar:oscarPropertiesCheck property="mcedt.mailbox.enabled" value="false" defaultVal="false">
							<li>
								<a href="javascript:void(0);" class="xlink" rel="${ctx}/mcedt/mcedt.do">
									<bean:message key="admin.admin.mcedt"/>
								</a>
							</li>
							</oscar:oscarPropertiesCheck>

							<oscar:oscarPropertiesCheck property="mcedt.mailbox.enabled" value="true" defaultVal="false">
							<li>
								<a href="javascript:void(0);" class="xlink" rel="${ctx}/mcedt/kaimcedt.do">
									<bean:message key="admin.admin.mcedt.mailbox"/>
								</a>
							</li>
							</oscar:oscarPropertiesCheck>
							<li><a href='javascript:void(0);'
								class="xlink" rel="${ctx}/servlet/oscar.DocumentUploadServlet"><bean:message
								key="admin.admin.btnBillingReconciliation" /></a></li>
										<!--  li><a href='javascript:void(0);' onclick ='popupPage(600,900,"${ctx}/billing/CA/ON/billingRA.jsp");return false;'><bean:message key="admin.admin.btnBillingReconciliation"/></a></li-->
							<!--  li><a href='javascript:void(0);' onclick ='popupPage(600,1000,"${ctx}/billing/CA/ON/billingOBECEA.jsp");return false;'><bean:message key="admin.admin.btnEDTBillingReportGenerator"/></a></li-->
							<li><a href='javascript:void(0);'
								class="xlink" rel="${ctx}/billing/CA/ON/billStatus.jsp"><bean:message key="admin.admin.invoiceRpts"/></a></li>
										<li><a href='javascript:void(0);'
												class="xlink" rel="${ctx}/billing/CA/ON/endYearStatement.do"><bean:message key="admin.admin.endYearStatement"/></a></li>

							<oscar:oscarPropertiesCheck property="rma_enabled" value="true" defaultVal="false">
								<li>
									<a href='#'	class="xlink" rel="${ctx}/admin/clinicNbrManage.jsp">Manage Clinic NBR Codes</a>
								</li>
							</oscar:oscarPropertiesCheck>
					<%
						}
					%>

		            </security:oscarSec>

					<%
						if(props.isOntarioBillingType())
						{
					%>
							<li><a href='javascript:void(0);' class="xlink" rel='${ctx}/billing/CA/ON/billingONPayment.jsp'><bean:message key="admin.admin.paymentReceived"/></a></li>
							<li><a href='javascript:void(0);' class="xlink" rel='${ctx}/billing/CA/ON/managePaymentType.do'><bean:message key="admin.admin.managePaymentType"/></a></li>
					<%
						}
					%>
					<% if (isCparIntegrationEnabled) { %>
					<li>
						<a href='javascript:void(0);' class="xlink" rel="${ctx}/billing/CA/AB/billingCparManagement.jsp">
							<bean:message key="admin.admin.billing.cparManagement"/>
						</a>
					</li>
					<% } %>
				</ul>
				</div>
				</div>
</security:oscarSec>
<!-- #BILLING END-->

<!-- #OLD OSCAR BILLING -->
<security:oscarSec roleName="<%=roleName$%>" objectName="_admin.invoices,_admin,_admin.billing" rights="r" reverse="<%=false%>">
	<%
		boolean showOldBillingLinks = props.showOldBillingLinks();

		if(showOldBillingLinks)
		{
	%>
			<div class="accordion-heading">
			<a class="accordion-toggle" data-toggle="collapse" data-parent="#adminNav" href="#collapseOld">
			<bean:message key="admin.admin.billingOld" />
			<i class="icon-chevron-right"></i>
			</a>
			</div>
			<div id="collapseOld" class="accordion-body collapse">
			<div class="accordion-inner">
			<ul>
				<security:oscarSec roleName="<%=roleName$%>" objectName="_admin,_admin.billing" rights="r" reverse="<%=false%>">
	<%
				if(props.isBritishColumbiaInstanceType())
				{
	%>
						<li><a href='javascript:void(0);'
							   class="xlink" rel="${ctx}/billing/manageBillingform.jsp"><bean:message key="admin.admin.ManageBillFrm"/></a></li>
						<li><a href='javascript:void(0);'
							   class="xlink" rel="${ctx}/billing/CA/BC/billingPrivateCodeAdjust.jsp"><bean:message key="admin.admin.ManagePrivFrm"/></a></li>
						<oscar:oscarPropertiesCheck property="BC_BILLING_CODE_MANAGEMENT"
													value="yes">
							<li><a href='javascript:void(0);'
								   class="xlink" rel="${ctx}/billing/CA/BC/billingCodeAdjust.jsp"><bean:message key="admin.admin.ManageBillCodes"/></a></li>
						</oscar:oscarPropertiesCheck>
						<li><a href='javascript:void(0);'
							   class="xlink" rel="${ctx}/billing/CA/BC/showServiceCodeAssocs.do"><bean:message key="admin.admin.ManageServiceDiagnosticCodeAssoc"/></a></li>
						<li><a href='javascript:void(0);'
							   class="xlink" rel="${ctx}/billing/CA/BC/supServiceCodeAssocAction.do"><bean:message key="admin.admin.ManageProcedureFeeCodeAssoc"/></a></li>

						<li><a href='javascript:void(0);' rel="${ctx}/billing/CA/BC/billingManageReferralDoc.jsp" class="xlink"><bean:message key="admin.admin.ManageReferralDoc"/></a></li>

						<li>
							<a href="javascript:void(0);" class="xlink" rel="${ ctx }/quickBillingBC.do" >
								<bean:message key="admin.admin.bcQuickBilling" />
							</a>
						</li>

						<oscar:oscarPropertiesCheck property="NEW_BC_TELEPLAN" value="no" defaultVal="true">
							<li><a href='javascript:void(0);'
								   class="xlink" rel="${ctx}/billing/CA/BC/billingSim.jsp"><bean:message key="admin.admin.SimulateSubFile"/></a></li>
							<li><a href='javascript:void(0);'
								   class="xlink" rel="${ctx}/billing/CA/BC/billingTeleplanGroupReport.jsp"><bean:message key="admin.admin.genTeleplanFile"/></a></li>
						</oscar:oscarPropertiesCheck>
						<oscar:oscarPropertiesCheck property="NEW_BC_TELEPLAN" value="yes">
							<li><a href='javascript:void(0);'
								   class="xlink" rel="${ctx}/billing/CA/BC/TeleplanSimulation.jsp"><bean:message key="admin.admin.simulateSubFile2"/></a></li>
							<li><a href='javascript:void(0);'
								   class="xlink" rel="${ctx}/billing/CA/BC/TeleplanSubmission.jsp"><bean:message key="admin.admin.genTeleplanFile2"/></a></li>
							<li><a href='javascript:void(0);'
								   class="xlink" rel="${ctx}/billing/CA/BC/teleplan/ManageTeleplan.jsp"><bean:message key="admin.admin.manageTeleplan"/></a></li>
						</oscar:oscarPropertiesCheck>
						<oscar:oscarPropertiesCheck property="NEW_BC_TELEPLAN" value="no"
													defaultVal="true">
							<li><a href='javascript:void(0);'
								   class="xlink" rel="${ctx}/billing/CA/BC/billingTA.jsp"><bean:message key="admin.admin.uploadRemittance"/></a></li>
						</oscar:oscarPropertiesCheck>
						<li><a href='javascript:void(0);'
							   class="xlink" rel="${ctx}/billing/CA/BC/viewReconcileReports.jsp"><bean:message key="admin.admin.reconciliationReports"/></a></li>
						<li><a href='javascript:void(0);'
							   class="xlink" rel="${ctx}/billing/CA/BC/billingAccountReports.jsp"><bean:message key="admin.admin.AccountingRpts"/></a></li>
						<li><a href='javascript:void(0);'
							   class="xlink" rel="${ctx}/billing/CA/BC/billStatus.jsp"><bean:message key="admin.admin.editInvoices"/></a></li>
						<li><a href='javascript:void(0);'
							   class="xlink" rel="${ctx}/billing/CA/BC/settleBG.jsp"><bean:message key="admin.admin.settlePaidClaims"/></a></li>
	<%
					}

					if(props.isOntarioInstanceType())
					{
	%>
						<li><a href='javascript:void(0);'
							   class="xlink" rel="${ctx}/billing/CA/ON/ScheduleOfBenefitsUpload.jsp"><bean:message key="admin.admin.scheduleOfBenefits"/></a></li>
						<li><a href='javascript:void(0);'
							   class="xlink" rel="${ctx}/billing/CA/ON/addEditServiceCode.jsp"><bean:message key="admin.admin.manageBillingServiceCode"/></a></li>
						<li><a href='javascript:void(0);'
							   class="xlink" rel="${ctx}/billing/CA/ON/billingONEditPrivateCode.jsp"><bean:message key="admin.admin.managePrivBillingCode"/></a></li>
						<li><a href='javascript:void(0);'
							   class="xlink" rel="${ctx}/admin/manageCSSStyles.do"><bean:message key="admin.admin.manageCodeStyles"/></a></li>
						<li><a href='#'
							   class="xlink" rel="${ctx}/admin/gstControl.jsp"><bean:message key="admin.admin.manageGSTControl"/></a></li>
						<li><a href='javascript:void(0);'
							   class="xlink" rel="${ctx}/admin/gstreport.jsp"><bean:message key="admin.admin.gstReport"/></a></li>
						<li><a href='#'
							   class="xlink" rel="${ctx}/billing/CA/ON/manageBillingLocation.jsp"><bean:message
								key="admin.admin.btnAddBillingLocation" /></a></li>
						<li><a href='javascript:void(0);'
							   class="xlink" rel="${ctx}/billing/CA/ON/manageBillingform.jsp"><bean:message
								key="admin.admin.btnManageBillingForm" /></a></li>

						<li><a href="${ctx}/billing/CA/ON/billingOHIPsimulation.jsp?html=" class="contentLink"><bean:message
								key="admin.admin.btnSimulationOHIPDiskette" /></a></li>

						<li><a href='javascript:void(0);'
							   class="xlink" rel="${ctx}/billing/CA/ON/billingOHIPreport.jsp"><bean:message
								key="admin.admin.btnGenerateOHIPDiskette" /></a></li>
						<li><a href='javascript:void(0);'
							   class="xlink" rel="${ctx}/billing/CA/ON/billingCorrection.jsp?admin&billing_no="><bean:message
								key="admin.admin.btnBillingCorrection" /></a></li>
						<li><a href='javascript:void(0);'
							   class="xlink" rel="${ctx}/billing/CA/ON/batchBilling.jsp?service_code=all"><bean:message
								key="admin.admin.btnBatchBilling" /></a></li>
						<li><a href='javascript:void(0);'
							   class="xlink" rel="${ctx}/billing/CA/ON/inr/reportINR.jsp?provider_no=all"><bean:message
								key="admin.admin.btnINRBatchBilling" /></a></li>
						<li><a href='javascript:void(0);'
							   class="xlink" rel="${ctx}/billing/CA/ON/billingONUpload.jsp"><bean:message key="admin.admin.uploadMOHFile"/></a></li>


						<oscar:oscarPropertiesCheck property="moh_file_management_enabled" value="true" defaultVal="false">
							<li><a href='javascript:void(0);' class="xlink" rel="${ctx}/billing/CA/ON/viewMOHFiles.jsp"><bean:message key="admin.admin.viewMOHFiles"/></a></li>
						</oscar:oscarPropertiesCheck>

						<oscar:oscarPropertiesCheck property="mcedt.mailbox.enabled" value="false" defaultVal="false">
							<li>
								<a href="javascript:void(0);" class="xlink" rel="${ctx}/mcedt/mcedt.do">
									<bean:message key="admin.admin.mcedt"/>
								</a>
							</li>
						</oscar:oscarPropertiesCheck>

						<oscar:oscarPropertiesCheck property="mcedt.mailbox.enabled" value="true" defaultVal="false">
							<li>
								<a href="javascript:void(0);" class="xlink" rel="${ctx}/mcedt/kaimcedt.do">
									<bean:message key="admin.admin.mcedt.mailbox"/>
								</a>
							</li>
						</oscar:oscarPropertiesCheck>
						<li><a href='javascript:void(0);'
							   class="xlink" rel="${ctx}/servlet/oscar.DocumentUploadServlet"><bean:message
								key="admin.admin.btnBillingReconciliation" /></a></li>
						<!--  li><a href='javascript:void(0);' onclick ='popupPage(600,900,"${ctx}/billing/CA/ON/billingRA.jsp");return false;'><bean:message key="admin.admin.btnBillingReconciliation"/></a></li-->
						<!--  li><a href='javascript:void(0);' onclick ='popupPage(600,1000,"${ctx}/billing/CA/ON/billingOBECEA.jsp");return false;'><bean:message key="admin.admin.btnEDTBillingReportGenerator"/></a></li-->
						<li><a href='javascript:void(0);'
							   class="xlink" rel="${ctx}/billing/CA/ON/billStatus.jsp"><bean:message key="admin.admin.invoiceRpts"/></a></li>
						<li><a href='javascript:void(0);'
							   class="xlink" rel="${ctx}/billing/CA/ON/endYearStatement.do"><bean:message key="admin.admin.endYearStatement"/></a></li>

						<oscar:oscarPropertiesCheck property="rma_enabled" value="true" defaultVal="false">
							<li>
								<a href='#'	class="xlink" rel="${ctx}/admin/clinicNbrManage.jsp">Manage Clinic NBR Codes</a>
							</li>
						</oscar:oscarPropertiesCheck>
	<%
					}
	%>

				</security:oscarSec>

	<%
					if(props.isOntarioInstanceType())
					{
	%>
						<li><a href='javascript:void(0);' class="xlink" rel='${ctx}/billing/CA/ON/billingONPayment.jsp'><bean:message key="admin.admin.paymentReceived"/></a></li>
						<li><a href='javascript:void(0);' class="xlink" rel='${ctx}/billing/CA/ON/managePaymentType.do'><bean:message key="admin.admin.managePaymentType"/></a></li>
	<%
					}
	%>
			</ul>
			</div>
			</div>
	<%
		}
	%>
</security:oscarSec>
<!-- #OLD OSCAR BILLING END -->

<!-- #LABS/INBOX -->
<security:oscarSec roleName="<%=roleName$%>" objectName="_admin," rights="r" reverse="<%=false%>">
				<div class="accordion-heading">
				<a class="accordion-toggle" data-toggle="collapse" data-parent="#adminNav" href="#collapseThree">
				<bean:message key="admin.admin.LabsInbox" />
				<i class="icon-chevron-right"></i>
				</a>
				</div>

				<div id="collapseThree" class="accordion-body collapse">
				<div class="accordion-inner">
				<ul>
					<li><a href='javascript:void(0);' class="xlink" rel="${ctx}/lab/CA/ALL/testUploader.jsp" ><bean:message key="admin.admin.hl7LabUpload" /></a></li>
					<oscar:oscarPropertiesCheck property="OLD_LAB_UPLOAD" value="yes" defaultVal="false">
						<li><a href="${ctx}/lab/CA/BC/LabUpload.jsp" class="contentLink"><bean:message key="admin.admin.oldLabUpload"/></a></li>
					</oscar:oscarPropertiesCheck>
					<li><a href="${ctx}/admin/labforwardingrules.jsp" class="contentLink"><bean:message key="admin.admin.labFwdRules" /></a></li>

					<li><a href="${ctx}/admin/addQueue.jsp" class="contentLink"><bean:message key="admin.admin.AddNewQueue"/></a></li>
				</ul>
				</div>
				</div>
</security:oscarSec>
<!-- #LABS/INBOX END -->

<!--  #FORMS/EFORMS -->
<security:oscarSec roleName="<%=roleName$%>" objectName="_admin,_admin.eform" rights="r" reverse="<%=false%>">
				<div class="accordion-heading">
				<a class="accordion-toggle" data-toggle="collapse" data-parent="#adminNav" href="#collapseForms">
				<bean:message key="admin.admin.FormsEforms" />
				<i class="icon-chevron-right"></i>
				</a>
				</div>
				<div id="collapseForms" class="accordion-body collapse">
				<div class="accordion-inner">
				<ul>
					<li><a href="${ctx}/form/setupSelect.do" class="contentLink"><bean:message key="admin.admin.btnSelectForm" /></a></li>

					<li><a href="${ctx}/form/formXmlUpload.jsp" class="contentLink"><bean:message key="admin.admin.btnImportFormData" /></a></li>

					<li><a href="${ctx}/eform/efmformmanager.jsp" class="contentLink defaultForms">
						<bean:message key="eform.showmyform.msgManageEFrm"/>
					</a></li>

					<li><a href="${ctx}/eform/efmimagemanager.jsp"  class="contentLink defaultImageUpload">
						<bean:message key="admin.admin.btnUploadImage" />
					</a></li>
					<li><a href="${ctx}/eform/efmmanageformgroups.jsp" class="contentLink defaultFormsGroups">
						<bean:message key="admin.admin.frmGroups"/>
					</a></li>

					<% if (org.oscarehr.common.IsPropertiesOn.isIndivicaRichTextLetterEnable()) { %>
					<li><a href="${ctx}/eform/efmformrtl_config.jsp" class="contentLink"><bean:message key="admin.admin.richTextLetter"/></a></li>
					<% } %>

					<li><a href="${ctx}/eform/efmmanageindependent.jsp" class="contentLink">
						<bean:message key="admin.admin.frmIndependent"/>s
					</a></li>
				</ul>
				</div>
				</div>
</security:oscarSec>
<!--  #FORMS/EFORMS END-->

<!-- #REPORTS-->
	<security:oscarSec roleName="<%=roleName$%>" objectName="_admin,_admin.reporting" rights="r" reverse="<%=false%>">
				<div class="accordion-heading">
				<a class="accordion-toggle" data-toggle="collapse" data-parent="#adminNav" href="#collapseFive">
				<bean:message key="admin.admin.oscarReport" />
				<i class="icon-chevron-right"></i>
				</a>
				</div>

				<div id="collapseFive" class="accordion-body collapse">
				<div class="accordion-inner">

				<ul>
				<oscar:oscarPropertiesCheck property="enable_dashboards" value="true">
				<oscar:oscarPropertiesCheck property="instance_type" value="BC">
				<security:oscarSec roleName="<%=roleName$%>" objectName="_dashboardManager" rights="w" reverse="<%=false%>" >
					<li>
						<a href="javascript:void(0);" class="xlink" rel="${ctx}/web/dashboard/admin/DashboardManager.do" >
							<bean:message key="dashboard.dashboardmanager.title" />
						</a>
					</li>
				</security:oscarSec>
				</oscar:oscarPropertiesCheck>
				</oscar:oscarPropertiesCheck>
					<li>
					<a href="javascript:void(0);" class="xlink" rel="${ctx}/oscarReport/RptByExample.do"><bean:message key="admin.admin.btnQueryByExample" /></a>
					</li>

					<li>
					<a href="javascript:void(0);" class="xlink" rel="${ctx}/oscarReport/reportByTemplate/homePage.jsp"><bean:message key="admin.admin.rptbyTemplate" /></a>
					</li>

					<li>
					<a href="javascript:void(0);" class="xlink" rel="${ctx}/oscarReport/dbReportAgeSex.jsp"><bean:message key="admin.admin.btnAgeSexReport" /></a>
					</li>

					<li><a href="${ctx}/oscarReport/oscarReportVisitControl.jsp" class="contentLink defaultvisitreport">
						<bean:message key="admin.admin.btnVisitReport" />
						</a>
					</li>
					<li><a href="${ctx}/oscarReport/oscarReportCatchment.jsp"
						class="contentLink">PCN</a></li>
					<li><a href="${ctx}/oscarReport/FluBilling.do?orderby="
						class="contentLink"><bean:message
								key="admin.admin.btnFluBillingReport" /></a></li>
					<li><a href="${ctx}/oscarReport/obec.jsp" class="contentLink">Overnight
							Batch</a></li>
					<li><a href="${ctx}/oscarSurveillance/ReportSurveillance.jsp"
						class="contentLink"><bean:message
								key="admin.admin.report.SurveillanceReport" /></a></li>
					<li><a href="${ctx}/oscarReport/oscarReportRehabStudy.jsp" class="contentLink"><bean:message
								key="admin.admin.rehabStudy" /></a></li>

					<caisi:isModuleLoad moduleName="caisi">
						<li><a href="javascript:void(0);"
						class="xlink" rel="${ctx}/PMmodule/Reports/ProgramActivityReport" styleClass="contentLink"><bean:message key="admin.admin.activityRpt" /></a></li>
					</caisi:isModuleLoad>

					<li><a href="${ctx}/oscarReport/patientlist.jsp"
						class="contentLink"><bean:message
								key="admin.admin.exportPatientbyAppt" /></a></li>
					<li><a
						href="${ctx}/oscarReport/provider_service_report_form.jsp"
						class="contentLink"><bean:message
								key="admin.admin.providerServiceRpt" /></a></li>

					<caisi:isModuleLoad moduleName="caisi">
						<li><html:link page="/PopulationReport.do" styleClass="contentLink"> <bean:message key="admin.admin.popRpt" /></html:link></li>
						<li><a href="${ctx}/oscarReport/cds_4_report_form.jsp"	class="contentLink"><bean:message key="admin.admin.cdsRpt" /></a></li>
						<li><a href="${ctx}/oscarReport/mis_report_form.jsp" class="contentLink"><bean:message key="admin.admin.misRpt" /></a></li>
						<li><a href="${ctx}/oscarReport/ocan_report_form.jsp" class="contentLink"><bean:message key="admin.admin.ocanRpt" /></a></li>
						<li><a href="${ctx}/oscarReport/ocan_iar.jsp" class="contentLink"><bean:message key="admin.admin.ocanIarRpt" /></a></li>
						<li><a href="javascript:void(0);" class="xlink" rel="${ctx}/oscarReport/ocan_reporting.jsp"><bean:message key="admin.admin.ocanReporting"/></a></li>
						<li><a href="javascript:void(0);" class="xlink" rel="${ctx}/oscarReport/cbi_submit_form.jsp"><bean:message key="admin.admin.cbiSubmit"/></a></li>
						<li><a href="javascript:void(0);" class="xlink" rel="${ctx}/admin/cbiAdmin.jsp"><bean:message key="admin.admin.cbi.reportlink"/></a></li>
						<li><a href="javascript:void(0);" class="xlink" rel="${ctx}/oscarReport/cbi_report_form.jsp"><bean:message key="admin.admin.cbiRpt"/></a></li>
					</caisi:isModuleLoad>

					<li><a href="${ctx}/admin/UsageReport.jsp" class="contentLink"><bean:message key="admin.admin.usageRpt" /></a></li>
					<oscar:oscarPropertiesCheck property="SERVERLOGGING" value="yes">
						<li><a href="${ctx}/admin/oscarLogging.jsp" class="contentLink"><bean:message key="admin.admin.serverLog" /></a></li>
					</oscar:oscarPropertiesCheck>
					<li><a href="javascript:void(0);" class="xlink" rel="${ctx}/report/DxresearchReport.do"><bean:message key="admin.admin.DiseaseRegistry" /></a></li>
					<li><a href="javascript:void(0);" class="xlink" rel="${ctx}/admin/demographicstudy.jsp"><bean:message key="admin.admin.btnStudy" /></a></li>

					<caisi:isModuleLoad moduleName="eaaps.enabled">
					                <li><a href="javascript:void(0);" class="xlink" rel="${ctx}/eaaps/index.jsp">
								<bean:message key="admin.admin.btnEaaps" /></a>
							</li>
				        </caisi:isModuleLoad>

			<%
				if (oscarVariables.isOntarioBillingType())
								{
			%>
			<li><a href="javascript:void(0);" class="xlink" rel="${ctx}/report/reportonbilledphcp.jsp"><bean:message key="admin.admin.PHCP"/></a>
			<span style="font-size: x-small;"> (Setting: <a href="javascript:void(0);" class="xlink" rel="${ctx}/report/reportonbilledvisitprovider.jsp"><bean:message key="admin.admin.provider"/></a>,
			<a href="javascript:void(0);" class="xlink" rel="${ctx}/report/reportonbilleddxgrp.jsp"><bean:message key="admin.admin.dx"/> category</a>) </span></li>
			<%}%>



				<!-- <li>
					<a href="javascript:void(0);" class="xlink" rel="${ctx}/renal/ckd_screening_report.jsp">CKD Screening Report</a>
					</li>	
-->
					<li>
					<a href="javascript:void(0);" class="xlink" rel="${ctx}/renal/ckdScreeningReportSubmit.jsp">CKD Screening Report (async)</a>
					</li>

					<li>
						<a href="javascript:void(0);" class="xlink" rel="${ctx}/renal/preImplementationSubmit.jsp">
							Pre-Implementation Report
						</a>
					</li>
					<li>
						<a href="javascript:void(0);" class="xlink" rel="${ctx}/renal/patientLetterManager.jsp">
							<bean:message key="admin.renal.managePatientLetter"/>
						</a>
					</li>

					<security:oscarSec roleName="<%=roleName$%>" objectName="_admin.fieldnote" rights="r" reverse="<%=false%>">
					<li><a href='javascript:void(0);'
						class="xlink" rel="${ctx}/eform/fieldNoteReport/fieldnotereport.jsp">
						<bean:message key="admin.admin.fieldNoteReport" /></a>
					</li>
					</security:oscarSec>

					<security:oscarSec roleName="<%=roleName$%>" objectName="_admin.eformreporttool" rights="r" reverse="<%=false%>">
 						<li>
 							<a href='javascript:void(0);' class="xlink" rel="${ctx}/admin/eformReportTool/eformReportTool.jsp">
 							<bean:message key="admin.admin.eformReportTool" /></a>
						</li>
					</security:oscarSec>
				</ul>

				</div>
				</div>
	</security:oscarSec>
<!-- #REPORTS END -->

<!-- #ECHART -->
	<security:oscarSec roleName="<%=roleName$%>" objectName="_admin,_admin.encounter" rights="r" reverse="<%=false%>">
				<div class="accordion-heading">
				<a class="accordion-toggle" data-toggle="collapse" data-parent="#adminNav" href="#collapseSix">
				<bean:message key="admin.admin.eChart" />
				<i class="icon-chevron-right"></i>
				</a>
				</div>
				<div id="collapseSix" class="accordion-body collapse">
				<div class="accordion-inner">
				<ul>
					<li><a href='javascript:void(0);'
						class="xlink" rel="${ctx}/admin/providertemplate.jsp"><bean:message
						key="admin.admin.btnInsertTemplate" /></a></li>
				</ul>
				</div>
				</div>
	</security:oscarSec>
<!-- #ECHART END-->

<!-- #Schedule Management -->
	<security:oscarSec roleName="<%=roleName$%>" objectName="_admin,_admin.schedule" rights="r" reverse="<%=false%>">
				<div class="accordion-heading">
				<a class="accordion-toggle" data-toggle="collapse" data-parent="#adminNav" href="#collapseSeven">
				<bean:message key="admin.admin.ScheduleManagement" />
				<i class="icon-chevron-right"></i>
				</a>
				</div>

				<div id="collapseSeven" class="accordion-body collapse">
				<div class="accordion-inner">
				<ul>
					<li><a href="javascript:void(0);"
						class="xlink" rel="${ctx}/schedule/scheduletemplatesetting.jsp"
						title="<bean:message key="admin.admin.scheduleSettingTitle"/>"><bean:message
						key="admin.admin.scheduleSetting" /></a></li>
					<oscar:oscarPropertiesCheck property="ENABLE_EDIT_APPT_STATUS"
						value="yes">
						<li><a href="javascript:void(0);"
							class="xlink" rel='${ctx}/appointment/apptStatusSetting.do?method=view'
							title="<bean:message key="admin.admin.scheduleSettingTitle"/>"><bean:message
							key="admin.admin.appointmentStatusSetting" /></a></li>
					</oscar:oscarPropertiesCheck>

					<li><a href="javascript:void(0);" class="xlink" rel='${ctx}/appointment/appointmentTypeAction.do'><bean:message
								key="admin.admin.appointmentTypeList" /></a></li>

					<li><a href="javascript:void(0);"
						class="xlink" rel="${ctx}/admin/adminnewgroup.jsp?submit=blank"><bean:message
						key="admin.admin.btnAddGroupNoRecord" /></a></li>
					<li><a href="javascript:void(0);"
						class="xlink" rel="${ctx}/admin/admindisplaymygroup.jsp"><bean:message
						key="admin.admin.btnSearchGroupNoRecords" /></a></li>
					<li><a href="javascript:void(0);"
						class="xlink" rel="${ctx}/admin/groupnoacl.jsp"><bean:message
						key="admin.admin.btnGroupNoAcl" /></a></li>
		                        <li><a href='javascript:void(0);'
						class="xlink" rel="${ctx}/admin/groupPreferences.jsp"><bean:message key="admin.admin.btnGroupPreference" /></a></li>
					<li><a href="javascript:void(0);" class="xlink" rel="${ctx}/oscarPrevention/PreventionManager.jsp" title="Customize prevention notifications."><bean:message key="admin.admin.preventionNotification.title" /></a></li>
				</ul>
				</div>
				</div>
	</security:oscarSec>
<!-- #Schedule Management END-->

<!-- #CAISI - TODO-legacy: Should this move under system management?-->
<caisi:isModuleLoad moduleName="caisi">
	<security:oscarSec roleName="<%=roleName$%>" objectName="_admin.caisi" 	rights="r" reverse="<%=false%>">
				<div class="accordion-heading">
				<a class="accordion-toggle" data-toggle="collapse" data-parent="#adminNav" href="#collapseCAISI">
				<bean:message key="admin.admin.caisi" />
				<i class="icon-chevron-right"></i>
				</a>
				</div>
				<div id="collapseCAISI" class="accordion-body collapse">
				<div class="accordion-inner">
				<ul>
					<li><a href="javascript:void(0);"
						class="xlink" rel="${ctx}/SystemMessage.do">
						<bean:message key="admin.admin.systemMessage" />
					</a></li>
					<li><a href="javascript:void(0);"
						class="xlink" rel="${ctx}/FacilityMessage.do?"><bean:message key="admin.admin.FacilitiesMsgs"/></a></li>
					<li><a href="javascript:void(0);"
						class="xlink" rel="${ctx}/issueAdmin.do?method=list">
						<bean:message key="admin.admin.issueEditor" />
					</a></li>
					<li><a href="javascript:void(0);"
						class="xlink" rel="${ctx}/SurveyManager.do">
						<bean:message key="admin.admin.surveyManager" />
					</a></li>
					<li><a href="javascript:void(0);"
						class="xlink" rel="${ctx}/DefaultEncounterIssue.do">
						<bean:message key="admin.admin.defaultEncounterIssue" />
					</a></li>
				</ul>
				</div>
				</div>
	</security:oscarSec>

	<security:oscarSec roleName="<%=roleName$%>" objectName="_admin.caisi" rights="r" reverse="<%=true%>">
				<div class="accordion-heading">
				<a class="accordion-toggle" data-toggle="collapse" data-parent="#adminNav" href="#collapseCAISI2">
				<bean:message key="admin.admin.caisi" />
				<i class="icon-chevron-right"></i>
				</a>
				</div>
				<div id="collapseCAISI2" class="accordion-body collapse">
				<div class="accordion-inner">
				<ul>
					<security:oscarSec roleName="<%=roleName$%>"
						objectName="_admin.systemMessage" rights="r" reverse="<%=false%>">
						<li><a href="javascript:void(0);"
						class="xlink" rel="${ctx}/SystemMessage.do">
							<bean:message key="admin.admin.systemMessage" />
						</a></li>
					</security:oscarSec>
					<security:oscarSec roleName="<%=roleName$%>"
						objectName="_admin.facilityMessage" rights="r" reverse="<%=false%>">
						<li><a href="javascript:void(0);"
						class="xlink" rel="${ctx}/FacilityMessage.do?"><bean:message key="admin.admin.FacilitiesMsgs"/></a></li>
					</security:oscarSec>
					<security:oscarSec roleName="<%=roleName$%>"
						objectName="_admin.lookupFieldEditor" rights="r">
						<li><a href="javascript:void(0);"
						class="xlink" rel="${ctx}/Lookup/LookupTableList.do"> <bean:message key="admin.admin.LookupFieldEditor"/></a></li>
					</security:oscarSec>
					<security:oscarSec roleName="<%=roleName$%>"
						objectName="_admin.issueEditor" rights="r">
						<li><a href="javascript:void(0);"
						class="xlink" rel="${ctx}/issueAdmin.do?method=list">
							<bean:message key="admin.admin.issueEditor" />
						</a></li>
					</security:oscarSec>
					<security:oscarSec roleName="<%=roleName$%>"
						objectName="_admin.userCreatedForms" rights="r">
						<li><a href="javascript:void(0);"
						class="xlink" rel="${ctx}/SurveyManager.do">
							<bean:message key="admin.admin.surveyManager" />
						</a></li>
					</security:oscarSec>
				</ul>
				</div>
				</div>
	</security:oscarSec>
</caisi:isModuleLoad>
<!-- #CAISI END-->

<!-- #SYSTEM Management-->
	<security:oscarSec roleName="<%=roleName$%>" objectName="_admin,_admin.measurements,_admin.document,_admin.consult" rights="r" reverse="<%=false%>">
				<div class="accordion-heading">
				<a class="accordion-toggle" data-toggle="collapse" data-parent="#adminNav" href="#collapseEight">
				<bean:message key="admin.admin.SystemManagement" />
				<i class="icon-chevron-right"></i>
				</a>
				</div>
				<div id="collapseEight" class="accordion-body collapse">
				<div class="accordion-inner">
				<ul>

				<security:oscarSec roleName="<%=roleName$%>" objectName="_admin,_admin.document" rights="r" reverse="<%=false%>">

					<li><a href='javascript:void(0);'
						class="xlink" rel="${ctx}/admin/displayDocumentCategories.jsp"><bean:message key="admin.admin.DocumentCategories"/></a></li>

					<li><a href='javascript:void(0);'
						class="xlink" rel="${ctx}/admin/displayDocumentDescriptionTemplate.jsp?setDefault=true"><bean:message key="admin.admin.DocumentDescriptionTemplate"/></a></li>
				</security:oscarSec>


				<security:oscarSec roleName="<%=roleName$%>" objectName="_admin" rights="r" reverse="<%=false%>">

					<li><a href='javascript:void(0);'
						class="xlink" rel="${ctx}/admin/ManageClinic.do"><bean:message key="admin.admin.clinicAdmin"/></a></li>
					<%
						if (org.oscarehr.common.IsPropertiesOn.isMultisitesEnable())
									{
					%>
					<li><a href='javascript:void(0);'
						class="xlink" rel="${ctx}/admin/ManageSites.do"><bean:message key="admin.admin.sitesAdmin"/></a></li>
					<%
						}
					%>
					<li><a href='javascript:void(0);'
						class="xlink" rel="${ctx}/oscarResearch/oscarDxResearch/dxResearchCustomization.jsp"><bean:message
						key="oscarEncounter.Index.btnCustomize" /> <bean:message
						key="oscar.admin.diseaseRegistryQuickList" /></a></li>
				</security:oscarSec>

				<security:oscarSec roleName="<%=roleName$%>" objectName="_admin,_admin.consult" rights="r" reverse="<%=false%>">
				<!-- Allow edit consultation specialists & services -->
		            <li><a href='javascript:void(0);'
						class="xlink" rel="${ctx}/oscarEncounter/oscarConsultationRequest/config/EditSpecialists.jsp"><bean:message key="admin.admin.consultationSettings"/></a></li>
				</security:oscarSec>

				<security:oscarSec roleName="<%=roleName$%>" objectName="_admin,_admin.measurements" rights="r" reverse="<%=false%>">
					<li><a href='javascript:void(0);'
						class="xlink" rel="${ctx}/oscarEncounter/oscarMeasurements/Customization.jsp"><bean:message
						key="oscarEncounter.Index.btnCustomize" /> <bean:message
						key="admin.admin.oscarMeasurements" /></a></li>
				</security:oscarSec>

				<security:oscarSec roleName="<%=roleName$%>" objectName="_admin" rights="r" reverse="<%=false%>">
					<li><a href='javascript:void(0);' class="xlink" rel="${ctx}/oscarPrevention/PreventionListManager.jsp">
						<bean:message key="oscarprevention.preventionlistmanager.title" /></a></li>
					</security:oscarSec>

				<security:oscarSec roleName="<%=roleName$%>" objectName="_admin" rights="r" reverse="<%=false%>">
					<li><a href='javascript:void(0);'
						class="xlink" rel="${ctx}/admin/resourcebaseurl.jsp"
						title='<bean:message key="admin.admin.baseURLSettingTitle"/>'><bean:message
						key="admin.admin.btnBaseURLSetting" /></a></li>
				</security:oscarSec>

				<security:oscarSec roleName="<%=roleName$%>" objectName="_admin,_admin.messenger" rights="r" reverse="<%=false%>">
							<li><a href='javascript:void(0);'
								class="xlink" rel="${ctx}/oscarMessenger/DisplayMessages.do?providerNo=<%=curProvider_no%>&amp;userName=<%=userfirstname%>%20<%=userlastname%>"><bean:message
								key="admin.admin.messages" /></a></li>
							<li><a href='javascript:void(0);'
								class="xlink" rel="${ctx}/oscarMessenger/config/MessengerAdmin.jsp"><bean:message
								key="admin.admin.btnMessengerAdmin" /></a></li>
				</security:oscarSec>

				<security:oscarSec roleName="<%=roleName$%>" objectName="_admin" rights="r" reverse="<%=false%>">
					<li><a href='javascript:void(0);' class="xlink" rel="${ctx}/admin/keygen/keyManager.jsp"><bean:message key="admin.admin.keyPairGen" /></a></li>
					<li><a href='javascript:void(0);' class="xlink" rel="${ctx}/FacilityManager.do"><bean:message key="admin.admin.manageFacilities" /></a></li>
					<li><a href='javascript:void(0);' class="xlink" rel="${ctx}/oscarEncounter/oscarMeasurements/adminFlowsheet/NewFlowsheet.jsp">Create New Flowsheet</a></li>
					<li><a href='javascript:void(0);' class="xlink" rel="${ctx}/admin/manageFlowsheets.jsp"><bean:message key="admin.admin.flowsheetManager"/></a></li>
			      	<li><a href='javascript:void(0);' class="xlink" rel="${ctx}/admin/lotnraddrecordhtm.jsp"><bean:message key="admin.admin.add_lot_nr.title"/></a></li>
					<li><a href='javascript:void(0);' class="xlink" rel="${ctx}/admin/lotnrsearchrecordshtm.jsp"><bean:message key="admin.lotnrsearchrecordshtm.title"/></a></li>

					<li><a href='javascript:void(0);' class="xlink" rel="${ctx}/admin/jobs.jsp"><bean:message key="admin.jobs.title"/></a></li>
					<li><a href='javascript:void(0);' class="xlink" rel="${ctx}/admin/jobTypes.jsp"><bean:message key="admin.jobtypes.title"/></a></li>
	            </security:oscarSec>

				 	<%
						if (oscar.oscarSecurity.CRHelper.isCRFrameworkEnabled())
								{
					%>
				<security:oscarSec roleName="<%=roleName$%>" objectName="_admin.cookieRevolver" rights="r">
						<li>&nbsp; <bean:message key="admin.admin.titleFactorAuth"/>
						<ul>
							<li><a href='javascript:void(0);'
								class="xlink" rel='${ctx}/gatekeeper/ip/show'><bean:message key="admin.admin.ipFilter"/></a></li>
							<li><a href='javascript:void(0);'
								class="xlink" rel='${ctx}/gatekeeper/cert/?act=super'><bean:message key="admin.admin.setCert"/></a></li>
							<li><a href='javascript:void(0);'
								class="xlink" rel='${ctx}/gatekeeper/supercert'><bean:message key="admin.admin.genCert"/></a></li>
							<li><a href='javascript:void(0);'
								class="xlink" rel='${ctx}/gatekeeper/clear'><bean:message key="admin.admin.clearCookie"/></a></li>
							<li><a href='javascript:void(0);'
								class="xlink" rel='${ctx}/gatekeeper/quest/adminQuestions'><bean:message key="admin.admin.adminSecQuestions"/></a></li>
							<li><a href='javascript:void(0);'
								class="xlink" rel='${ctx}/gatekeeper/policyadmin/select'><bean:message key="admin.admin.adminSecPolicies"/></a></li>
							<li><a href='javascript:void(0);'
								class="xlink" rel='${ctx}/gatekeeper/banremover/show'><bean:message key="admin.admin.removeBans"/></a></li>
							<li><a href='javascript:void(0);'
								class="xlink" rel='${ctx}/gatekeeper/matrixadmin/show'><bean:message key="admin.admin.genMatrixCards"/></a></li>
						</ul>
						</li>
				</security:oscarSec>
					<%
						}
					%>

				</ul>
				</div>
				</div>
	</security:oscarSec>
<!-- #SYSTEM Management END-->

<!-- #Fax Management -->
<security:oscarSec roleName="<%=roleName$%>" objectName="_admin.fax" rights="r" reverse="<%=false%>">
	<div class="accordion-heading">
				<a class="accordion-toggle" data-toggle="collapse" data-parent="#adminNav" href="#collapseFifteen">
				Faxes
				<i class="icon-chevron-right"></i>
				</a>
				</div>

				<div id="collapseFifteen" class="accordion-body collapse">
					<div class="accordion-inner">
						<ul>
							<security:oscarSec roleName="<%=roleName$%>" objectName="_admin" rights="r" reverse="<%=false%>">
								<li><a href="javascript:void(0);" class="xlink" rel="${ctx}/web/legacyFaxConfiguration.jsp" >Fax Configuration</a></li>
							</security:oscarSec>
							<li><a href="javascript:void(0);" class="xlink" rel="${ctx}/web/legacyFaxInbox.jsp" >Fax Inbox</a></li>
							<li><a href="javascript:void(0);" class="xlink" rel="${ctx}/web/legacyFaxOutbox.jsp" >Fax Outbox</a></li>
						</ul>
					</div>
				</div>
</security:oscarSec>

<!-- #SYSTEM REPORTS-->
	<security:oscarSec roleName="<%=roleName$%>" objectName="_admin" rights="r" reverse="<%=false%>">
				<div class="accordion-heading">
				<a class="accordion-toggle" data-toggle="collapse" data-parent="#adminNav" href="#collapseNine">
				<bean:message key="admin.admin.SystemReports" />
				<i class="icon-chevron-right"></i>
				</a>
				</div>

				<div id="collapseNine" class="accordion-body collapse">
				<div class="accordion-inner">
				<ul>

				<security:oscarSec roleName="<%=roleName$%>"
					objectName="_admin,_admin.securityLogReport" rights="r">
					<li>
						<a href='javascript:void(0);'
						class="xlink" rel="${ctx}/admin/logReport.jsp?keyword=admin">
						<bean:message key="admin.admin.securityLogReport"/></a>
					</li>
					<%
						if(OscarProperties.getInstance().isPropertyActive("admin.show_rest_log_report"))
						{
					%>

					<li>
						<a href='javascript:void(0);'
						   class="xlink" rel="${ctx}/admin/logRestReport.jsp">
							<bean:message key="admin.admin.securityLogRestReport"/></a>
					</li>
					<%
						}
					%>
				</security:oscarSec>

				</ul>
				</div>
				</div>
	</security:oscarSec>
<!-- #SYSTEM REPORTS END-->

<!-- #INTEGRATION-->
	<security:oscarSec roleName="<%=roleName$%>" objectName="_admin" rights="r" reverse="<%=false%>">
				<div class="accordion-heading">
				<a class="accordion-toggle" data-toggle="collapse" data-parent="#adminNav" href="#collapseTen">
				<bean:message key="admin.admin.Integration" />
				<i class="icon-chevron-right"></i>
				</a>
				</div>
				<div id="collapseTen" class="accordion-body collapse">
				<div class="accordion-inner">
				<ul>
					<li>API/Connections
						<ul>
						<li>&nbsp;<a href='javascript:void(0);' class="xlink" rel="${ctx}/admin/api/clients.jsp">REST Clients</a></li>
						<li>&nbsp;<a href='javascript:void(0);' class="xlink" rel="${ctx}/admin/api/api.jsp">REST API</a></li>
						</ul>
					</li>
					<li><a href='javascript:void(0);' class="xlink" rel='${ctx}/setProviderStaleDate.do?method=viewIntegratorProperties'><bean:message key="provider.btnSetIntegratorPreferences" /></a></li>
					<li><a href='javascript:void(0);' class="xlink" rel='${ctx}/lab/CA/ALL/sendOruR01.jsp'><bean:message key="admin.admin.sendOruR01" /></a></li>
					<li><a href='javascript:void(0);' class="xlink" rel="${ctx}/admin/MyoscarConfiguration.jsp"><bean:message key="admin.admin.phrconfig"/></a></li>
					<%
						if (StringUtils.trimToNull(OscarProperties.getInstance().getProperty("oscar_myoscar_sync_component_url"))!=null)
						{
							MyOscarLoggedInInfo myOscarLoggedInInfo=MyOscarLoggedInInfo.getLoggedInInfo(session);
							if (myOscarLoggedInInfo!=null && myOscarLoggedInInfo.isLoggedIn())
							{
								%>
									<li><a href='javascript:void(0);' class="xlink" rel="${ctx}/admin/oscar_myoscar_sync_config_redirect.jsp"><bean:message key="admin.admin.oscar_phr_sync_config"/></a></li>
								<%
							}
							else
							{
								%>
									<li onclick="alert('<bean:message key="admin.admin.oscar_phr_sync_config_must_be_logged_in"/>');"><bean:message key="admin.admin.oscar_phr_sync_config"/></li>
								<%
							}
						}
					%>
					<%
						if (StringUtils.trimToNull(OscarProperties.getInstance().getProperty("oscar_myoscar_clinic_component_url"))!=null)
						{
							MyOscarLoggedInInfo myOscarLoggedInInfo=MyOscarLoggedInInfo.getLoggedInInfo(session);
							if (myOscarLoggedInInfo!=null && myOscarLoggedInInfo.isLoggedIn())
							{
								%>
									<li><a href='javascript:void(0);' class="xlink" rel="${ctx}/admin/oscar_myoscar_clinic_config_redirect.jsp"><bean:message key="admin.admin.oscar_phr_clinic_config"/></a></li>
								<%
							}
							else
							{
								%>
									<li onclick="alert('<bean:message key="admin.admin.oscar_phr_sync_config_must_be_logged_in"/>');"><bean:message key="admin.admin.oscar_phr_clinic_config"/></li>
								<%
							}
						}
					%>

					<%
						if (oscarVariables.getProperty("hsfo.loginSiteCode", "") != null && !"".equalsIgnoreCase(oscarVariables.getProperty("hsfo.loginSiteCode", "")))
									{
					%>
					<li><a href='javascript:void(0);'
						class="xlink" rel="${ctx}/admin/RecommitHSFO.do?method=showSchedule"><bean:message key="admin.admin.hsfoSubmit"/></a></li>
					<%
						}
					%>

					  <%
		                                if (oscarVariables.getProperty("hsfo2.loginSiteCode", "") != null && !"".equalsIgnoreCase(oscarVariables.getProperty("hsfo2.loginSiteCode", "")))
		                                {
		                        %>
		                        <li><a href='javascript:void(0);'
		                                class="xlink" rel="${ctx}/admin/RecommitHSFO2.do?method=showSchedule">schedule HSFO2 XML resubmit</a></li>
		                        <%
		                                }
		                        %>

					<!-- <li><a href="javascript:void(0);" class="xlink" rel="${ctx}/admin/updateDrugref.jsp" ><bean:message key="admin.admin.UpdateDrugref"/></a></li> -->

                    <%
                        if (systemPreferenceService.isPreferenceEnabled(UserProperty.INTEGRATION_KNOW2ACT_ENABLED, false)) {
                    %>
                    <li><a href="javascript:void(0);" class="xlink" rel="${ctx}/web/Know2actConfiguration.jsp" ><bean:message key="admin.admin.Know2ActConfig"/></a></li>
					<% } %>

                    <li><a  href='javascript:void(0);' class="xlink" rel="${ctx}/admin/integratorPushStatus.jsp"><bean:message key="admin.admin.integratorPush" /></a></li>
					<li><a  href='javascript:void(0);' class="xlink" rel="${ctx}/admin/born.jsp"><bean:message key="admin.admin.born" /></a></li>

					<%-- TODO: Add Polaris Check --%>
					<%--<oscar:oscarPropertiesCheck property="[ADD POLARIS PROPERTY HERE]" value="true">--%>
					<!-- #Polaris API Management -->
					<li><a href='javascript:void(0);'
						class="xlink" rel="${ctx}/demographic/polarisAPIManagementConfiguration.jsp"><bean:message key="admin.admin.PolarisAPIManagementConfiguration"/></a></li>
					<!-- #Polaris API Management END-->
					<%--</oscar:oscarPropertiesCheck>--%>
				</ul>
				</div>
				</div>
	</security:oscarSec>
<!-- #INTEGRATION END -->

<!-- #Data Management -->
	<security:oscarSec roleName="<%=roleName$%>" objectName="_admin,_admin.backup" rights="r" reverse="<%=false%>">
				<div class="accordion-heading">
				<a class="accordion-toggle" data-toggle="collapse" data-parent="#adminNav" href="#collapseTwelve">
				<bean:message key="admin.admin.DataManagement" />
				<i class="icon-chevron-right"></i>
				</a>
				</div>
				<div id="collapseTwelve" class="accordion-body collapse">
				<div class="accordion-inner">
				<ul>

					<li><a href='javascript:void(0);'
						class="xlink" rel="${ctx}/demographic/demographicExport.jsp"><bean:message key="admin.admin.DemoExport"/></a></li>
		                        <li><a href='javascript:void(0);'
						class="xlink" rel="${ctx}/demographic/demographicImport.jsp"><bean:message key="admin.admin.DemoImport"/></a></li>
					<li><a href='javascript:void(0);'
						class="xlink" rel="${ctx}/admin/demographicmergerecord.jsp"><bean:message key="admin.admin.mergeRec"/></a></li>
					<li><a href='javascript:void(0);'
						class="xlink" rel="${ctx}/admin/updatedemographicprovider.jsp"><bean:message
						key="admin.admin.btnUpdatePatientProvider" /></a></li>
				   <li><a href='javascript:void(0);'
						class="xlink" rel="${ctx}/admin/productDispensing/products.jsp"><bean:message
						key="admin.admin.btnInventory" /></a></li>
				   <% if (OscarProperties.getInstance().getProperty("NEW_CONTACTS_UI","false").equals("true")) { %>
		            	<li><a href='javascript:void(0);' class="xlink" rel="${ctx}/demographic/migrate_demographic_contacts.jsp"><bean:message key="admin.admin.migrate_contacts"/></a></li>
		            <% } %>

				</ul>
				</div>
				</div>
	</security:oscarSec>
<!-- #Data Management END-->

<!-- #Hamilton Public Health -->
<oscar:oscarPropertiesCheck property="admin.hph" value="true">
	<security:oscarSec roleName="<%=roleName$%>" objectName="_admin" rights="r" reverse="<%=false%>">
				<div class="accordion-heading">
				<a class="accordion-toggle" data-toggle="collapse" data-parent="#adminNav" href="#collapseThirteen">
				Hamilton Public Health
				<i class="icon-chevron-right"></i>
				</a>
				</div>
				<div id="collapseThirteen" class="accordion-body collapse">
				<div class="accordion-inner">
				<ul>
					<li><a href='javascript:void(0);'
						class="xlink" rel="${ctx}/admin/hamiltonPublicHealth/setProviderAvailability.jsp">
						<bean:message key="admin.admin.setProviderAvailabilities" />
					</a></li>
				</ul>
				</div>
				</div>
	</security:oscarSec>
</oscar:oscarPropertiesCheck>
<!-- #Hamilton Public Health END-->

<!-- #SHARING CENTER -->
<oscar:oscarPropertiesCheck property="sharingcenter.enabled" value="true">
	<security:oscarSec roleName="<%=roleName$%>" objectName="_admin,_admin.sharingcenter" rights="r" reverse="<%=false%>">
				<div class="accordion-heading">
				<a class="accordion-toggle" data-toggle="collapse" data-parent="#adminNav" href="#collapseThirteen">
				<bean:message key="sharingcenter.title" />
				<i class="icon-chevron-right"></i>
				</a>
				</div>

				<div id="collapseThirteen" class="accordion-body collapse">
				<div class="accordion-inner">

				<ul>
					<li><a href="${ctx}/sharingcenter/affinitydomain/manage.jsp" class="contentLink"><bean:message key="sharingcenter.affinitydomain.manage" /></a></li>
					<li><a href="${ctx}/sharingcenter/affinitydomain/clinic.jsp" class="contentLink"><bean:message key="sharingcenter.clinicinfo" /></a></li>
					<li><a href="${ctx}/sharingcenter/security/infrastructure.jsp" class="contentLink"><bean:message key="sharingcenter.security" /></a></li>
				</ul>
				</div>
				</div>
	</security:oscarSec>
</oscar:oscarPropertiesCheck>
<!-- #SHARING CENTER END -->

	<security:oscarSec roleName="<%=roleName$%>" objectName="_admin" rights="r" reverse="<%=false%>">
				<div class="accordion-heading" style="display: none">
				<a class="accordion-toggle data-toggle="collapse" data-parent="#adminNav" href="#collapseSixteen">
				<bean:message key="admin.admin.updates" />
				<i class="icon-chevron-right"></i>
				</a>
				</div>
				<div id="collapseSixteen" class="accordion-body collapse">
				<div class="accordion-inner">
				<li><a href='javascript:void(0);'
						class="xlink" rel="${ctx}/pregnancy/migrateToSplitOnArEnhancedForm.jsp"><bean:message key="admin.admin.updates.migrate_onarenhanced"/></a></li>
				</ul>
				</div>
				</div>
	</security:oscarSec>


		</div> <!-- ACCORDION GROUP END -->
		</div> <!-- ACCORDION END -->

</div><!-- SIDE END -->

<script>
function popupPage(vheight,vwidth,varpage) { //open a new popup window
	  var page = "" + varpage;
	  windowprops = "height="+vheight+",width="+vwidth+",location=no,scrollbars=yes,menubars=no,toolbars=no,resizable=yes,screenX=0,screenY=0,top=0,left=0";//360,680
	  var popup=window.open(page, "groupno", windowprops);
	  if (popup != null) {
	    if (popup.opener == null) {
	      popup.opener = self;
	    }
	    popup.focus();
	  }
}

$(document).ready(function(){
	$(".accordion-heading").click(function(e) {
		if($(this).hasClass('selected-heading')) {
			$('.accordion-heading ').removeClass('selected-heading');
		}else{
        	$('.accordion-heading').not(this).removeClass('selected-heading');
        	$(this).addClass('selected-heading');
		}
	});

	$(".xlink").click(function(e) {
		var source = $(this).attr('rel');
		$("#dynamic-content").html('<iframe id="myFrame" name="myFrame" frameborder="0" width="950" height="1000" src="'+source+'">');
		$("html, body").animate({ scrollTop: 0 }, "slow");
	});


	//using this to collapse and load items so we can have direct links to menu items
	try
	  {
		var show = getUrlVars()["show"];

		if(show!=null){
			var load = getUrlVars()["load"];
			var pageToLoad="";

			if(show!=null & load!=null){
				pageToLoad=$('.default'+show+load).attr('href');
			}else{
				pageToLoad=$('.default'+show).attr('href');
			}

		//expand request menu item
		$("#collapse"+show).collapse('show');

		//load request page or load default
		$("#dynamic-content").load(pageToLoad,
				function(response, status, xhr) {
			  		if (status == "error") {
				    	var msg = "Sorry but there was an error: ";
				    	$("#dynamic-content").html(msg + xhr.status + " " + xhr.statusText);
					}
		});

		}
	  }
	catch(err)
	  {
	  //do nothing
	  }

});

//Read a page's GET URL variables and return them as an associative array.
function getUrlVars()
{
    var vars = [], hash;
    var hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');
    for(var i = 0; i < hashes.length; i++)
    {
        hash = hashes[i].split('=');
        vars.push(hash[0]);
        vars[hash[0]] = hash[1];
    }
    return vars;
}
</script>