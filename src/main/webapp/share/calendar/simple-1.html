<html style="background-color: buttonface; color: buttontext;">

<head>
<meta http-equiv="content-type" content="text/xml; charset=utf-8" />

<title>Simple calendar setups [popup calendar]</title>

  <!-- calendar stylesheet -->
  <link rel="stylesheet" type="text/css" media="all" href="calendar-win2k-cold-1.css" title="win2k-cold-1" />

  <!-- main calendar program -->
  <script type="text/javascript" src="calendar.js"></script>

  <!-- language for the calendar -->
  <script type="text/javascript" src="lang/calendar-en.js"></script>

  <!-- the following script defines the Calendar.setup helper function, which makes
       adding a calendar a matter of 1 or 2 lines of code. -->
  <script type="text/javascript" src="calendar-setup.js"></script>

</head>

<body>

<h2>DHTML Calendar &mdash; for the impatient</h2>

      <blockquote>
        <p>
          This page lists some common setups for the popup calendar.  In
          order to see how to do any of them please see the source of this
          page.  For each example it's structured like this: there's the
          &lt;form&gt; that contains the input field, and following there is
          the JavaScript snippet that setups that form.  An example of
          <em>flat</em> calendar is available in <a
            href="simple-2.html">another page</a>.
        </p>
        <p>
          The code in this page uses a helper function defined in
          "calendar-setup.js".  With it you can setup the calendar in
          minutes.  If you're not <em>that</em> impatient, ;-) <a
          href="doc/html/reference.html">complete documenation</a> is
          available.
        </p>
      </blockquote>



<hr />

<p><b>Basic setup: one input per calendar.</b> Clicking in the input field
activates the calendar.  The date format is "%m/%d/%Y %I:%M %p".  The
calendar defaults to "single-click mode".</p>

<p>The example below has been updated to show you how to create "linked"
fields.  Basically, when some field is filled with a date, the other
is updated so that the difference between them remains one week.  The
property useful here is "onUpdate".</p>

<form action="#" method="get">
<input type="text" name="date" id="f_date_a" />
<input type="text" name="date" id="f_calcdate" />
</form>

<script type="text/javascript">
    function catcalc(cal) {
        var date = cal.date;
        var time = date.getTime()
        // use the _other_ field
        var field = document.getElementById("f_calcdate");
        if (field == cal.params.inputField) {
            field = document.getElementById("f_date_a");
            time -= Date.WEEK; // substract one week
        } else {
            time += Date.WEEK; // add one week
        }
        var date2 = new Date(time);
        field.value = date2.print("%Y-%m-%d %H:%M");
    }
    Calendar.setup({
        inputField      :    "f_date_a",   // id of the input field
        ifFormat        :    "%Y-%m-%d %H:%M",       // format of the input field
        showsTime       :    true,
        timeFormat      :    "24",
        onUpdate        :    catcalc
    });
    Calendar.setup({
        inputField     :    "f_calcdate",
        ifFormat       :    "%Y-%m-%d %H:%M",
        showsTime      :    true,
        timeFormat     :    "24",
        onUpdate       :    catcalc
    });
</script>



<hr />

<p><b>Input field with a trigger button.</b> Clicking the button activates
the calendar.  Note that this one needs double-click (singleClick parameter
is explicitely set to false).  Also demonstrates the "step" parameter
introduced in 0.9.6 (show all years in drop-down boxes, instead of every
other year as default).</p>

<form action="#" method="get">
<input type="text" name="date" id="f_date_b" /><button type="reset" id="f_trigger_b">...</button>
</form>

<script type="text/javascript">
    Calendar.setup({
        inputField     :    "f_date_b",      // id of the input field
        ifFormat       :    "%m/%d/%Y %I:%M %p",       // format of the input field
        showsTime      :    true,            // will display a time selector
        button         :    "f_trigger_b",   // trigger for the calendar (button ID)
        singleClick    :    false,           // double-click mode
        step           :    1                // show all years in drop-down boxes (instead of every other year as default)
    });
</script>



<hr />

<p><b>Input field with a trigger image.</b> Note that the Calendar.setup
function doesn't care if the trigger is a button, image, or anything else.
Also in this example we setup a different alignment, just to show how it's
done.  The input field is read-only (that is set from HTML).</p>

<form action="#" method="get">
<table cellspacing="0" cellpadding="0" style="border-collapse: collapse"><tr>
 <td><input type="text" name="date" id="f_date_c" readonly="1" /></td>
 <td><img src="img.gif" id="f_trigger_c" style="cursor: pointer; border: 1px solid red;" title="Date selector"
      onmouseover="this.style.background='red';" onmouseout="this.style.background=''" /></td>
</table>
</form>

<script type="text/javascript">
    Calendar.setup({
        inputField     :    "f_date_c",     // id of the input field
        ifFormat       :    "%B %e, %Y",      // format of the input field
        button         :    "f_trigger_c",  // trigger for the calendar (button ID)
        align          :    "Tl",           // alignment (defaults to "Bl")
        singleClick    :    true
    });
</script>



<hr />

<p><b>Hidden field, display area.</b> The calendar now puts the date into 2
elements: one is an input field of type "hidden"&mdash;so that the user
can't directly see or modify it&mdash; and one is a &lt;span&gt; element in
which the date is displayed.  Note that if the trigger is not specified the
calendar will use the displayArea (or inputField as in the first example).
The display area can have it's own format.  This is useful if, for instance,
we need to store one format in the database (thus pass it in the input
field) but we wanna show a friendlier format to the end-user.</p>

<form action="#" method="get" style="visibility: hidden">
<input type="hidden" name="date" id="f_date_d" />
</form>

<p>Your birthday:
   <span style="background-color: #ff8; cursor: default;"
         onmouseover="this.style.backgroundColor='#ff0';"
         onmouseout="this.style.backgroundColor='#ff8';"
         id="show_d"
   >Click to open date selector</span>.</p>

<script type="text/javascript">
    Calendar.setup({
        inputField     :    "f_date_d",     // id of the input field
        ifFormat       :    "%Y/%d/%m",     // format of the input field (even if hidden, this format will be honored)
        displayArea    :    "show_d",       // ID of the span where the date is to be shown
        daFormat       :    "%A, %B %d, %Y",// format of the displayed date
        align          :    "Tl",           // alignment (defaults to "Bl")
        singleClick    :    true
    });
</script>



<hr />

<p><b>Hidden field, display area, trigger image.</b> Very similar to the
previous example.  The difference is that we also have a trigger image.</p>

<form action="#" method="get" style="visibility: hidden">
<input type="hidden" name="date" id="f_date_e" />
</form>

<p>Your birthday: <span id="show_e">-- not entered --</span> <img
src="img.gif" id="f_trigger_e" style="cursor: pointer; border: 1px solid
red;" title="Date selector" onmouseover="this.style.background='red';"
onmouseout="this.style.background=''" />.</p>

<script type="text/javascript">
    Calendar.setup({
        inputField     :    "f_date_e",     // id of the input field
        ifFormat       :    "%Y/%d/%m",     // format of the input field (even if hidden, this format will be honored)
        displayArea    :    "show_e",       // ID of the span where the date is to be shown
        daFormat       :    "%A, %B %d, %Y",// format of the displayed date
        button         :    "f_trigger_e",  // trigger button (well, IMG in our case)
        align          :    "Tl",           // alignment (defaults to "Bl")
        singleClick    :    true
    });
</script>



<hr />

<p><b>Hidden field, display area.</b> Very much like the previous examples,
but we now disable some dates (all weekends, that is, Saturdays and
Sundays).</p>

<form action="#" method="get" style="visibility: hidden">
<input type="hidden" name="date" id="f_date_f" />
</form>

<p>Your birthday:
   <span style="background-color: #ff8; cursor: default;"
         onmouseover="this.style.backgroundColor='#ff0';"
         onmouseout="this.style.backgroundColor='#ff8';"
         id="show_f"
   >Click to open date selector</span>.</p>

<script type="text/javascript">
    Calendar.setup({
        inputField     :    "f_date_f",     // id of the input field
        ifFormat       :    "%Y/%d/%m",     // format of the input field (even if hidden, this format will be honored)
        displayArea    :    "show_f",       // ID of the span where the date is to be shown
        daFormat       :    "%A, %B %d, %Y",// format of the displayed date
        align          :    "Tl",           // alignment (defaults to "Bl")
        dateStatusFunc :    function (date) { // disable weekend days (Saturdays == 6 and Subdays == 0)
                              return (date.getDay() == 6 || date.getDay() == 0) ? true : false;
                            }
    });
</script>


</body>
</html>
