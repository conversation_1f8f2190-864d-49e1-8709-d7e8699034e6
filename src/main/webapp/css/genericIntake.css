body {
	font-family: Arial, Helvetica, sans-serif;
	padding: 7px;
}

body.edit {
	overflow: hidden;
}

.intakeLayoutContainer {
	width: 100%;
	height: 100%;
}

.intakeTopPane {
	font-weight: bold;
	font-size: 14px;
	text-align: center;
	padding: 5px;
}

.intakeChildPane {
	padding: 2px;
	overflow: auto;
}

.intakeBottomPane {
	padding: 5px;
}

.intakePageContainer {
	width: 100%;
	height: 100%;
}

.intakePage {
	overflow: auto;
}

.intakeSectionLabel {
	color: #000;
	font-size: 12px;
	font-weight: bold;
	background: url("images/soriaBarBg.gif") repeat-x top left #85aeec;
	border: 1px solid #84a3d1;
}

.intakeSectionContainer {
	background: #fff;
	border: 1px solid #84a3d1;
}

table.intakeTable {
	width: 99%;
	font-size: 12px;
	overflow: auto;
}

table.intakeTable td {
	padding: 2px;
}

table.intakeTable label {
	font-size: 10px;
	font-weight: bold;
}

.intakeBedCommunityProgramCell {
	width: 15%;
	vertical-align: top;
}

.intakeQuestionCell {
	background-color: #CCCCCC;
}

.intakeAnswerCell {
	font-style: italic;
}

.intakeEmptyCell {
	width: 5%;
}

.intakeInput {
	width: 100%;
}

.error {
	color: #FF0000;
	text-align: left;
}

.message {
	color: #000000;
	text-align: left;
}