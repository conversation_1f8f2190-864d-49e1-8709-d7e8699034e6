/**
 *
 * Copyright (c) 2005-2012. Centre for Research on Inner City Health, St. Michael's Hospital, Toronto. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for
 * Centre for Research on Inner City Health, St. Michael's Hospital,
 * Toronto, Ontario, Canada
 */


package com.quatro.dao.security;

import java.util.List;

import org.apache.log4j.Logger;
import org.oscarehr.util.MiscUtils;
import org.springframework.orm.hibernate3.support.HibernateDaoSupport;

import com.quatro.model.security.Secrole;

/**
 * @deprecated use jpa version instead
 */
@Deprecated
public class SecroleDao extends HibernateDaoSupport {

    private static final Logger logger = MiscUtils.getLogger();

    public List<Secrole> getRoles() {
        @SuppressWarnings("unchecked")
        List<Secrole> results = (List<Secrole>) this.getHibernateTemplate().find("from Secrole r where deleted_at IS NULL order by roleName");

        logger.debug("getRoles: # of results=" + results.size());

        return results;
    }

    public Secrole getRole(Integer id) {
        if (id == null || id.intValue() <= 0) {
            throw new IllegalArgumentException();
        }

        Secrole result = this.getHibernateTemplate().get(Secrole.class, new Long(id));

        logger.debug("getRole: id=" + id + ",found=" + (result != null));

        return result;
    }

    public void save(Secrole secrole) {
        if (secrole == null) {
            throw new IllegalArgumentException();
        }

        getHibernateTemplate().saveOrUpdate(secrole);

    }

}
