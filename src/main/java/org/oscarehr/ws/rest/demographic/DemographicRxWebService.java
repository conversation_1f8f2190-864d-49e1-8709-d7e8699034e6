/**
 * Copyright (c) 2012-2018. CloudPractice Inc. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for
 * CloudPractice Inc.
 * Victoria, British Columbia
 * Canada
 */
package org.oscarehr.ws.rest.demographic;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.oscarehr.rx.model.MedicationModel;
import org.oscarehr.rx.search.DrugCriteriaSearch;
import org.oscarehr.rx.service.MedicationService;
import org.oscarehr.security.model.Permission;
import org.oscarehr.ws.rest.AbstractServiceImpl;
import org.oscarehr.ws.rest.response.RestSearchResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.ws.rs.Consumes;
import javax.ws.rs.DefaultValue;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import java.time.LocalDate;
import java.util.List;

@Path("demographic/{demographicNo}/rx")
@Component("demographicRxWebService")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "demographic")
public class DemographicRxWebService extends AbstractServiceImpl
{
	@Autowired
	private MedicationService medicationService;

	@GET
	@Path("/")
	public RestSearchResponse<MedicationModel> searchMedications(
			@PathParam("demographicNo") Integer demographicId,
			@QueryParam("rxStartDate") LocalDate rxStartDate,
			@QueryParam("rxEndDate") LocalDate rxEndDate,
			@QueryParam("brandName") String brandName,
			@QueryParam("genericName") String genericName,
			@QueryParam("status") String status,
			@QueryParam("page")
			@DefaultValue("1")
			@Parameter(description = "Requested result page")
			Integer page,
			@QueryParam("perPage")
			@DefaultValue("10")
			@Parameter(description = "Number of results per page")
			Integer perPage,
			@QueryParam("orderBy") String orderByColumn,
			@QueryParam("desc")
			@DefaultValue("false") Boolean descending)
	{
		securityInfoManager.requireAllPrivilege(getLoggedInProviderId(), demographicId, Permission.RX_READ);

		page = validPageNo(page);
		perPage = limitedResultCount(perPage);

		DrugCriteriaSearch criteriaSearch = new DrugCriteriaSearch();
		criteriaSearch.setDemographicId(demographicId);
		criteriaSearch.setRxStartDate(rxStartDate);
		criteriaSearch.setRxEndDate(rxEndDate);
		criteriaSearch.setBrandName(brandName);
		criteriaSearch.setGenericName(genericName);
		if(status != null)
		{
			criteriaSearch.setStatus(DrugCriteriaSearch.Status.fromString(status)
					.orElseThrow(() -> new IllegalArgumentException("Invalid status value" + status)));
		}
		criteriaSearch.setPaging(page, perPage);
		criteriaSearch.setOrderBy(orderByColumn);
		if(descending)
		{
			criteriaSearch.setSortDirDescending();
		}

		List<MedicationModel> results = medicationService.medicationCriteriaSearch(criteriaSearch);
		long total = medicationService.medicationCriteriaSearchCount(criteriaSearch);
		return RestSearchResponse.successResponse(results, page, perPage, total);
	}
}
