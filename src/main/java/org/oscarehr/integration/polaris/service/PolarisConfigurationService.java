/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package org.oscarehr.integration.polaris.service;

import static oscar.util.EnvironmentUtils.testProfileIsActive;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import java.util.UUID;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.oscarehr.integration.polaris.dao.PolarisConfigurationDao;
import org.oscarehr.integration.polaris.model.PolarisConfiguration;
import org.springframework.stereotype.Service;
import oscar.OscarProperties;

@Service
@Slf4j
public class PolarisConfigurationService {

  private final PolarisConfigurationDao polarisConfigurationDao;

  public PolarisConfigurationService(PolarisConfigurationDao polarisConfigurationDao) {
    this.polarisConfigurationDao = polarisConfigurationDao;
  }

  /**
   * This method is called after the bean is constructed to set the build version in the
   * PolarisConfiguration table using the buildDateTime property in oscar_mcmaster.properties.
   */
  @PostConstruct
  public void configureBuildVersion() {
    if (testProfileIsActive()) {
      return;
    }
    var buildTag = OscarProperties.getBuildDate();
    if (buildTag == null) {
      log.warn("Build tag not configured");
      return;
    }

    val polarisConfig = polarisConfigurationDao.getPolarisConfiguration();
    if (polarisConfig == null) {
      log.warn("Polaris configuration does not exist");
      return;
    }

    try {
      val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm a", Locale.ENGLISH);
      val buildDate = LocalDateTime.parse(buildTag, formatter).toLocalDate();
      polarisConfig.setProductVersion(buildDate.toString());
      polarisConfigurationDao.merge(polarisConfig);
    } catch (Exception exception) {
      log.error("Error trying to parse and save build version.", exception);
    }
  }

  /**
   * Generate a new Polaris organization ID and save it to the database.
   *
   * @return The generated organization ID.
   * @throws IllegalStateException if the Polaris configuration does not exist.
   */
  public String generatePolarisOrganizationId() {
    val polarisConfig = polarisConfigurationDao.getPolarisConfiguration();
    if (polarisConfig == null) {
      throw new IllegalStateException("Polaris configuration does not exist");
    }

    val organizationUuid = UUID.randomUUID().toString();
    polarisConfig.setOrganizationId(organizationUuid);
    polarisConfigurationDao.merge(polarisConfig);

    return organizationUuid;
  }

  /**
   * Get the Polaris organization ID.
   *
   * @return The Polaris organization ID, or null if it does not exist.
   */
  public String getPolarisUuid() {
    val polarisConfig = polarisConfigurationDao.getPolarisConfiguration();

    if (polarisConfig != null && StringUtils.isNotBlank(polarisConfig.getOrganizationId())) {
      return polarisConfig.getOrganizationId();
    } else {
      return null;
    }
  }

  public String getAdminPolarisFhirEndpoint() {
    val polarisConfig = polarisConfigurationDao.getPolarisConfiguration();

    if (polarisConfig == null || StringUtils.isBlank(polarisConfig.getAdminPolarisFhirEndpoint())) {
      return null;
    }

    return polarisConfig.getAdminPolarisFhirEndpoint();
  }

  public boolean isCparEnabled() {
    val polarisConfig = polarisConfigurationDao.getPolarisConfiguration();

    return polarisConfig != null && polarisConfig.getIsCparEnabled();
  }

  public void setCparEnabled(boolean isEnabled) {
    val polarisConfig = polarisConfigurationDao.getPolarisConfiguration();

    if (polarisConfig == null) {
      throw new IllegalStateException("Polaris configuration does not exist");
    }

    polarisConfig.setIsCparEnabled(isEnabled);
    polarisConfigurationDao.merge(polarisConfig);
  }

  public PolarisConfiguration getPolarisConfiguration() {
    return polarisConfigurationDao.getPolarisConfiguration();
  }

  public void savePolarisConfiguration(PolarisConfiguration polarisConfiguration) {
    polarisConfigurationDao.merge(polarisConfiguration);
  }

  /**
   * Get the CPAR component URL.
   *
   * @return The CPAR component URL, or the default URL if it does not exist.
   */
  public String getCparComponentUrl() {
    val polarisConfig = polarisConfigurationDao.getPolarisConfiguration();
    if (polarisConfig == null || StringUtils.isBlank(polarisConfig.getCparComponentUrl())) {
      return null;
    }
    return polarisConfig.getCparComponentUrl();
  }

  /**
   * Set the CPAR component URL.
   *
   * @param url The CPAR component URL to set.
   * @throws IllegalStateException if the Polaris configuration does not exist.
   */
  public void setCparComponentUrl(String url) {
    val polarisConfig = polarisConfigurationDao.getPolarisConfiguration();

    if (polarisConfig == null) {
      throw new IllegalStateException("Polaris configuration does not exist");
    }

    polarisConfig.setCparComponentUrl(url);
    polarisConfigurationDao.merge(polarisConfig);
  }
}
