/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package org.oscarehr.integration.polaris.service;

import lombok.val;
import org.jsoup.helper.StringUtil;
import org.oscarehr.integration.polaris.PolarisApiConnectionException;
import org.oscarehr.integration.polaris.PolarisApiConnector;
import org.oscarehr.integration.polaris.model.CparUiAttributes;
import org.oscarehr.integration.polaris.dao.PolarisConfigurationDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Service to provide CPAR UI attributes for both Juno UI and classic UI JSP pages.
 *
 * <p>This service retrieves configuration values from the database and system preferences to
 * populate the attributes required by the CPAR UI component. It handles organization identifiers,
 * Medplum credentials, and patient identifiers.
 */
@Service
public class CparUiService {

  @Autowired
  private PolarisConfigurationDao polarisConfigurationDao;

  @Autowired
  private PolarisApiConnector polarisApiConnector;

  /**
   * Retrieves CPAR UI attributes for a specific demographic.
   *
   * <p>This method gathers all the necessary attributes required by the CPAR UI component,
   * including organization identifiers, Medplum base URL, credentials, and patient identifiers. It
   * retrieves configuration values from the database and system preferences.
   *
   * <p>The method performs the following steps:
   *
   * <ol>
   *   <li>Checks if CPAR integration is enabled
   *   <li>Retrieves the organization identifier from the database
   *   <li>Retrieves Medplum base URL and credentials from system preferences
   *   <li>Creates a new CparUiAttributes object with all the gathered values
   *   <li>Validates that all required fields are present
   * </ol>
   *
   * <p>The CparUiAttributes constructor automatically sets default values and computes the patient
   * identifier system based on the organization identifier.
   *
   * @return A CparUiAttributes object containing all the necessary attributes for the CPAR UI
   *     component
   * @throws IllegalStateException if CPAR integration is not enabled or if any required fields are
   *     missing
   */
  public CparUiAttributes getCparUiAttributes() throws PolarisApiConnectionException {
    val polarisConfig = polarisConfigurationDao.getPolarisConfiguration();
    if (polarisConfig == null) {
      throw new IllegalStateException("Polaris configuration not found");
    }

    if (!polarisConfig.getIsCparEnabled()) {
      throw new UnsupportedOperationException("CPAR integration is not enabled");
    }

    String organizationId;
    if (!StringUtil.isBlank(polarisConfig.getOrganizationId())) {
      organizationId = polarisConfig.getOrganizationId();
    } else {
      throw new IllegalStateException("Organization identifier is required");
    }
    val medplumBaseUrl = polarisConfig.getAdminPolarisBasePath();
    val componentUrl = polarisConfig.getCparComponentUrl();

    if (StringUtil.isBlank(medplumBaseUrl)) {
      throw new IllegalStateException("Medplum base URL is required");
    }
    if (StringUtil.isBlank(componentUrl)) {
      throw new IllegalStateException("CPAR component URL is required");
    }

    val accessToken = polarisApiConnector.getLatestToken();

    return new CparUiAttributes(
        organizationId,
        medplumBaseUrl,
        accessToken,
        componentUrl
    );
  }
}
