/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package org.oscarehr.integration.polaris;

import static org.oscarehr.integration.polaris.PolarisUtils.isValidUrl;

import health.apps.common.extension.DomainResource_ToJsonKt;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.hl7.fhir.r4.model.DomainResource;
import org.jetbrains.annotations.Nullable;
import org.oscarehr.integration.polaris.service.PolarisConfigurationService;
import org.oscarehr.util.SpringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import oscar.util.RESTClient;

/**
 * PolarisApiConnector is responsible for handling communication with the Polaris API. It will
 * automatically manage authentication, token fetching, and token validation.
 */
@Slf4j
@Component
public class PolarisApiConnector extends RESTClient {

  private PolarisCredentialStoreWrapper credentialStoreWrapper;
  protected String restApiUrl;
  protected String baseUrl;

  public PolarisApiConnector() {
    this(getConfigurationService());
  }

  private static PolarisConfigurationService getConfigurationService() {
    try {
      return SpringUtils.getBean(PolarisConfigurationService.class);
    } catch (Exception exception) {
      log.debug("PolarisConfigurationService not available yet during Spring context initialization", exception);
      return null;
    }
  }

    /** Constructs a new PolarisApiConnector instance. */
  public PolarisApiConnector(PolarisConfigurationService polarisConfigurationService) {
    this(polarisConfigurationService, new DefaultPolarisCredentialStoreWrapper());
  }

  /** Constructs a new PolarisApiConnector instance with a custom credential store wrapper. */
  public PolarisApiConnector(PolarisConfigurationService polarisConfigurationService, PolarisCredentialStoreWrapper credentialStoreWrapper) {
    this.credentialStoreWrapper = credentialStoreWrapper;

    if (polarisConfigurationService == null) {
      return;
    }

    val config = polarisConfigurationService.getPolarisConfiguration();
    try {
      restApiUrl = config.getAdminPolarisFhirEndpoint();
      baseUrl = config.getAdminPolarisBasePath();
      if (restApiUrl == null || restApiUrl.isEmpty()) {
        throw new PolarisApiConnectionException("Polaris API URL not set");
      }

      if (!isValidUrl(restApiUrl)) {
        throw new PolarisApiConnectionException("Invalid Polaris API URL");
      }
    } catch (PolarisApiConnectionException e) {
      log.warn("PolarisApiConnector failed to construct: {}", e.getMessage());
      restApiUrl = "";
    }

    this.setErrorHandler(new PolarisApiErrorHandler());
  }

  /**
   * Makes an HTTP request to the FHIR API using the specified method.
   *
   * <p>Supported HTTP methods include "POST", "GET", and "PUT". "DELETE" is not currently supported
   * and will throw an exception.
   *
   * <p>For "POST" and "PUT" requests, a non-null {@code fhirObject} is required.
   *
   * @param path the relative path of the FHIR resource (e.g. "/Patient/123")
   * @param queryParams the query parameters to append to the request URL
   * @param method the HTTP method to use: "POST", "GET", or "PUT"
   * @param fhirObject the FHIR resource to send (required for "POST" and "PUT"; ignored for "GET")
   * @param responseType the expected response class type for deserialization
   * @param <T> the type of FHIR resource being sent (must extend {@link
   *     org.hl7.fhir.r4.model.DomainResource})
   * @param <U> the type of object expected in the response
   * @return the response deserialized into an instance of {@code responseType}
   * @throws PolarisApiConnectionException if an error occurs during the request, or if the method
   *     is unsupported or {@code fhirObject} is null when required
   * @throws MalformedURLException if the constructed URL is invalid
   * @throws URISyntaxException if the constructed URI is invalid
   */
  public <T extends DomainResource, U> U makeFhirRequest(
      final String path,
      final Map<String, String> queryParams,
      // Supported Methods: "POST", "GET", "PUT", or "DELETE"
      // TODO: consider Enum?
      final String method,
      // NOTE: only required for PUT/POST
      @Nullable final T fhirObject,
      final Class<U> responseType)
      throws PolarisApiConnectionException, MalformedURLException, URISyntaxException {
    String url = PolarisUtils.buildUrl(restApiUrl, path, queryParams);
    HttpHeaders headers = getAuthorizationBearerHeadersWithFetch();

    headers.setContentType(MediaType.APPLICATION_JSON);

    if ("POST".equalsIgnoreCase(method)) {
      if (fhirObject == null) {
        throw new PolarisApiConnectionException("Fhir Object not set");
      }

      headers.setContentType(MediaType.APPLICATION_JSON);
      return doPost(url, headers, DomainResource_ToJsonKt.toJson(fhirObject), responseType);
    } else if ("GET".equalsIgnoreCase(method)) {
      return doGet(url, headers, responseType);
    } else if ("PUT".equalsIgnoreCase(method)) {
      if (fhirObject == null) {
        throw new PolarisApiConnectionException("Fhir Object not set");
      }

      return doPut(url, headers, DomainResource_ToJsonKt.toJson(fhirObject), responseType);
    } else if ("DELETE".equalsIgnoreCase(method)) {
      // TODO: why doesn't RESTClient have a doDelete function??
      throw new PolarisApiConnectionException("Delete method not supported");
    } else {
      throw new IllegalArgumentException("Unsupported HTTP method: " + method);
    }
  }

  /**
   * Fetches authentication tokens (access token and ID token) using the client credentials OAuth2
   * grant type, and stores them in the PolarisCredentialStore.
   *
   * @throws PolarisCredentialStoreException if an error occurs while retrieving or storing the
   *     credentials.
   */
  public void fetchAuthTokens() throws PolarisCredentialStoreException {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

    MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
    formData.set("grant_type", "client_credentials");
    formData.set("client_id", PolarisCredentialStore.getClientId());
    formData.set("client_secret", PolarisCredentialStore.getClientSecret());

    PolarisAuthTokenResponseBody response =
        doPost(
            PolarisCredentialStore.getTokenUrl(),
            headers,
            formData,
            PolarisAuthTokenResponseBody.class);

    // TODO: when setting token verify EVERYTHING, issuer, public key, etc
    PolarisCredentialStore.setAccessToken(response.accessToken);
    PolarisCredentialStore.setIdToken(response.idToken);
    PolarisCredentialStore.setExpiresIn(response.expiresIn);
  }

  /**
   * Validates whether the currently stored token is valid by attempting to call the user info
   * endpoint.
   *
   * @return true if the token is valid; false otherwise.
   * @throws PolarisApiConnectionException if an error occurs while communicating with the Polaris
   *     API.
   * @throws PolarisCredentialStoreException if there is an issue retrieving the stored credentials.
   */
  public Boolean isTokenValid()
      throws PolarisApiConnectionException, PolarisCredentialStoreException {
    String url = buildUrl(DEFAULT_PROTOCOL, PolarisCredentialStore.getUserInfoEndpoint());
    Map<String, String> response = doGet(url, getAuthorizationBearerHeaders(), Map.class);

    // if we can get a response, then it is valid
    return response.get("sub") != null;
  }

  /**
   * Checks if there is a valid token, & sends it over
   * Attempts to get a new token if necessary
   * @return A Valid Token
   * @throws PolarisApiConnectionException if there is an issue with the configuration
   */
  public String getLatestToken() throws PolarisApiConnectionException {
    try {
      if (credentialStoreWrapper.getAccessToken() == null || !isTokenValid()) {
        fetchAuthTokens();
      }

      return credentialStoreWrapper.getAccessToken();
    } catch (Exception exception) {
      log.error("Error refreshing or loading OAuth credentials", exception);
      throw new PolarisApiConnectionException("Error refreshing or loading OAuth credentials");
    }
  }

  /**
   * Retrieves the Polaris configuration from the
   * /.well-known/openid-configuration endpoint.
   *
   * @return the Polaris configuration as a PolarisConfiguration object
   */
  public PolarisEndpointConfiguration getConfigurationEndpoints() {
    try {
      val configurationUrl = PolarisUtils.buildUrl(baseUrl, "/.well-known/openid-configuration",
          null);
      return doGet(configurationUrl, PolarisEndpointConfiguration.class);
    } catch (Exception e) {
      log.error("Error retrieving Polaris configuration", e);
      return null;
    }
  }

  /**
   * Returns HTTP headers with a Bearer Authorization token. If the token is invalid or expired,
   * this method will attempt to fetch a new token and then return the headers.
   *
   * <p>NOTE: Use this one over the function without the fetch as this handles token expiration i.e.
   * via fetch NOTE: This function uses the other non-fetch variant inside.
   *
   * @return HttpHeaders with the Authorization Bearer token.
   * @throws PolarisApiConnectionException if an error occurs while loading or refreshing the token.
   */
  protected HttpHeaders getAuthorizationBearerHeadersWithFetch()
      throws PolarisApiConnectionException {
    try {
      getLatestToken();

      return getAuthorizationBearerHeaders();
    } catch (Exception exception) {
      log.error("Error refreshing or loading OAuth credentials", exception);
      throw new PolarisApiConnectionException("Error refreshing or loading OAuth credentials");
    }
  }

  /**
   * Returns HTTP headers with a Bearer Authorization token using the current access token in the
   * credential store.
   *
   * <p>NOTE: This function without the fetch, is only be used for testing if the tokens are valid
   * as using the fetch variant will result in an infinite loop. This is because the fetch only
   * fetches if the token is invalid. This function is used in the fetch variant too but only after
   * the valid token check.
   *
   * @return HttpHeaders with the Authorization Bearer token.
   * @throws PolarisApiConnectionException if the access token is null or cannot be loaded.
   */
  protected HttpHeaders getAuthorizationBearerHeaders() throws PolarisApiConnectionException {
    try {
      String accessToken = credentialStoreWrapper.getAccessToken();
      if (accessToken == null) {
        throw new PolarisApiConnectionException("Access token is null");
      }

      HttpHeaders headers = new HttpHeaders();
      headers.set(HttpHeaders.AUTHORIZATION, String.format("Bearer %s", accessToken));

      return headers;
    } catch (Exception exception) {
      throw new PolarisApiConnectionException("Error refreshing or loading OAuth credentials");
    }
  }
}
