/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package org.oscarehr.integration.polaris.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.oscarehr.common.model.AbstractModel;

@Entity
@Table(name = "polaris_configuration")
@Getter
public class PolarisConfiguration extends AbstractModel<String> {

  @Id
  private Integer id;

  @Column(name = "vendor_name")
  private String vendorName;

  @Column(name = "product_name")
  private String productName;

  @Column(name = "product_version")
  @Setter
  private String productVersion;

  private String type;

  @Column(name = "organization_id")
  @Setter
  private String organizationId;

  @Column(name = "is_cpar_enabled")
  @Setter
  private Boolean isCparEnabled = false;

  @Column(name = "admin_polaris_fhir_endpoint")
  @Setter
  private String adminPolarisFhirEndpoint;

  @Column(name = "admin_polaris_fhir_client_id")
  @Setter
  private String adminPolarisFhirClientId;

  @Column(name = "admin_polaris_fhir_client_secret")
  @Setter
  private String adminPolarisFhirClientSecret;

  @Column(name = "admin_polaris_base_path")
  @Setter
  private String adminPolarisBasePath;

  @Column(name = "cpar_component_url")
  @Setter
  private String cparComponentUrl;

  @Override
  public String getId() {
    return String.valueOf(id);
  }
}
