/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package org.oscarehr.integration.polaris.model;

import lombok.Getter;

@Getter
public class CparUiAttributes {

  public static final String ORGANIZATION_IDENTIFIER_SYSTEM =
      "https://fhir.apps.health/NamingSystem/organization-identifier";
  public static final String DEFAULT_THEME = "juno";
  public static final String PATIENT_IDENTIFIER_SYSTEM_TEMPLATE =
      "https://fhir.apps.health/%s/NamingSystem/patient-identifier";

  private final String organizationIdentifierSystem;
  private final String organizationIdentifierValue;
  private final String medplumBaseUrl;
  private final String theme;
  private final String medplumAccessToken;
  private String identifierSystem;
  private String componentUrl;

  /**
   * Constructor with required fields and default values for others.
   *
   * @param organizationIdentifierValue The organization identifier value
   * @param medplumBaseUrl The Medplum base URL
   * @param accessToken The Medplum access token
   * @param componentUrl The CPAR component URL
   */
  public CparUiAttributes(
      final String organizationIdentifierValue,
      final String medplumBaseUrl,
      final String accessToken,
      final String componentUrl) {
    this.organizationIdentifierSystem = ORGANIZATION_IDENTIFIER_SYSTEM;
    this.organizationIdentifierValue = organizationIdentifierValue;
    this.medplumBaseUrl = medplumBaseUrl;
    this.medplumAccessToken = accessToken;
    this.theme = DEFAULT_THEME;
    this.componentUrl = componentUrl;
    if (organizationIdentifierValue != null) {
      this.identifierSystem = String.format(
          PATIENT_IDENTIFIER_SYSTEM_TEMPLATE,
          organizationIdentifierValue
      );
    }
  }
}
