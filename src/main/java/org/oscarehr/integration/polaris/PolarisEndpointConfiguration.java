/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package org.oscarehr.integration.polaris;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.stream.Stream;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PolarisEndpointConfiguration {

  @JsonProperty("authorization_endpoint")
  String authorizationEndpoint;

  @JsonProperty("token_endpoint")
  String tokenEndpoint;

  @JsonProperty("userinfo_endpoint")
  String userInfoEndpoint;

  @JsonProperty("jwks_uri")
  String jwksUri;

  public boolean isAnyBlank() {
    return Stream
        .of(authorizationEndpoint, tokenEndpoint, userInfoEndpoint, jwksUri)
        .anyMatch(StringUtils::isBlank);
  }
}
