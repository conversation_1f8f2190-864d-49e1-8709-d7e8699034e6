/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package org.oscarehr.integration.polaris;

import static org.oscarehr.integration.polaris.PolarisUtils.requiredVarSet;
import static oscar.util.EnvironmentUtils.testProfileIsActive;

import javax.annotation.PostConstruct;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.oscarehr.common.encryption.StringEncryptor;
import org.oscarehr.integration.polaris.model.PolarisConfiguration;
import org.oscarehr.integration.polaris.service.PolarisConfigurationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Manages credentials and configuration values required to authenticate and communicate with the
 * Polaris OAuth2/OpenID Connect provider.
 *
 * <p>This class is responsible for retrieving client credentials, validating configuration
 * endpoints, and storing access/id tokens for reuse.
 */
@Slf4j
@Component
public class PolarisCredentialStore {

  /** Polaris client ID used for authentication. */
  private static String clientId;

  /** Polaris client secret used for authentication. */
  private static String clientSecret;

  /** OAuth2 authorization endpoint URL. */
  private static String authorizationUrl;

  /** OAuth2 token endpoint URL. */
  private static String tokenUrl;

  /** OpenID Connect user info endpoint URL. */
  private static String userInfoUrl;

  /** JSON Web Key Set URI for validating ID tokens. */
  private static String jwksUri;

  /** The access token obtained from Polaris after authentication. */
  @Getter @Setter private static String accessToken;

  /** The ID token obtained from Polaris after authentication. */
  @Getter @Setter private static String idToken;

  /** The number of seconds before the access token expires. */
  @Getter @Setter private static int expiresIn;

  private static PolarisApiConnector polarisApiConnector;

  private static PolarisConfigurationService polarisConfigurationService;

  @Autowired
  public PolarisCredentialStore(
      PolarisApiConnector polarisApiConnector,
      PolarisConfigurationService polarisConfigurationService
  ) {
    PolarisCredentialStore.polarisApiConnector = polarisApiConnector;
    PolarisCredentialStore.polarisConfigurationService = polarisConfigurationService;
  }

  @PostConstruct
  public void configurePolaris() {
    if (testProfileIsActive()) {
      return;
    }
    try {
      PolarisCredentialStore.init();
    } catch (PolarisCredentialStoreException exception) {
      log.error(
          "Polaris config could not be completed successfully, systems may not work properly.",
          exception);
    }
  }

  /**
   * Initializes the credential store
   *
   * @throws PolarisCredentialStoreException if any required configuration is missing or invalid
   */
  public static void init() throws PolarisCredentialStoreException {
    val polarisConfiguration = polarisConfigurationService.getPolarisConfiguration();
    if (polarisConfiguration == null) {
      throw new PolarisCredentialStoreException("Polaris configuration not found");
    }
    try {
      setupClientCredentials(polarisConfiguration);
      retrieveConfigurationUrls();
    } catch (PolarisCredentialStoreException e) {
      log.warn("Polaris credential store could not be initialized", e);
    }
  }

  /**
   * Initializes the Polaris client credentials from the provided configuration.
   *
   * <p>This method retrieves the FHIR client ID and client secret from the
   * given {@link PolarisConfiguration} object and stores them for later use.
   * If either credential is missing, a {@link PolarisCredentialStoreException}
   * is thrown to indicate improper configuration.
   *
   * @param polarisConfiguration the configuration object containing Polaris client credentials
   * @throws PolarisCredentialStoreException if the client ID or client secret is missing
   */
  public static void setupClientCredentials(
      final PolarisConfiguration polarisConfiguration)
      throws PolarisCredentialStoreException {
    clientId = polarisConfiguration.getAdminPolarisFhirClientId();
    val encryptedClientSecret = polarisConfiguration.getAdminPolarisFhirClientSecret();

    if (clientId == null || encryptedClientSecret == null) {
      throw new PolarisCredentialStoreException("Client ID and/or client secret are missing");
    }

    clientSecret = StringEncryptor.decrypt(encryptedClientSecret);
  }

  /**
   * Retrieves the configuration URLs from the Polaris server.
   *
   * <p>This method fetches the OpenID Connect configuration from the Polaris server and extracts
   * the authorization, token, user info, and JWKS URIs. It constructs the URLs based on the
   * provided base path in the {@link PolarisConfiguration}.
   *
   * @throws PolarisCredentialStoreException if the configuration cannot be retrieved
   */
  public static void retrieveConfigurationUrls() throws PolarisCredentialStoreException {
    val configuration = polarisApiConnector.getConfigurationEndpoints();
    if (configuration == null || configuration.isAnyBlank()) {
      throw new PolarisCredentialStoreException("Failed to retrieve Polaris configuration");
    }
    authorizationUrl = configuration.getAuthorizationEndpoint();
    jwksUri = configuration.getJwksUri();
    tokenUrl = configuration.getTokenEndpoint();
    userInfoUrl = configuration.getUserInfoEndpoint();
  }

  /**
   * Clears any previously stored authentication tokens.
   *
   * <p>This method resets the access token, ID token, and token expiration time.
   * It is typically used during logout or reinitialization of the authentication state.
   */
  public static void reset() {
    accessToken = null;
    idToken = null;
    expiresIn = 0;
  }

  protected static String getClientId() throws PolarisCredentialStoreException {
    if (!requiredVarSet(clientId)) {
      throw new PolarisCredentialStoreException("Missing Polaris client ID");
    }

    return clientId;
  }

  protected static String getClientSecret() throws PolarisCredentialStoreException {
    if (!requiredVarSet(clientSecret)) {
      throw new PolarisCredentialStoreException("Missing Polaris client secret");
    }

    return clientSecret;
  }

  protected static String getTokenUrl() {
    return getEndpointUrl(tokenUrl);
  }

  protected static String getUserInfoEndpoint() {
    return getEndpointUrl(userInfoUrl);
  }

  protected static String getJwksUri() {
    return getEndpointUrl(jwksUri);
  }

  protected static String getAuthorizationUrl() {
    return getEndpointUrl(authorizationUrl);
  }

  private static String getEndpointUrl(final String endpointUrl) {
    if (!requiredVarSet(endpointUrl)) {
      try {
        retrieveConfigurationUrls();
      } catch (PolarisCredentialStoreException e) {
        log.error("Failed to retrieve Polaris configuration URL endpoints", e);
        return null;
      }
    }
    return endpointUrl;
  }
}
