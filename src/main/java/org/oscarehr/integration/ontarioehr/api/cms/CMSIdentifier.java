/**
 * Copyright (c) 2012-2018. CloudPractice Inc. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for
 * CloudPractice Inc.
 * Victoria, British Columbia
 * Canada
 */
package org.oscarehr.integration.ontarioehr.api.cms;

import lombok.Getter;

import java.util.Optional;

/**
 * OntarioContextManagement Identifiers
 * See <a href="https://simplifier.net/guide/OntarioContextManagement/Identifiers?version=current">Identifiers</a>
 */
public enum CMSIdentifier
{
	CA_ON_LICENSE_MIDWIFE_ORG("CMO assigned identifiers to midwifery clinics",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-midwife-org"),
	CA_ON_REGISTRATION_AUDIOLOGIST_SPEECH_LANGUAGE_PATHOLOGIST("College of Audiologists and Speech-Language Pathologists of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-audiologist-speech-language-pathologist"),
	CA_ON_REGISTRATION_CHIROPRODIST("College of Chiropodists of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-chiropodist"),
	CA_ON_LICENSE_CHIROPRACTOR("College of Chiropractors of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-chiropractor"),
	CA_ON_LICENSE_DENTAL_HYGIENIST("College of Dental Hygienists of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-dental-hygienist"),
	CA_ON_REGISTRATION_DENTAL_TECH("College of Dental Technologists of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-dental-technologist"),
	CA_ON_LICENSE_DENTURIST("College of Denturists of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-denturist"),
	CA_ON_LICENSE_DIETITIAN("College of Dietitians of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-dietitian"),
	CA_ON_REGISTRATION_HOMEOPATH("College of Homeopaths of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-homeopath"),
	CA_ON_REGISTRATION_KINESIOLOGIST("College of Kinesiologists of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-kinesiologist"),
	CA_ON_THERAPIST("College of Massage Therapist of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-massage-therapist"),
	CA_ON_REGISTRATION_MEDICAL_LAB_TECH("College of Medical Laboratory Technologists of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-medical-laboratory-technologist"),
	CA_ON_REGISTRATION_RADIATION_TECH("College of Medical Radiation Technologists of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-medical-radiation-techologist"),
	CA_ON_LICENSE_MIDWIFE("College of Midwives of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-midwife"),
	CA_ON_LICENSE_NATUROPATH("College of Naturopaths of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-naturopath"),
	CA_ON_LICENSE_NURSE("College of Nurses of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-nurse"),
	CA_ON_REGISTRATION_OCCUPATIONAL_THERAPIST("College of Occupational Therapists of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-occupational-therapist"),
	CA_ON_LICENSE_OPTICIAN("College of Opticians of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-optician"),
	CA_ON_LICENSE_OPTOMETRIST("College of Optometrists of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-optometrist"),
	CA_ON_LICENSE_PHYSICIAN("College of Physicians and Surgeons of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-physician"),
	CA_ON_LICENSE_PHYSIOTHERAPIST("College of Physiotherapists of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-physiotherapist"),
	CA_ON_REGISTRATION_PSYCHOLOGIST("College of Psychologists of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-psychologist"),
	CA_ON_REGISTRATION_PSYCHOTHERAPIST("College of Registered Psychotherapists of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-psychotherapist"),
	CA_ON_REGISTRATION_RESPIRATORY_THERAPIST("College of Respiratory Therapists of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-respiratory-therapist"),
	CA_ON_REGISTRATION_TRADITIONAL_CHINESE_MED_ACUPUNCTURIST("College of Traditional Chinese Medicine Practitioners and Acupuncturists of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-traditional-chinese-medicine-acupuncturist"),
	CA_ON_PATIENT_HCN("Ontario, Canada Personal Health Number",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-patient-hcn"),
	CA_ON_PANORAMA_IMMUNIZATION_ID("Ontario, Canada Panorama Immunization ID (OIID)",
			"http://ehealthontario.ca/fhir/NamingSystem/ca-on-panorama-immunization-id"),
	CA_ON_LICENSE_PHARMACIST("Ontario College of Pharmacists",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-pharmacist"),
	CA_ON_LICENSE_PHARMACIST_ORG("Ontario College of Pharmacists - Org",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-license-pharmacist-org"),
	CA_ON_REG_SOCIAL_WORKER_SOCIAL_SERVICE_WORKER("Ontario College of Social Workers and Social Service Workers",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-social-worker-social-service-worker"),
	CA_ON_HEALTH_CARE_FACILITY_ID("Ontario Organization Identifiers assigned by the Ministry of Health",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-health-care-facility-id"),
	CA_ON_LAB_LICENSE("Ontario Organization Identifiers assigned by the Ministry of Health lab branch (LAB)",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-lab-license"),
	CA_ON_REGISTRATION_OSTEOPATH("Osteopathic College of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-registration-osteopath"),
	CA_ON_DENTAL_SURGEON("Royal College of Dental Surgeons of Ontario",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-dental-surgeon"),
	CA_ON_PROVIDER_UPI("Unique Provider Identifier",
			"https://fhir.infoway-inforoute.ca/NamingSystem/ca-on-provider-upi");

	@Getter
	private final String uri;
	@Getter
	private final String description;

	CMSIdentifier(String description, String uri)
	{
		this.uri = uri;
		this.description = description;
	}

	public static Optional<CMSIdentifier> findByUri(String uri)
	{
		for (CMSIdentifier identifier : CMSIdentifier.values())
		{
			if(identifier.getUri().equals(uri))
			{
				return Optional.of(identifier);
			}
		}
		return Optional.empty();
	}
}
