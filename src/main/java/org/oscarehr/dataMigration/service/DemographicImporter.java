/**
 * Copyright (c) 2012-2018. CloudPractice Inc. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for
 * CloudPractice Inc.
 * Victoria, British Columbia
 * Canada
 */
package org.oscarehr.dataMigration.service;

import org.oscarehr.common.io.GenericFile;
import org.oscarehr.dataMigration.exception.InvalidImportFileException;
import org.oscarehr.dataMigration.model.PatientRecord;
import org.oscarehr.dataMigration.pref.ExportPreferences;

import java.io.IOException;
import java.util.List;

public interface DemographicImporter
{
	enum MERGE_STRATEGY {
		SKIP,
		MERGE,
	}

	void verifyFileFormat(GenericFile importFile) throws InvalidImportFileException;

	PatientRecord importDemographic(GenericFile importFile) throws Exception;

	List<GenericFile> getAdditionalFiles(ExportPreferences preferences) throws IOException;
}
