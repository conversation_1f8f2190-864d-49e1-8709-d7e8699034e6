/**
 * Copyright (c) 2012-2018. CloudPractice Inc. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for
 * CloudPractice Inc.
 * Victoria, British Columbia
 * Canada
 */
package org.oscarehr.ticklers.search;

import lombok.Data;
import org.oscarehr.common.search.AbstractCriteriaQuerySearch;
import org.oscarehr.demographic.entity.Demographic;
import org.oscarehr.demographic.entity.Demographic_;
import org.oscarehr.provider.model.ProviderData;
import org.oscarehr.provider.model.ProviderData_;
import org.oscarehr.ticklers.entity.Tickler;
import org.oscarehr.ticklers.entity.Tickler_;
import oscar.util.ConversionUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

@Data
public class TicklerCriteriaSearch extends AbstractCriteriaQuerySearch<Tickler>
{
	public enum SortMode
	{
		UpdateDate,
		DemographicName,
		Creator,
		ServiceDate,
		Priority,
		TaskAssignedTo,
		Status,
		Message
	}

	// fields here
	private LocalDate startDate;
	private LocalDate endDate;
	private String creator;
	private String taskAssignedTo;
	private String programId;
	private Integer demographicNo;
	private String message;
	private Tickler.PRIORITY priority;
	private Tickler.STATUS status;
	private String mrp;

	private SortMode sortMode = SortMode.UpdateDate;

	@Override
	public List<Predicate> setCriteriaProperties(CriteriaBuilder criteriaBuilder, CriteriaQuery<?> criteriaQuery, Root<Tickler> ticklerRoot)
	{
		List<Predicate> predicateList = new ArrayList<>();

		// set search filters
		if (getDemographicNo() != null)
		{
			predicateList.add(criteriaBuilder.equal(ticklerRoot.get(Tickler_.DEMOGRAPHIC_NO), getDemographicNo()));
		}

		if (getCreator() != null)
		{
			predicateList.add(criteriaBuilder.equal(ticklerRoot.get(Tickler_.CREATOR), getCreator()));
		}

		if (getTaskAssignedTo() != null)
		{
			predicateList.add(criteriaBuilder.equal(ticklerRoot.get(Tickler_.TASK_ASSIGNED_TO), getTaskAssignedTo()));
		}

		if (getPriority() != null)
		{
			predicateList.add(criteriaBuilder.equal(ticklerRoot.get(Tickler_.PRIORITY), getPriority()));
		}

		if (getMessage() != null)
		{
			predicateList.add(criteriaBuilder.equal(ticklerRoot.get(Tickler_.MESSAGE), getMessage()));
		}

		if (getProgramId() != null)
		{
			predicateList.add(criteriaBuilder.equal(ticklerRoot.get(Tickler_.PROGRAM_ID), getProgramId()));
		}

		if (getStatus() != null)
		{
			predicateList.add(criteriaBuilder.equal(ticklerRoot.get(Tickler_.STATUS), getStatus()));
		}

		if (getMrp() != null)
		{
			// join demographic and only return the result if the assigned mrp matches
			Join<Tickler, Demographic> demographicJoin = ticklerRoot.join(Tickler_.DEMOGRAPHIC);
			predicateList.add(criteriaBuilder.equal(demographicJoin.get(Demographic_.PROVIDER_NO), getMrp()));
		}

		/* Date searching:
		 * The dates are stored in the database as UTC, so we need to convert the LocalDate to a ZonedDateTime in UTC */
		if(getEndDate() != null)
		{
			predicateList.add(criteriaBuilder.lessThan(ticklerRoot.get(Tickler_.SERVICE_DATE),
					ConversionUtils.toLegacyDateTime(getEndDate().plusDays(1).atStartOfDay(ZoneId.systemDefault()))));
		}
		if(getStartDate() != null)
		{
			predicateList.add(criteriaBuilder.greaterThanOrEqualTo(ticklerRoot.get(Tickler_.SERVICE_DATE),
					ConversionUtils.toLegacyDateTime(getStartDate().atStartOfDay(ZoneId.systemDefault()))));
		}

		applyColumnOrdering(criteriaBuilder, criteriaQuery, ticklerRoot);
		return predicateList;
	}

	@Override
	protected void applyColumnOrdering(CriteriaBuilder criteriaBuilder, CriteriaQuery<?> criteriaQuery, Root<Tickler> ticklerRoot)
	{
		switch(sortMode)
		{
			case DemographicName:
			{
				Join<Tickler, Demographic> demographicJoin = ticklerRoot.join(Tickler_.DEMOGRAPHIC);
				criteriaQuery.orderBy(
						applySortOrder(criteriaBuilder, demographicJoin.get(Demographic_.LAST_NAME)),
						applySortOrder(criteriaBuilder, demographicJoin.get(Demographic_.FIRST_NAME)));
				break;
			}
			case Creator:
			{
				Join<Tickler, ProviderData> providerJoin = ticklerRoot.join(Tickler_.PROVIDER, JoinType.LEFT);
				criteriaQuery.orderBy(
						applySortOrder(criteriaBuilder, providerJoin.get(ProviderData_.LAST_NAME)),
						applySortOrder(criteriaBuilder, providerJoin.get(ProviderData_.FIRST_NAME)));
				break;
			}
			case TaskAssignedTo:
			{
				Join<Tickler, ProviderData> providerJoin = ticklerRoot.join(Tickler_.ASSIGNEE, JoinType.LEFT);
				criteriaQuery.orderBy(
						applySortOrder(criteriaBuilder, providerJoin.get(ProviderData_.LAST_NAME)),
						applySortOrder(criteriaBuilder, providerJoin.get(ProviderData_.FIRST_NAME)));
				break;
			}
			case ServiceDate:
			{
				criteriaQuery.orderBy(applySortOrder(criteriaBuilder, ticklerRoot.get(Tickler_.SERVICE_DATE)));
				break;
			}
			case Priority:
			{
				criteriaQuery.orderBy(applySortOrder(criteriaBuilder, ticklerRoot.get(Tickler_.PRIORITY)));
				break;
			}
			case Status:
			{
				criteriaQuery.orderBy(applySortOrder(criteriaBuilder, ticklerRoot.get(Tickler_.STATUS)));
				break;
			}
			case Message:
			{
				criteriaQuery.orderBy(applySortOrder(criteriaBuilder, ticklerRoot.get(Tickler_.MESSAGE)));
				break;
			}
			case UpdateDate:
			default:
			{
				criteriaQuery.orderBy(applySortOrder(criteriaBuilder, ticklerRoot.get(Tickler_.UPDATE_DATE)));
				break;
			}
		}
	}
}
