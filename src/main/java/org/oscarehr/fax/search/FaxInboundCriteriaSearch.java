/**
 * Copyright (c) 2012-2018. CloudPractice Inc. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for
 * CloudPractice Inc.
 * Victoria, British Columbia
 * Canada
 */
package org.oscarehr.fax.search;

import lombok.Data;
import org.oscarehr.common.search.AbstractCriteriaQuerySearch;
import org.oscarehr.document.model.Document;
import org.oscarehr.document.model.Document_;
import org.oscarehr.fax.model.FaxAccount;
import org.oscarehr.fax.model.FaxAccount_;
import org.oscarehr.fax.model.FaxInbound;
import org.oscarehr.fax.model.FaxInbound_;
import oscar.util.ConversionUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

@Data
public class FaxInboundCriteriaSearch extends AbstractCriteriaQuerySearch<FaxInbound>
{
	private Long faxAccountId;
	private Integer documentNo;
	private LocalDate startDate;
	private LocalDate endDate;

	public FaxInboundCriteriaSearch()
	{
		//set default ordering
		setOrderBy(FaxInbound_.CREATED_AT);
	}

	@Override
	public List<Predicate> setCriteriaProperties(CriteriaBuilder criteriaBuilder, CriteriaQuery<?> criteriaQuery, Root<FaxInbound> faxInboundRoot)
	{
		List<Predicate> predicateList = new ArrayList<>();

		Join<FaxInbound, FaxAccount> faxAccountJoin = faxInboundRoot.join(FaxInbound_.FAX_ACCOUNT);
		Join<FaxInbound, Document> documentJoin = faxInboundRoot.join(FaxInbound_.DOCUMENT);

		if(getFaxAccountId() != null)
		{
			predicateList.add(criteriaBuilder.equal(faxAccountJoin.get(FaxAccount_.ID), getFaxAccountId()));
		}
		if(getDocumentNo() != null)
		{
			predicateList.add(criteriaBuilder.equal(documentJoin.get(Document_.DOCUMENT_NO), getDocumentNo()));
		}
		if(getEndDate() != null)
		{
			predicateList.add(criteriaBuilder.lessThanOrEqualTo(faxInboundRoot.get(FaxInbound_.CREATED_AT),
					ConversionUtils.toLegacyDateTime(getEndDate().atTime(LocalTime.MAX))));
		}
		if(getStartDate() != null)
		{
			predicateList.add(criteriaBuilder.greaterThanOrEqualTo(faxInboundRoot.get(FaxInbound_.CREATED_AT),
					ConversionUtils.toLegacyDateTime(getStartDate().atTime(LocalTime.MIN))));
		}

		applyColumnOrdering(criteriaBuilder, criteriaQuery, faxInboundRoot);
		return predicateList;
	}
}
