/**
 * Copyright (c) 2012-2018. CloudPractice Inc. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for
 * CloudPractice Inc.
 * Victoria, British Columbia
 * Canada
 */

package org.oscarehr.consultations.converter;

import org.apache.commons.lang3.StringUtils;
import org.oscarehr.common.conversion.AbstractModelConverter;
import org.oscarehr.common.model.ConsultationRequest;
import org.oscarehr.common.model.Site;
import org.oscarehr.site.service.SiteService;
import org.oscarehr.specialists.converter.ProfessionalSpecialistToTransferConverter;
import org.oscarehr.ws.rest.to.model.ConsultationRequestTo1;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import oscar.util.ConversionUtils;

import java.time.ZoneId;
import java.util.Optional;

@Lazy
@Component
public class ConsultationRequestToTransferConverter extends AbstractModelConverter<ConsultationRequest, ConsultationRequestTo1>
{
	@Autowired
	private SiteService siteService;

	@Autowired
	private ProfessionalSpecialistToTransferConverter specialistToTransferConverter;

	@Override
	public ConsultationRequestTo1 convert(ConsultationRequest request)
	{
		ConsultationRequestTo1 transfer = new ConsultationRequestTo1();
		if (request == null)
		{
			return null;
		}

		BeanUtils.copyProperties(request, transfer,
				"professionalSpecialist",
				"appointmentDate",
				"appointmentTime",
				"referralDate",
				"followUpDate",
				"siteId");
		transfer.setReferralDate(ConversionUtils.toNullableLocalDate(request.getReferralDate()));
		transfer.setFollowUpDate(ConversionUtils.toNullableLocalDate(request.getFollowUpDate()));

		transfer.setAppointmentDateTime(
				Optional.ofNullable(request.getAppointmentDateTime())
						.map(ConversionUtils::toLocalDateTime)
						.map((localDateTime) -> localDateTime.atZone(ZoneId.systemDefault()))
						.orElse(null));

		transfer.setProfessionalSpecialist(specialistToTransferConverter.convert(request.getProfessionalSpecialist()));

		// have to do this for now since consult doesn't reference sites by ID
		if(StringUtils.isNotBlank(request.getSiteName()))
		{
			Site site = siteService.getSiteByName(request.getSiteName());
			transfer.setSiteId(site.getId());
		}
		return transfer;
	}

}
