/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package org.oscarehr.common.service;

import javax.servlet.http.HttpSession;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang.StringUtils;
import org.oscarehr.common.model.PendoAccountData;
import org.oscarehr.common.model.PendoVisitorData;
import org.oscarehr.common.model.Provider;
import org.oscarehr.config.JunoProperties;
import org.oscarehr.preferences.service.SystemPreferenceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import oscar.OscarProperties;

@Slf4j
@Service
public class PendoMetadataService
{

    private static final String EMPTY = "";
    private static final String USER_ROLE_ATTRIBUTE_KEY = "userrole";
    private static final String USER_FIRST_NAME_ATTRIBUTE_KEY = "userfirstname";
    private static final String USER_LAST_NAME_ATTRIBUTE_KEY = "userlastname";
    private static final String PENDO_ENABLED_KEY = "pendo.enabled";
    private static final String VISITOR_ID_DELIMITER = "#$#$SUFFIX#$#$";

    private final OscarProperties oscarProperties;
    private final SystemPreferenceService systemPreferenceService;
    private final JunoProperties junoProperties;

    /**
     * The primary constructor for Spring's dependency injection.
     * It autowires the managed beans and then delegates to the main constructor,
     * manually providing the legacy {@link OscarProperties} instance.
     *
     * @param junoProperties the JunoProperties bean (autowired)
     * @param systemPreferenceService the SystemPreferenceService bean (autowired)
     */
    @Autowired
    public PendoMetadataService(final JunoProperties junoProperties,
                               final SystemPreferenceService systemPreferenceService) {
        this(junoProperties, systemPreferenceService, OscarProperties.getInstance());
    }

    /**
     * The main internal constructor that initializes all service dependencies.
     * This is the designated constructor for unit testing, as it allows all dependencies,
     * including the legacy {@link OscarProperties}, to be mocked.
     *
     * @param junoProperties the JunoProperties bean
     * @param systemPreferenceService the SystemPreferenceService bean
     * @param oscarProperties the OscarProperties instance (can be a mock).
     */
    public PendoMetadataService(final JunoProperties junoProperties,
                               final SystemPreferenceService systemPreferenceService,
                               final OscarProperties oscarProperties) {
        this.junoProperties = junoProperties;
        this.systemPreferenceService = systemPreferenceService;
        this.oscarProperties = oscarProperties;

		log.info("PendoMetaService initialized, build number: {}", junoProperties.getVersion());
    }

    /**
     * Check if Pendo is enabled in the system preferences.
     *
     * @return true if Pendo is enabled, false otherwise
     */
    public boolean isPendoEnabled() {
        return systemPreferenceService.isPreferenceEnabled(PENDO_ENABLED_KEY, false);
    }

    /**
     * Get the Pendo API key from system properties.
     *
     * @return the Pendo API key or empty string if not configured
     */
    public String getPendoApiKey() {
        if (junoProperties.getPendoConfig() == null) {
            return EMPTY;
        }
        return junoProperties.getPendoConfig().getApiKey() != null ? 
               junoProperties.getPendoConfig().getApiKey() : EMPTY;
    }

    /**
     * Get the visitor data for the given session and provider. This will return a PendoVisitorData
     * to be included in the pendo script as metadata.
     *
     * @param loggedInProvider - the provider that is logged in when accessing the page
     * @param session          - the session that is currently active
     * @return - a PendoVisitorData object with the visitor data
     */
    public PendoVisitorData getVisitorData(final Provider loggedInProvider,
                                           final HttpSession session) {
        return new PendoVisitorData(
            getVisitorId(session),
            buildFullName(session),
            getUserRole(session),
            determineIfSuperUser(loggedInProvider),
            getProviderType(loggedInProvider),
            determineHasCollegeNumber(loggedInProvider),
            getCollegeType(loggedInProvider)
        );
    }

    /**
     * Get the account data for the current session. This will return a PendoAccountData object to
     * be included in the pendo script as metadata.
     *
     * @return - a PendoAccountData object with the account data
     */
    public PendoAccountData getAccountData() {
        return new PendoAccountData(
            getPracticeId(),
            getEmrVersion(),
            getProvince()
        );
    }

    /**
     * Get the visitor id for the given session. This is a combination of the practice ID and the
     * session username
     *
     * @param session - the session to get the session username
     * @return - the visitor id for the given session
     */
    private String getVisitorId(final HttpSession session) {
        return getPracticeId() + VISITOR_ID_DELIMITER + session.getAttribute("user");
    }

    /**
     * Get the user role for the given session.
     *
     * @param session - the session to get the user role for
     * @return - the user role for the given session
     */
    private String getUserRole(final HttpSession session) {
        return getSessionAttributeAsString(session, USER_ROLE_ATTRIBUTE_KEY);
    }

    /**
     * Determine if the user is a superuser based on the provider's superAdmin flag.
     *
     * @param loggedInProvider - the provider to check
     * @return - true if the user is a superuser, false otherwise
     */
    private boolean determineIfSuperUser(final Provider loggedInProvider) {
        return loggedInProvider != null && loggedInProvider.getSuperAdmin() != null &&
            loggedInProvider.getSuperAdmin();
    }

    /**
     * Get the provider type for the given provider.
     *
     * @param loggedInProvider - the provider that is logged in when accessing the page
     * @return - the provider type for the given provider
     */
    private String getProviderType(final Provider loggedInProvider) {
        return loggedInProvider == null ? EMPTY : loggedInProvider.getProviderType();
    }

    /**
     * Determine if the given provider has a college number. This is true if the provider is not
     * null and the practitioner number is not empty.
     *
     * @param loggedInProvider - the provider that is logged in when accessing the page
     * @return - true if the provider has a college number, false otherwise
     */
    private boolean determineHasCollegeNumber(final Provider loggedInProvider) {
        if (loggedInProvider == null) {
            return false;
        }

        return StringUtils.isNotBlank(loggedInProvider.getOntarioCnoNumber()) ||
               StringUtils.isNotBlank(loggedInProvider.getAlbertaTakNo()) || 
               StringUtils.isNotBlank(loggedInProvider.getPractitionerNo());
    }

    /**
     * Get the college type for the given provider. This returns the actual college number value
     * that is present, rather than a hardcoded string.
     *
     * @param loggedInProvider - the provider that is logged in when accessing the page
     * @return - the actual college number value for the given provider
     */
    private String getCollegeType(final Provider loggedInProvider) {
        if (loggedInProvider == null) {
            return EMPTY;
        }

        if (StringUtils.isNotBlank(loggedInProvider.getOntarioCnoNumber())) {
            return loggedInProvider.getOntarioCnoNumber();
        } else if (StringUtils.isNotBlank(loggedInProvider.getAlbertaTakNo())) {
            return loggedInProvider.getAlbertaTakNo();
        } else if (StringUtils.isNotBlank(loggedInProvider.getPractitionerNo())) {
            return loggedInProvider.getPractitionerNo();
        }

        return EMPTY;
    }

    /**
     * Get the EMR version from the 'buildtag' property in the oscar properties.
     *
     * @return the EMR version string, or empty string if not found
     */
    private String getEmrVersion() {
        return junoProperties.getVersion() != null ? junoProperties.getVersion() : EMPTY;
    }

    /**
     * Get the practice ID from the 'PRACTICE_ID' environment variable.
     * This is used to uniquely identify the practice in Pendo analytics.
     *
     * @return the practice ID string, or null if not found
     */
    private String getPracticeId() {
        return getEnvironmentVariable("PRACTICE_ID");
    }

    /**
     * Get the value of an environment variable.
     * This method is protected to allow it to be overridden in tests.
     *
     * @param name the name of the environment variable
     * @return the value of the environment variable, or null if not found
     */
    protected String getEnvironmentVariable(String name) {
        return System.getenv(name);
    }

    /**
     * Get the province from the 'instance_type' property in the oscar properties.
     *
     * @return - the province
     */
    private String getProvince() {
		return oscarProperties.getInstanceType();
	}

    /**
     * Get the session attribute as a string. If the attribute is null, return an empty string.
     *
     * @param session       - the session to get the attribute from
     * @param attributeName - the name of the attribute to get
     * @return - the attribute as a string
     */
    private String getSessionAttributeAsString(final HttpSession session,
                                               final String attributeName) {
        val attr = session.getAttribute(attributeName);
        return attr != null ? attr.toString() : EMPTY;
    }

    /**
     * Build the user's full name from the first and last name session attributes.
     *
     * @param session - the session to get the first and last name from
     * @return - the user's full name
     */
    private String buildFullName(final HttpSession session) {
        val firstName = getSessionAttributeAsString(session, USER_FIRST_NAME_ATTRIBUTE_KEY);
		val lastName = getSessionAttributeAsString(session, USER_LAST_NAME_ATTRIBUTE_KEY);
        return firstName.isEmpty() && lastName.isEmpty() ? EMPTY :
            firstName + " " + lastName;
    }
}
