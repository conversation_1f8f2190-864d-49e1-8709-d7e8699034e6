/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package apps.health.device.model;

import apps.health.device.service.DevicePropertiesService;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
public class Device {

	private String vendorName;
	private String productName;
	private String version;
	private String productInstance;
	private String type;

	public static final String VENDOR_NAME = "WELL EMR Group Inc.";
	public static final String PRODUCT_NAME = "Juno EMR";
	public static final String BUILD_TAG_PROPERTY = "buildDateTime";
	public static final String PRACTICE_ID_PROPERTY = "PRACTICE_ID";
	public static final String TYPE = "software";

	public Device() {
		this.vendorName = VENDOR_NAME;
		this.productName = PRODUCT_NAME;
		this.type = TYPE;
		this.version = null;
		this.productInstance = null;
	}
	
	public Device(DevicePropertiesService propertiesService) {
		this.vendorName = VENDOR_NAME;
		this.productName = PRODUCT_NAME;
		this.type = TYPE;
		this.version = propertiesService.getBuildDateTime();
		this.productInstance = propertiesService.getPracticeId();
	}
}
