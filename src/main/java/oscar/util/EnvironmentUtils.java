/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package oscar.util;

import java.util.Arrays;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class EnvironmentUtils {

  public static final String TEST_PROFILE = "test";

  private static Environment environment;

  public EnvironmentUtils(Environment environment) {
    EnvironmentUtils.environment = environment;
  }

  public static boolean testProfileIsActive() {
    return Arrays.asList(environment.getActiveProfiles()).contains(TEST_PROFILE);
  }
}
