/**
 *
 * Copyright (c) 2005-2012. Centre for Research on Inner City Health, St. Michael's Hospital, Toronto. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for
 * Centre for Research on Inner City Health, St. Michael's Hospital,
 * Toronto, Ontario, Canada
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vhudson-jaxb-ri-2.1-793 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2009.05.21 at 12:50:19 PM EDT 
//


package oscar.ocan.domain.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{}C1__Accommodation__What_kind_of_place_do_you_live_in_"/>
 *         &lt;element ref="{}C2__Food__Do_you_get_enough_to_eat_"/>
 *         &lt;element ref="{}C3__Looking_after_the_home__Are_you_able_to_look_after_your_home__"/>
 *         &lt;element ref="{}C4__Self_Care__Do_you_have_problems_keeping_clean_and_tidy__"/>
 *         &lt;element ref="{}C5__Daytime_activities__How_do_you_spend_your_day__"/>
 *         &lt;element ref="{}C6__Physical_Health__How_well_do_you_feel_physically__"/>
 *         &lt;element ref="{}C7__Psychotic_symptoms__Do_you_ever_hear_voices_or_have_problems_with_your_thoughts__"/>
 *         &lt;element ref="{}C8__Information_on_condition_and_treatment__Have_you_been_given_clear_information_about_your_med"/>
 *         &lt;element ref="{}C9__Psychological_distress__Have_you_recently_felt_very_sad_or_low__"/>
 *         &lt;element ref="{}C10__Safety_to_Self__Do_you_ever_have_thoughts_of_harming_yourself__"/>
 *         &lt;element ref="{}C11__Safety_to_others__Do_you_think_you_could_be_a_danger_to_other_people_s_safety__"/>
 *         &lt;element ref="{}C12__Alcohol__Does_drinking_cause_you_any_problems__"/>
 *         &lt;element ref="{}C13__Drugs__Do_you_take_any_drugs_that_aren_t_prescribed__"/>
 *         &lt;element ref="{}COther_Addictions__Do_you_have_any_other_addictions__such_as_gambling__"/>
 *         &lt;element ref="{}C15__Company__Are_you_happy_with_your_social_life__"/>
 *         &lt;element ref="{}C16__Intimate_relationships__Do_you_have_a_partner_"/>
 *         &lt;element ref="{}C17__Sexual_expression__How_is_you_sex_life_"/>
 *         &lt;element ref="{}C18__Child_care__Do_you_have_any_children_under_18__"/>
 *         &lt;element ref="{}C19__Other_dependents__Do_you_have_any_dependents_other_than_children_under_18__such_as_an_elder"/>
 *         &lt;element ref="{}C20__Basic_education__Any_difficulty_in_reading__writing_or_understanding_English__"/>
 *         &lt;element ref="{}C21__Telephone__Do_you_know_how_to_use_a_telephone_"/>
 *         &lt;element ref="{}C22__Transport__How_do_you_find_using_the_bus__streetcar_or_train__"/>
 *         &lt;element ref="{}C23__Money__How_do_you_find_budgeting_your_money__"/>
 *         &lt;element ref="{}C24__Benefits__Are_you_getting_all_the_money_you_are_entitled_to__"/>
 *         &lt;element ref="{}CPlease_write_a_few_sentences_to_answer_the_following_questions_"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "c1AccommodationWhatKindOfPlaceDoYouLiveIn",
    "c2FoodDoYouGetEnoughToEat",
    "c3LookingAfterTheHomeAreYouAbleToLookAfterYourHome",
    "c4SelfCareDoYouHaveProblemsKeepingCleanAndTidy",
    "c5DaytimeActivitiesHowDoYouSpendYourDay",
    "c6PhysicalHealthHowWellDoYouFeelPhysically",
    "c7PsychoticSymptomsDoYouEverHearVoicesOrHaveProblemsWithYourThoughts",
    "c8InformationOnConditionAndTreatmentHaveYouBeenGivenClearInformationAboutYourMed",
    "c9PsychologicalDistressHaveYouRecentlyFeltVerySadOrLow",
    "c10SafetyToSelfDoYouEverHaveThoughtsOfHarmingYourself",
    "c11SafetyToOthersDoYouThinkYouCouldBeADangerToOtherPeopleSSafety",
    "c12AlcoholDoesDrinkingCauseYouAnyProblems",
    "c13DrugsDoYouTakeAnyDrugsThatArenTPrescribed",
    "cOtherAddictionsDoYouHaveAnyOtherAddictionsSuchAsGambling",
    "c15CompanyAreYouHappyWithYourSocialLife",
    "c16IntimateRelationshipsDoYouHaveAPartner",
    "c17SexualExpressionHowIsYouSexLife",
    "c18ChildCareDoYouHaveAnyChildrenUnder18",
    "c19OtherDependentsDoYouHaveAnyDependentsOtherThanChildrenUnder18SuchAsAnElder",
    "c20BasicEducationAnyDifficultyInReadingWritingOrUnderstandingEnglish",
    "c21TelephoneDoYouKnowHowToUseATelephone",
    "c22TransportHowDoYouFindUsingTheBusStreetcarOrTrain",
    "c23MoneyHowDoYouFindBudgetingYourMoney",
    "c24BenefitsAreYouGettingAllTheMoneyYouAreEntitledTo",
    "cPleaseWriteAFewSentencesToAnswerTheFollowingQuestions"
})
@XmlRootElement(name = "CQuestions")
public class CQuestions {

    @XmlElement(name = "C1__Accommodation__What_kind_of_place_do_you_live_in_", required = true)
    protected C1AccommodationWhatKindOfPlaceDoYouLiveIn c1AccommodationWhatKindOfPlaceDoYouLiveIn;
    @XmlElement(name = "C2__Food__Do_you_get_enough_to_eat_", required = true)
    protected C2FoodDoYouGetEnoughToEat c2FoodDoYouGetEnoughToEat;
    @XmlElement(name = "C3__Looking_after_the_home__Are_you_able_to_look_after_your_home__", required = true)
    protected C3LookingAfterTheHomeAreYouAbleToLookAfterYourHome c3LookingAfterTheHomeAreYouAbleToLookAfterYourHome;
    @XmlElement(name = "C4__Self_Care__Do_you_have_problems_keeping_clean_and_tidy__", required = true)
    protected C4SelfCareDoYouHaveProblemsKeepingCleanAndTidy c4SelfCareDoYouHaveProblemsKeepingCleanAndTidy;
    @XmlElement(name = "C5__Daytime_activities__How_do_you_spend_your_day__", required = true)
    protected C5DaytimeActivitiesHowDoYouSpendYourDay c5DaytimeActivitiesHowDoYouSpendYourDay;
    @XmlElement(name = "C6__Physical_Health__How_well_do_you_feel_physically__", required = true)
    protected C6PhysicalHealthHowWellDoYouFeelPhysically c6PhysicalHealthHowWellDoYouFeelPhysically;
    @XmlElement(name = "C7__Psychotic_symptoms__Do_you_ever_hear_voices_or_have_problems_with_your_thoughts__", required = true)
    protected C7PsychoticSymptomsDoYouEverHearVoicesOrHaveProblemsWithYourThoughts c7PsychoticSymptomsDoYouEverHearVoicesOrHaveProblemsWithYourThoughts;
    @XmlElement(name = "C8__Information_on_condition_and_treatment__Have_you_been_given_clear_information_about_your_med", required = true)
    protected C8InformationOnConditionAndTreatmentHaveYouBeenGivenClearInformationAboutYourMed c8InformationOnConditionAndTreatmentHaveYouBeenGivenClearInformationAboutYourMed;
    @XmlElement(name = "C9__Psychological_distress__Have_you_recently_felt_very_sad_or_low__", required = true)
    protected C9PsychologicalDistressHaveYouRecentlyFeltVerySadOrLow c9PsychologicalDistressHaveYouRecentlyFeltVerySadOrLow;
    @XmlElement(name = "C10__Safety_to_Self__Do_you_ever_have_thoughts_of_harming_yourself__", required = true)
    protected C10SafetyToSelfDoYouEverHaveThoughtsOfHarmingYourself c10SafetyToSelfDoYouEverHaveThoughtsOfHarmingYourself;
    @XmlElement(name = "C11__Safety_to_others__Do_you_think_you_could_be_a_danger_to_other_people_s_safety__", required = true)
    protected C11SafetyToOthersDoYouThinkYouCouldBeADangerToOtherPeopleSSafety c11SafetyToOthersDoYouThinkYouCouldBeADangerToOtherPeopleSSafety;
    @XmlElement(name = "C12__Alcohol__Does_drinking_cause_you_any_problems__", required = true)
    protected C12AlcoholDoesDrinkingCauseYouAnyProblems c12AlcoholDoesDrinkingCauseYouAnyProblems;
    @XmlElement(name = "C13__Drugs__Do_you_take_any_drugs_that_aren_t_prescribed__", required = true)
    protected C13DrugsDoYouTakeAnyDrugsThatArenTPrescribed c13DrugsDoYouTakeAnyDrugsThatArenTPrescribed;
    @XmlElement(name = "COther_Addictions__Do_you_have_any_other_addictions__such_as_gambling__", required = true)
    protected COtherAddictionsDoYouHaveAnyOtherAddictionsSuchAsGambling cOtherAddictionsDoYouHaveAnyOtherAddictionsSuchAsGambling;
    @XmlElement(name = "C15__Company__Are_you_happy_with_your_social_life__", required = true)
    protected C15CompanyAreYouHappyWithYourSocialLife c15CompanyAreYouHappyWithYourSocialLife;
    @XmlElement(name = "C16__Intimate_relationships__Do_you_have_a_partner_", required = true)
    protected C16IntimateRelationshipsDoYouHaveAPartner c16IntimateRelationshipsDoYouHaveAPartner;
    @XmlElement(name = "C17__Sexual_expression__How_is_you_sex_life_", required = true)
    protected C17SexualExpressionHowIsYouSexLife c17SexualExpressionHowIsYouSexLife;
    @XmlElement(name = "C18__Child_care__Do_you_have_any_children_under_18__", required = true)
    protected C18ChildCareDoYouHaveAnyChildrenUnder18 c18ChildCareDoYouHaveAnyChildrenUnder18;
    @XmlElement(name = "C19__Other_dependents__Do_you_have_any_dependents_other_than_children_under_18__such_as_an_elder", required = true)
    protected C19OtherDependentsDoYouHaveAnyDependentsOtherThanChildrenUnder18SuchAsAnElder c19OtherDependentsDoYouHaveAnyDependentsOtherThanChildrenUnder18SuchAsAnElder;
    @XmlElement(name = "C20__Basic_education__Any_difficulty_in_reading__writing_or_understanding_English__", required = true)
    protected C20BasicEducationAnyDifficultyInReadingWritingOrUnderstandingEnglish c20BasicEducationAnyDifficultyInReadingWritingOrUnderstandingEnglish;
    @XmlElement(name = "C21__Telephone__Do_you_know_how_to_use_a_telephone_", required = true)
    protected C21TelephoneDoYouKnowHowToUseATelephone c21TelephoneDoYouKnowHowToUseATelephone;
    @XmlElement(name = "C22__Transport__How_do_you_find_using_the_bus__streetcar_or_train__", required = true)
    protected C22TransportHowDoYouFindUsingTheBusStreetcarOrTrain c22TransportHowDoYouFindUsingTheBusStreetcarOrTrain;
    @XmlElement(name = "C23__Money__How_do_you_find_budgeting_your_money__", required = true)
    protected C23MoneyHowDoYouFindBudgetingYourMoney c23MoneyHowDoYouFindBudgetingYourMoney;
    @XmlElement(name = "C24__Benefits__Are_you_getting_all_the_money_you_are_entitled_to__", required = true)
    protected C24BenefitsAreYouGettingAllTheMoneyYouAreEntitledTo c24BenefitsAreYouGettingAllTheMoneyYouAreEntitledTo;
    @XmlElement(name = "CPlease_write_a_few_sentences_to_answer_the_following_questions_", required = true)
    protected CPleaseWriteAFewSentencesToAnswerTheFollowingQuestions cPleaseWriteAFewSentencesToAnswerTheFollowingQuestions;

    /**
     * Gets the value of the c1AccommodationWhatKindOfPlaceDoYouLiveIn property.
     * 
     * @return
     *     possible object is
     *     {@link C1AccommodationWhatKindOfPlaceDoYouLiveIn }
     *     
     */
    public C1AccommodationWhatKindOfPlaceDoYouLiveIn getC1AccommodationWhatKindOfPlaceDoYouLiveIn() {
        return c1AccommodationWhatKindOfPlaceDoYouLiveIn;
    }

    /**
     * Sets the value of the c1AccommodationWhatKindOfPlaceDoYouLiveIn property.
     * 
     * @param value
     *     allowed object is
     *     {@link C1AccommodationWhatKindOfPlaceDoYouLiveIn }
     *     
     */
    public void setC1AccommodationWhatKindOfPlaceDoYouLiveIn(C1AccommodationWhatKindOfPlaceDoYouLiveIn value) {
        this.c1AccommodationWhatKindOfPlaceDoYouLiveIn = value;
    }

    /**
     * Gets the value of the c2FoodDoYouGetEnoughToEat property.
     * 
     * @return
     *     possible object is
     *     {@link C2FoodDoYouGetEnoughToEat }
     *     
     */
    public C2FoodDoYouGetEnoughToEat getC2FoodDoYouGetEnoughToEat() {
        return c2FoodDoYouGetEnoughToEat;
    }

    /**
     * Sets the value of the c2FoodDoYouGetEnoughToEat property.
     * 
     * @param value
     *     allowed object is
     *     {@link C2FoodDoYouGetEnoughToEat }
     *     
     */
    public void setC2FoodDoYouGetEnoughToEat(C2FoodDoYouGetEnoughToEat value) {
        this.c2FoodDoYouGetEnoughToEat = value;
    }

    /**
     * Gets the value of the c3LookingAfterTheHomeAreYouAbleToLookAfterYourHome property.
     * 
     * @return
     *     possible object is
     *     {@link C3LookingAfterTheHomeAreYouAbleToLookAfterYourHome }
     *     
     */
    public C3LookingAfterTheHomeAreYouAbleToLookAfterYourHome getC3LookingAfterTheHomeAreYouAbleToLookAfterYourHome() {
        return c3LookingAfterTheHomeAreYouAbleToLookAfterYourHome;
    }

    /**
     * Sets the value of the c3LookingAfterTheHomeAreYouAbleToLookAfterYourHome property.
     * 
     * @param value
     *     allowed object is
     *     {@link C3LookingAfterTheHomeAreYouAbleToLookAfterYourHome }
     *     
     */
    public void setC3LookingAfterTheHomeAreYouAbleToLookAfterYourHome(C3LookingAfterTheHomeAreYouAbleToLookAfterYourHome value) {
        this.c3LookingAfterTheHomeAreYouAbleToLookAfterYourHome = value;
    }

    /**
     * Gets the value of the c4SelfCareDoYouHaveProblemsKeepingCleanAndTidy property.
     * 
     * @return
     *     possible object is
     *     {@link C4SelfCareDoYouHaveProblemsKeepingCleanAndTidy }
     *     
     */
    public C4SelfCareDoYouHaveProblemsKeepingCleanAndTidy getC4SelfCareDoYouHaveProblemsKeepingCleanAndTidy() {
        return c4SelfCareDoYouHaveProblemsKeepingCleanAndTidy;
    }

    /**
     * Sets the value of the c4SelfCareDoYouHaveProblemsKeepingCleanAndTidy property.
     * 
     * @param value
     *     allowed object is
     *     {@link C4SelfCareDoYouHaveProblemsKeepingCleanAndTidy }
     *     
     */
    public void setC4SelfCareDoYouHaveProblemsKeepingCleanAndTidy(C4SelfCareDoYouHaveProblemsKeepingCleanAndTidy value) {
        this.c4SelfCareDoYouHaveProblemsKeepingCleanAndTidy = value;
    }

    /**
     * Gets the value of the c5DaytimeActivitiesHowDoYouSpendYourDay property.
     * 
     * @return
     *     possible object is
     *     {@link C5DaytimeActivitiesHowDoYouSpendYourDay }
     *     
     */
    public C5DaytimeActivitiesHowDoYouSpendYourDay getC5DaytimeActivitiesHowDoYouSpendYourDay() {
        return c5DaytimeActivitiesHowDoYouSpendYourDay;
    }

    /**
     * Sets the value of the c5DaytimeActivitiesHowDoYouSpendYourDay property.
     * 
     * @param value
     *     allowed object is
     *     {@link C5DaytimeActivitiesHowDoYouSpendYourDay }
     *     
     */
    public void setC5DaytimeActivitiesHowDoYouSpendYourDay(C5DaytimeActivitiesHowDoYouSpendYourDay value) {
        this.c5DaytimeActivitiesHowDoYouSpendYourDay = value;
    }

    /**
     * Gets the value of the c6PhysicalHealthHowWellDoYouFeelPhysically property.
     * 
     * @return
     *     possible object is
     *     {@link C6PhysicalHealthHowWellDoYouFeelPhysically }
     *     
     */
    public C6PhysicalHealthHowWellDoYouFeelPhysically getC6PhysicalHealthHowWellDoYouFeelPhysically() {
        return c6PhysicalHealthHowWellDoYouFeelPhysically;
    }

    /**
     * Sets the value of the c6PhysicalHealthHowWellDoYouFeelPhysically property.
     * 
     * @param value
     *     allowed object is
     *     {@link C6PhysicalHealthHowWellDoYouFeelPhysically }
     *     
     */
    public void setC6PhysicalHealthHowWellDoYouFeelPhysically(C6PhysicalHealthHowWellDoYouFeelPhysically value) {
        this.c6PhysicalHealthHowWellDoYouFeelPhysically = value;
    }

    /**
     * Gets the value of the c7PsychoticSymptomsDoYouEverHearVoicesOrHaveProblemsWithYourThoughts property.
     * 
     * @return
     *     possible object is
     *     {@link C7PsychoticSymptomsDoYouEverHearVoicesOrHaveProblemsWithYourThoughts }
     *     
     */
    public C7PsychoticSymptomsDoYouEverHearVoicesOrHaveProblemsWithYourThoughts getC7PsychoticSymptomsDoYouEverHearVoicesOrHaveProblemsWithYourThoughts() {
        return c7PsychoticSymptomsDoYouEverHearVoicesOrHaveProblemsWithYourThoughts;
    }

    /**
     * Sets the value of the c7PsychoticSymptomsDoYouEverHearVoicesOrHaveProblemsWithYourThoughts property.
     * 
     * @param value
     *     allowed object is
     *     {@link C7PsychoticSymptomsDoYouEverHearVoicesOrHaveProblemsWithYourThoughts }
     *     
     */
    public void setC7PsychoticSymptomsDoYouEverHearVoicesOrHaveProblemsWithYourThoughts(C7PsychoticSymptomsDoYouEverHearVoicesOrHaveProblemsWithYourThoughts value) {
        this.c7PsychoticSymptomsDoYouEverHearVoicesOrHaveProblemsWithYourThoughts = value;
    }

    /**
     * Gets the value of the c8InformationOnConditionAndTreatmentHaveYouBeenGivenClearInformationAboutYourMed property.
     * 
     * @return
     *     possible object is
     *     {@link C8InformationOnConditionAndTreatmentHaveYouBeenGivenClearInformationAboutYourMed }
     *     
     */
    public C8InformationOnConditionAndTreatmentHaveYouBeenGivenClearInformationAboutYourMed getC8InformationOnConditionAndTreatmentHaveYouBeenGivenClearInformationAboutYourMed() {
        return c8InformationOnConditionAndTreatmentHaveYouBeenGivenClearInformationAboutYourMed;
    }

    /**
     * Sets the value of the c8InformationOnConditionAndTreatmentHaveYouBeenGivenClearInformationAboutYourMed property.
     * 
     * @param value
     *     allowed object is
     *     {@link C8InformationOnConditionAndTreatmentHaveYouBeenGivenClearInformationAboutYourMed }
     *     
     */
    public void setC8InformationOnConditionAndTreatmentHaveYouBeenGivenClearInformationAboutYourMed(C8InformationOnConditionAndTreatmentHaveYouBeenGivenClearInformationAboutYourMed value) {
        this.c8InformationOnConditionAndTreatmentHaveYouBeenGivenClearInformationAboutYourMed = value;
    }

    /**
     * Gets the value of the c9PsychologicalDistressHaveYouRecentlyFeltVerySadOrLow property.
     * 
     * @return
     *     possible object is
     *     {@link C9PsychologicalDistressHaveYouRecentlyFeltVerySadOrLow }
     *     
     */
    public C9PsychologicalDistressHaveYouRecentlyFeltVerySadOrLow getC9PsychologicalDistressHaveYouRecentlyFeltVerySadOrLow() {
        return c9PsychologicalDistressHaveYouRecentlyFeltVerySadOrLow;
    }

    /**
     * Sets the value of the c9PsychologicalDistressHaveYouRecentlyFeltVerySadOrLow property.
     * 
     * @param value
     *     allowed object is
     *     {@link C9PsychologicalDistressHaveYouRecentlyFeltVerySadOrLow }
     *     
     */
    public void setC9PsychologicalDistressHaveYouRecentlyFeltVerySadOrLow(C9PsychologicalDistressHaveYouRecentlyFeltVerySadOrLow value) {
        this.c9PsychologicalDistressHaveYouRecentlyFeltVerySadOrLow = value;
    }

    /**
     * Gets the value of the c10SafetyToSelfDoYouEverHaveThoughtsOfHarmingYourself property.
     * 
     * @return
     *     possible object is
     *     {@link C10SafetyToSelfDoYouEverHaveThoughtsOfHarmingYourself }
     *     
     */
    public C10SafetyToSelfDoYouEverHaveThoughtsOfHarmingYourself getC10SafetyToSelfDoYouEverHaveThoughtsOfHarmingYourself() {
        return c10SafetyToSelfDoYouEverHaveThoughtsOfHarmingYourself;
    }

    /**
     * Sets the value of the c10SafetyToSelfDoYouEverHaveThoughtsOfHarmingYourself property.
     * 
     * @param value
     *     allowed object is
     *     {@link C10SafetyToSelfDoYouEverHaveThoughtsOfHarmingYourself }
     *     
     */
    public void setC10SafetyToSelfDoYouEverHaveThoughtsOfHarmingYourself(C10SafetyToSelfDoYouEverHaveThoughtsOfHarmingYourself value) {
        this.c10SafetyToSelfDoYouEverHaveThoughtsOfHarmingYourself = value;
    }

    /**
     * Gets the value of the c11SafetyToOthersDoYouThinkYouCouldBeADangerToOtherPeopleSSafety property.
     * 
     * @return
     *     possible object is
     *     {@link C11SafetyToOthersDoYouThinkYouCouldBeADangerToOtherPeopleSSafety }
     *     
     */
    public C11SafetyToOthersDoYouThinkYouCouldBeADangerToOtherPeopleSSafety getC11SafetyToOthersDoYouThinkYouCouldBeADangerToOtherPeopleSSafety() {
        return c11SafetyToOthersDoYouThinkYouCouldBeADangerToOtherPeopleSSafety;
    }

    /**
     * Sets the value of the c11SafetyToOthersDoYouThinkYouCouldBeADangerToOtherPeopleSSafety property.
     * 
     * @param value
     *     allowed object is
     *     {@link C11SafetyToOthersDoYouThinkYouCouldBeADangerToOtherPeopleSSafety }
     *     
     */
    public void setC11SafetyToOthersDoYouThinkYouCouldBeADangerToOtherPeopleSSafety(C11SafetyToOthersDoYouThinkYouCouldBeADangerToOtherPeopleSSafety value) {
        this.c11SafetyToOthersDoYouThinkYouCouldBeADangerToOtherPeopleSSafety = value;
    }

    /**
     * Gets the value of the c12AlcoholDoesDrinkingCauseYouAnyProblems property.
     * 
     * @return
     *     possible object is
     *     {@link C12AlcoholDoesDrinkingCauseYouAnyProblems }
     *     
     */
    public C12AlcoholDoesDrinkingCauseYouAnyProblems getC12AlcoholDoesDrinkingCauseYouAnyProblems() {
        return c12AlcoholDoesDrinkingCauseYouAnyProblems;
    }

    /**
     * Sets the value of the c12AlcoholDoesDrinkingCauseYouAnyProblems property.
     * 
     * @param value
     *     allowed object is
     *     {@link C12AlcoholDoesDrinkingCauseYouAnyProblems }
     *     
     */
    public void setC12AlcoholDoesDrinkingCauseYouAnyProblems(C12AlcoholDoesDrinkingCauseYouAnyProblems value) {
        this.c12AlcoholDoesDrinkingCauseYouAnyProblems = value;
    }

    /**
     * Gets the value of the c13DrugsDoYouTakeAnyDrugsThatArenTPrescribed property.
     * 
     * @return
     *     possible object is
     *     {@link C13DrugsDoYouTakeAnyDrugsThatArenTPrescribed }
     *     
     */
    public C13DrugsDoYouTakeAnyDrugsThatArenTPrescribed getC13DrugsDoYouTakeAnyDrugsThatArenTPrescribed() {
        return c13DrugsDoYouTakeAnyDrugsThatArenTPrescribed;
    }

    /**
     * Sets the value of the c13DrugsDoYouTakeAnyDrugsThatArenTPrescribed property.
     * 
     * @param value
     *     allowed object is
     *     {@link C13DrugsDoYouTakeAnyDrugsThatArenTPrescribed }
     *     
     */
    public void setC13DrugsDoYouTakeAnyDrugsThatArenTPrescribed(C13DrugsDoYouTakeAnyDrugsThatArenTPrescribed value) {
        this.c13DrugsDoYouTakeAnyDrugsThatArenTPrescribed = value;
    }

    /**
     * Gets the value of the cOtherAddictionsDoYouHaveAnyOtherAddictionsSuchAsGambling property.
     * 
     * @return
     *     possible object is
     *     {@link COtherAddictionsDoYouHaveAnyOtherAddictionsSuchAsGambling }
     *     
     */
    public COtherAddictionsDoYouHaveAnyOtherAddictionsSuchAsGambling getCOtherAddictionsDoYouHaveAnyOtherAddictionsSuchAsGambling() {
        return cOtherAddictionsDoYouHaveAnyOtherAddictionsSuchAsGambling;
    }

    /**
     * Sets the value of the cOtherAddictionsDoYouHaveAnyOtherAddictionsSuchAsGambling property.
     * 
     * @param value
     *     allowed object is
     *     {@link COtherAddictionsDoYouHaveAnyOtherAddictionsSuchAsGambling }
     *     
     */
    public void setCOtherAddictionsDoYouHaveAnyOtherAddictionsSuchAsGambling(COtherAddictionsDoYouHaveAnyOtherAddictionsSuchAsGambling value) {
        this.cOtherAddictionsDoYouHaveAnyOtherAddictionsSuchAsGambling = value;
    }

    /**
     * Gets the value of the c15CompanyAreYouHappyWithYourSocialLife property.
     * 
     * @return
     *     possible object is
     *     {@link C15CompanyAreYouHappyWithYourSocialLife }
     *     
     */
    public C15CompanyAreYouHappyWithYourSocialLife getC15CompanyAreYouHappyWithYourSocialLife() {
        return c15CompanyAreYouHappyWithYourSocialLife;
    }

    /**
     * Sets the value of the c15CompanyAreYouHappyWithYourSocialLife property.
     * 
     * @param value
     *     allowed object is
     *     {@link C15CompanyAreYouHappyWithYourSocialLife }
     *     
     */
    public void setC15CompanyAreYouHappyWithYourSocialLife(C15CompanyAreYouHappyWithYourSocialLife value) {
        this.c15CompanyAreYouHappyWithYourSocialLife = value;
    }

    /**
     * Gets the value of the c16IntimateRelationshipsDoYouHaveAPartner property.
     * 
     * @return
     *     possible object is
     *     {@link C16IntimateRelationshipsDoYouHaveAPartner }
     *     
     */
    public C16IntimateRelationshipsDoYouHaveAPartner getC16IntimateRelationshipsDoYouHaveAPartner() {
        return c16IntimateRelationshipsDoYouHaveAPartner;
    }

    /**
     * Sets the value of the c16IntimateRelationshipsDoYouHaveAPartner property.
     * 
     * @param value
     *     allowed object is
     *     {@link C16IntimateRelationshipsDoYouHaveAPartner }
     *     
     */
    public void setC16IntimateRelationshipsDoYouHaveAPartner(C16IntimateRelationshipsDoYouHaveAPartner value) {
        this.c16IntimateRelationshipsDoYouHaveAPartner = value;
    }

    /**
     * Gets the value of the c17SexualExpressionHowIsYouSexLife property.
     * 
     * @return
     *     possible object is
     *     {@link C17SexualExpressionHowIsYouSexLife }
     *     
     */
    public C17SexualExpressionHowIsYouSexLife getC17SexualExpressionHowIsYouSexLife() {
        return c17SexualExpressionHowIsYouSexLife;
    }

    /**
     * Sets the value of the c17SexualExpressionHowIsYouSexLife property.
     * 
     * @param value
     *     allowed object is
     *     {@link C17SexualExpressionHowIsYouSexLife }
     *     
     */
    public void setC17SexualExpressionHowIsYouSexLife(C17SexualExpressionHowIsYouSexLife value) {
        this.c17SexualExpressionHowIsYouSexLife = value;
    }

    /**
     * Gets the value of the c18ChildCareDoYouHaveAnyChildrenUnder18 property.
     * 
     * @return
     *     possible object is
     *     {@link C18ChildCareDoYouHaveAnyChildrenUnder18 }
     *     
     */
    public C18ChildCareDoYouHaveAnyChildrenUnder18 getC18ChildCareDoYouHaveAnyChildrenUnder18() {
        return c18ChildCareDoYouHaveAnyChildrenUnder18;
    }

    /**
     * Sets the value of the c18ChildCareDoYouHaveAnyChildrenUnder18 property.
     * 
     * @param value
     *     allowed object is
     *     {@link C18ChildCareDoYouHaveAnyChildrenUnder18 }
     *     
     */
    public void setC18ChildCareDoYouHaveAnyChildrenUnder18(C18ChildCareDoYouHaveAnyChildrenUnder18 value) {
        this.c18ChildCareDoYouHaveAnyChildrenUnder18 = value;
    }

    /**
     * Gets the value of the c19OtherDependentsDoYouHaveAnyDependentsOtherThanChildrenUnder18SuchAsAnElder property.
     * 
     * @return
     *     possible object is
     *     {@link C19OtherDependentsDoYouHaveAnyDependentsOtherThanChildrenUnder18SuchAsAnElder }
     *     
     */
    public C19OtherDependentsDoYouHaveAnyDependentsOtherThanChildrenUnder18SuchAsAnElder getC19OtherDependentsDoYouHaveAnyDependentsOtherThanChildrenUnder18SuchAsAnElder() {
        return c19OtherDependentsDoYouHaveAnyDependentsOtherThanChildrenUnder18SuchAsAnElder;
    }

    /**
     * Sets the value of the c19OtherDependentsDoYouHaveAnyDependentsOtherThanChildrenUnder18SuchAsAnElder property.
     * 
     * @param value
     *     allowed object is
     *     {@link C19OtherDependentsDoYouHaveAnyDependentsOtherThanChildrenUnder18SuchAsAnElder }
     *     
     */
    public void setC19OtherDependentsDoYouHaveAnyDependentsOtherThanChildrenUnder18SuchAsAnElder(C19OtherDependentsDoYouHaveAnyDependentsOtherThanChildrenUnder18SuchAsAnElder value) {
        this.c19OtherDependentsDoYouHaveAnyDependentsOtherThanChildrenUnder18SuchAsAnElder = value;
    }

    /**
     * Gets the value of the c20BasicEducationAnyDifficultyInReadingWritingOrUnderstandingEnglish property.
     * 
     * @return
     *     possible object is
     *     {@link C20BasicEducationAnyDifficultyInReadingWritingOrUnderstandingEnglish }
     *     
     */
    public C20BasicEducationAnyDifficultyInReadingWritingOrUnderstandingEnglish getC20BasicEducationAnyDifficultyInReadingWritingOrUnderstandingEnglish() {
        return c20BasicEducationAnyDifficultyInReadingWritingOrUnderstandingEnglish;
    }

    /**
     * Sets the value of the c20BasicEducationAnyDifficultyInReadingWritingOrUnderstandingEnglish property.
     * 
     * @param value
     *     allowed object is
     *     {@link C20BasicEducationAnyDifficultyInReadingWritingOrUnderstandingEnglish }
     *     
     */
    public void setC20BasicEducationAnyDifficultyInReadingWritingOrUnderstandingEnglish(C20BasicEducationAnyDifficultyInReadingWritingOrUnderstandingEnglish value) {
        this.c20BasicEducationAnyDifficultyInReadingWritingOrUnderstandingEnglish = value;
    }

    /**
     * Gets the value of the c21TelephoneDoYouKnowHowToUseATelephone property.
     * 
     * @return
     *     possible object is
     *     {@link C21TelephoneDoYouKnowHowToUseATelephone }
     *     
     */
    public C21TelephoneDoYouKnowHowToUseATelephone getC21TelephoneDoYouKnowHowToUseATelephone() {
        return c21TelephoneDoYouKnowHowToUseATelephone;
    }

    /**
     * Sets the value of the c21TelephoneDoYouKnowHowToUseATelephone property.
     * 
     * @param value
     *     allowed object is
     *     {@link C21TelephoneDoYouKnowHowToUseATelephone }
     *     
     */
    public void setC21TelephoneDoYouKnowHowToUseATelephone(C21TelephoneDoYouKnowHowToUseATelephone value) {
        this.c21TelephoneDoYouKnowHowToUseATelephone = value;
    }

    /**
     * Gets the value of the c22TransportHowDoYouFindUsingTheBusStreetcarOrTrain property.
     * 
     * @return
     *     possible object is
     *     {@link C22TransportHowDoYouFindUsingTheBusStreetcarOrTrain }
     *     
     */
    public C22TransportHowDoYouFindUsingTheBusStreetcarOrTrain getC22TransportHowDoYouFindUsingTheBusStreetcarOrTrain() {
        return c22TransportHowDoYouFindUsingTheBusStreetcarOrTrain;
    }

    /**
     * Sets the value of the c22TransportHowDoYouFindUsingTheBusStreetcarOrTrain property.
     * 
     * @param value
     *     allowed object is
     *     {@link C22TransportHowDoYouFindUsingTheBusStreetcarOrTrain }
     *     
     */
    public void setC22TransportHowDoYouFindUsingTheBusStreetcarOrTrain(C22TransportHowDoYouFindUsingTheBusStreetcarOrTrain value) {
        this.c22TransportHowDoYouFindUsingTheBusStreetcarOrTrain = value;
    }

    /**
     * Gets the value of the c23MoneyHowDoYouFindBudgetingYourMoney property.
     * 
     * @return
     *     possible object is
     *     {@link C23MoneyHowDoYouFindBudgetingYourMoney }
     *     
     */
    public C23MoneyHowDoYouFindBudgetingYourMoney getC23MoneyHowDoYouFindBudgetingYourMoney() {
        return c23MoneyHowDoYouFindBudgetingYourMoney;
    }

    /**
     * Sets the value of the c23MoneyHowDoYouFindBudgetingYourMoney property.
     * 
     * @param value
     *     allowed object is
     *     {@link C23MoneyHowDoYouFindBudgetingYourMoney }
     *     
     */
    public void setC23MoneyHowDoYouFindBudgetingYourMoney(C23MoneyHowDoYouFindBudgetingYourMoney value) {
        this.c23MoneyHowDoYouFindBudgetingYourMoney = value;
    }

    /**
     * Gets the value of the c24BenefitsAreYouGettingAllTheMoneyYouAreEntitledTo property.
     * 
     * @return
     *     possible object is
     *     {@link C24BenefitsAreYouGettingAllTheMoneyYouAreEntitledTo }
     *     
     */
    public C24BenefitsAreYouGettingAllTheMoneyYouAreEntitledTo getC24BenefitsAreYouGettingAllTheMoneyYouAreEntitledTo() {
        return c24BenefitsAreYouGettingAllTheMoneyYouAreEntitledTo;
    }

    /**
     * Sets the value of the c24BenefitsAreYouGettingAllTheMoneyYouAreEntitledTo property.
     * 
     * @param value
     *     allowed object is
     *     {@link C24BenefitsAreYouGettingAllTheMoneyYouAreEntitledTo }
     *     
     */
    public void setC24BenefitsAreYouGettingAllTheMoneyYouAreEntitledTo(C24BenefitsAreYouGettingAllTheMoneyYouAreEntitledTo value) {
        this.c24BenefitsAreYouGettingAllTheMoneyYouAreEntitledTo = value;
    }

    /**
     * Gets the value of the cPleaseWriteAFewSentencesToAnswerTheFollowingQuestions property.
     * 
     * @return
     *     possible object is
     *     {@link CPleaseWriteAFewSentencesToAnswerTheFollowingQuestions }
     *     
     */
    public CPleaseWriteAFewSentencesToAnswerTheFollowingQuestions getCPleaseWriteAFewSentencesToAnswerTheFollowingQuestions() {
        return cPleaseWriteAFewSentencesToAnswerTheFollowingQuestions;
    }

    /**
     * Sets the value of the cPleaseWriteAFewSentencesToAnswerTheFollowingQuestions property.
     * 
     * @param value
     *     allowed object is
     *     {@link CPleaseWriteAFewSentencesToAnswerTheFollowingQuestions }
     *     
     */
    public void setCPleaseWriteAFewSentencesToAnswerTheFollowingQuestions(CPleaseWriteAFewSentencesToAnswerTheFollowingQuestions value) {
        this.cPleaseWriteAFewSentencesToAnswerTheFollowingQuestions = value;
    }

}
