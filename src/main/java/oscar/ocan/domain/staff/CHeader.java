/**
 *
 * Copyright (c) 2005-2012. Centre for Research on Inner City Health, St. Michael's Hospital, Toronto. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for
 * Centre for Research on Inner City Health, St. Michael's Hospital,
 * Toronto, Ontario, Canada
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vhudson-jaxb-ri-2.1-793 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2009.05.24 at 10:52:14 PM EDT 
//


package oscar.ocan.domain.staff;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{}CService_Organization_Name_"/>
 *         &lt;element ref="{}CService_Organization_Number_"/>
 *         &lt;element ref="{}CProgram_Name_"/>
 *         &lt;element ref="{}CProgram_Number_"/>
 *         &lt;element ref="{}CFunction__MIS_Functional_Centre__"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "cServiceOrganizationName",
    "cServiceOrganizationNumber",
    "cProgramName",
    "cProgramNumber",
    "cFunctionMISFunctionalCentre"
})
@XmlRootElement(name = "CHeader")
public class CHeader {

    @XmlElement(name = "CService_Organization_Name_", required = true)
    protected String cServiceOrganizationName;
    @XmlElement(name = "CService_Organization_Number_", required = true)
    protected String cServiceOrganizationNumber;
    @XmlElement(name = "CProgram_Name_", required = true)
    protected String cProgramName;
    @XmlElement(name = "CProgram_Number_", required = true)
    protected String cProgramNumber;
    @XmlElement(name = "CFunction__MIS_Functional_Centre__", required = true)
    protected String cFunctionMISFunctionalCentre;

    /**
     * Gets the value of the cServiceOrganizationName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCServiceOrganizationName() {
        return cServiceOrganizationName;
    }

    /**
     * Sets the value of the cServiceOrganizationName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCServiceOrganizationName(String value) {
        this.cServiceOrganizationName = value;
    }

    /**
     * Gets the value of the cServiceOrganizationNumber property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCServiceOrganizationNumber() {
        return cServiceOrganizationNumber;
    }

    /**
     * Sets the value of the cServiceOrganizationNumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCServiceOrganizationNumber(String value) {
        this.cServiceOrganizationNumber = value;
    }

    /**
     * Gets the value of the cProgramName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCProgramName() {
        return cProgramName;
    }

    /**
     * Sets the value of the cProgramName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCProgramName(String value) {
        this.cProgramName = value;
    }

    /**
     * Gets the value of the cProgramNumber property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCProgramNumber() {
        return cProgramNumber;
    }

    /**
     * Sets the value of the cProgramNumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCProgramNumber(String value) {
        this.cProgramNumber = value;
    }

    /**
     * Gets the value of the cFunctionMISFunctionalCentre property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCFunctionMISFunctionalCentre() {
        return cFunctionMISFunctionalCentre;
    }

    /**
     * Sets the value of the cFunctionMISFunctionalCentre property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCFunctionMISFunctionalCentre(String value) {
        this.cFunctionMISFunctionalCentre = value;
    }

}
