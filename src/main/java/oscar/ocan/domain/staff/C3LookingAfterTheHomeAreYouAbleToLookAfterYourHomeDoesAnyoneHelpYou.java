/**
 *
 * Copyright (c) 2005-2012. Centre for Research on Inner City Health, St. Michael's Hospital, Toronto. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for
 * Centre for Research on Inner City Health, St. Michael's Hospital,
 * Toronto, Ontario, Canada
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vhudson-jaxb-ri-2.1-793 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2009.05.24 at 10:52:14 PM EDT 
//


package oscar.ocan.domain.staff;

import java.math.BigInteger;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{}C1__Does_the_person_have_difficulty_looking_after_the_home___If_rated_0_or_9__go_to_the_next_dom"/>
 *         &lt;element ref="{}C2__How_much_help_with_looking_after_the_home_does_the_person_receive_from_friends_or_relatives_"/>
 *         &lt;element ref="{}C3a__How_much_help_with_looking_after_the_home_does_the_person_receive_from_local_services_"/>
 *         &lt;element ref="{}C3b__How_much_help_with_looking_after_the_home_does_the_person_need_from_local_services_"/>
 *         &lt;element ref="{}CComments_"/>
 *         &lt;element ref="{}CActions_"/>
 *         &lt;element ref="{}CBy_whom_"/>
 *         &lt;element ref="{}CReview_date_"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "c1DoesThePersonHaveDifficultyLookingAfterTheHomeIfRated0Or9GoToTheNextDom",
    "c2HowMuchHelpWithLookingAfterTheHomeDoesThePersonReceiveFromFriendsOrRelatives",
    "c3AHowMuchHelpWithLookingAfterTheHomeDoesThePersonReceiveFromLocalServices",
    "c3BHowMuchHelpWithLookingAfterTheHomeDoesThePersonNeedFromLocalServices",
    "cComments",
    "cActions",
    "cByWhom",
    "cReviewDate"
})
@XmlRootElement(name = "C3__Looking_after_the_home__Are_you_able_to_look_after_your_home__Does_anyone_help_you_")
public class C3LookingAfterTheHomeAreYouAbleToLookAfterYourHomeDoesAnyoneHelpYou {

    @XmlElement(name = "C1__Does_the_person_have_difficulty_looking_after_the_home___If_rated_0_or_9__go_to_the_next_dom", required = true)
    protected BigInteger c1DoesThePersonHaveDifficultyLookingAfterTheHomeIfRated0Or9GoToTheNextDom;
    @XmlElement(name = "C2__How_much_help_with_looking_after_the_home_does_the_person_receive_from_friends_or_relatives_", required = true)
    protected BigInteger c2HowMuchHelpWithLookingAfterTheHomeDoesThePersonReceiveFromFriendsOrRelatives;
    @XmlElement(name = "C3a__How_much_help_with_looking_after_the_home_does_the_person_receive_from_local_services_", required = true)
    protected BigInteger c3AHowMuchHelpWithLookingAfterTheHomeDoesThePersonReceiveFromLocalServices;
    @XmlElement(name = "C3b__How_much_help_with_looking_after_the_home_does_the_person_need_from_local_services_", required = true)
    protected BigInteger c3BHowMuchHelpWithLookingAfterTheHomeDoesThePersonNeedFromLocalServices;
    @XmlElement(name = "CComments_", required = true)
    protected CComments cComments;
    @XmlElement(name = "CActions_", required = true)
    protected String cActions;
    @XmlElement(name = "CBy_whom_", required = true)
    protected String cByWhom;
    @XmlElement(name = "CReview_date_", required = true)
    protected String cReviewDate;

    /**
     * Gets the value of the c1DoesThePersonHaveDifficultyLookingAfterTheHomeIfRated0Or9GoToTheNextDom property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getC1DoesThePersonHaveDifficultyLookingAfterTheHomeIfRated0Or9GoToTheNextDom() {
        return c1DoesThePersonHaveDifficultyLookingAfterTheHomeIfRated0Or9GoToTheNextDom;
    }

    /**
     * Sets the value of the c1DoesThePersonHaveDifficultyLookingAfterTheHomeIfRated0Or9GoToTheNextDom property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setC1DoesThePersonHaveDifficultyLookingAfterTheHomeIfRated0Or9GoToTheNextDom(BigInteger value) {
        this.c1DoesThePersonHaveDifficultyLookingAfterTheHomeIfRated0Or9GoToTheNextDom = value;
    }

    /**
     * Gets the value of the c2HowMuchHelpWithLookingAfterTheHomeDoesThePersonReceiveFromFriendsOrRelatives property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getC2HowMuchHelpWithLookingAfterTheHomeDoesThePersonReceiveFromFriendsOrRelatives() {
        return c2HowMuchHelpWithLookingAfterTheHomeDoesThePersonReceiveFromFriendsOrRelatives;
    }

    /**
     * Sets the value of the c2HowMuchHelpWithLookingAfterTheHomeDoesThePersonReceiveFromFriendsOrRelatives property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setC2HowMuchHelpWithLookingAfterTheHomeDoesThePersonReceiveFromFriendsOrRelatives(BigInteger value) {
        this.c2HowMuchHelpWithLookingAfterTheHomeDoesThePersonReceiveFromFriendsOrRelatives = value;
    }

    /**
     * Gets the value of the c3AHowMuchHelpWithLookingAfterTheHomeDoesThePersonReceiveFromLocalServices property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getC3AHowMuchHelpWithLookingAfterTheHomeDoesThePersonReceiveFromLocalServices() {
        return c3AHowMuchHelpWithLookingAfterTheHomeDoesThePersonReceiveFromLocalServices;
    }

    /**
     * Sets the value of the c3AHowMuchHelpWithLookingAfterTheHomeDoesThePersonReceiveFromLocalServices property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setC3AHowMuchHelpWithLookingAfterTheHomeDoesThePersonReceiveFromLocalServices(BigInteger value) {
        this.c3AHowMuchHelpWithLookingAfterTheHomeDoesThePersonReceiveFromLocalServices = value;
    }

    /**
     * Gets the value of the c3BHowMuchHelpWithLookingAfterTheHomeDoesThePersonNeedFromLocalServices property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getC3BHowMuchHelpWithLookingAfterTheHomeDoesThePersonNeedFromLocalServices() {
        return c3BHowMuchHelpWithLookingAfterTheHomeDoesThePersonNeedFromLocalServices;
    }

    /**
     * Sets the value of the c3BHowMuchHelpWithLookingAfterTheHomeDoesThePersonNeedFromLocalServices property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setC3BHowMuchHelpWithLookingAfterTheHomeDoesThePersonNeedFromLocalServices(BigInteger value) {
        this.c3BHowMuchHelpWithLookingAfterTheHomeDoesThePersonNeedFromLocalServices = value;
    }

    /**
     * Gets the value of the cComments property.
     * 
     * @return
     *     possible object is
     *     {@link CComments }
     *     
     */
    public CComments getCComments() {
        return cComments;
    }

    /**
     * Sets the value of the cComments property.
     * 
     * @param value
     *     allowed object is
     *     {@link CComments }
     *     
     */
    public void setCComments(CComments value) {
        this.cComments = value;
    }

    /**
     * Gets the value of the cActions property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCActions() {
        return cActions;
    }

    /**
     * Sets the value of the cActions property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCActions(String value) {
        this.cActions = value;
    }

    /**
     * Gets the value of the cByWhom property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCByWhom() {
        return cByWhom;
    }

    /**
     * Sets the value of the cByWhom property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCByWhom(String value) {
        this.cByWhom = value;
    }

    /**
     * Gets the value of the cReviewDate property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCReviewDate() {
        return cReviewDate;
    }

    /**
     * Sets the value of the cReviewDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCReviewDate(String value) {
        this.cReviewDate = value;
    }

}
