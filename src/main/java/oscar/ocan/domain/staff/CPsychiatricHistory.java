/**
 *
 * Copyright (c) 2005-2012. Centre for Research on Inner City Health, St. Michael's Hospital, Toronto. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for
 * Centre for Research on Inner City Health, St. Michael's Hospital,
 * Toronto, Ontario, Canada
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vhudson-jaxb-ri-2.1-793 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2009.05.24 at 10:52:14 PM EDT 
//


package oscar.ocan.domain.staff;

import java.math.BigInteger;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;choice>
 *         &lt;element ref="{}CAdditional_information_"/>
 *         &lt;sequence>
 *           &lt;element ref="{}CHave_you_been_hospitalized_due_to_your_mental_health_during_the_past_two_years_"/>
 *           &lt;element ref="{}CIf_yes__total_number_of_admission__last_two_years__"/>
 *           &lt;element ref="{}CIf_yes__total_number_of_hospitalization_days__last_two_years__"/>
 *         &lt;/sequence>
 *       &lt;/choice>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "cAdditionalInformation",
    "cHaveYouBeenHospitalizedDueToYourMentalHealthDuringThePastTwoYears",
    "cIfYesTotalNumberOfAdmissionLastTwoYears",
    "cIfYesTotalNumberOfHospitalizationDaysLastTwoYears"
})
@XmlRootElement(name = "CPsychiatric_History")
public class CPsychiatricHistory {

    @XmlElement(name = "CAdditional_information_")
    protected String cAdditionalInformation;
    @XmlElement(name = "CHave_you_been_hospitalized_due_to_your_mental_health_during_the_past_two_years_")
    protected String cHaveYouBeenHospitalizedDueToYourMentalHealthDuringThePastTwoYears;
    @XmlElement(name = "CIf_yes__total_number_of_admission__last_two_years__")
    protected BigInteger cIfYesTotalNumberOfAdmissionLastTwoYears;
    @XmlElement(name = "CIf_yes__total_number_of_hospitalization_days__last_two_years__")
    protected BigInteger cIfYesTotalNumberOfHospitalizationDaysLastTwoYears;

    /**
     * Gets the value of the cAdditionalInformation property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCAdditionalInformation() {
        return cAdditionalInformation;
    }

    /**
     * Sets the value of the cAdditionalInformation property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCAdditionalInformation(String value) {
        this.cAdditionalInformation = value;
    }

    /**
     * Gets the value of the cHaveYouBeenHospitalizedDueToYourMentalHealthDuringThePastTwoYears property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCHaveYouBeenHospitalizedDueToYourMentalHealthDuringThePastTwoYears() {
        return cHaveYouBeenHospitalizedDueToYourMentalHealthDuringThePastTwoYears;
    }

    /**
     * Sets the value of the cHaveYouBeenHospitalizedDueToYourMentalHealthDuringThePastTwoYears property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCHaveYouBeenHospitalizedDueToYourMentalHealthDuringThePastTwoYears(String value) {
        this.cHaveYouBeenHospitalizedDueToYourMentalHealthDuringThePastTwoYears = value;
    }

    /**
     * Gets the value of the cIfYesTotalNumberOfAdmissionLastTwoYears property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getCIfYesTotalNumberOfAdmissionLastTwoYears() {
        return cIfYesTotalNumberOfAdmissionLastTwoYears;
    }

    /**
     * Sets the value of the cIfYesTotalNumberOfAdmissionLastTwoYears property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setCIfYesTotalNumberOfAdmissionLastTwoYears(BigInteger value) {
        this.cIfYesTotalNumberOfAdmissionLastTwoYears = value;
    }

    /**
     * Gets the value of the cIfYesTotalNumberOfHospitalizationDaysLastTwoYears property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getCIfYesTotalNumberOfHospitalizationDaysLastTwoYears() {
        return cIfYesTotalNumberOfHospitalizationDaysLastTwoYears;
    }

    /**
     * Sets the value of the cIfYesTotalNumberOfHospitalizationDaysLastTwoYears property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setCIfYesTotalNumberOfHospitalizationDaysLastTwoYears(BigInteger value) {
        this.cIfYesTotalNumberOfHospitalizationDaysLastTwoYears = value;
    }

}
