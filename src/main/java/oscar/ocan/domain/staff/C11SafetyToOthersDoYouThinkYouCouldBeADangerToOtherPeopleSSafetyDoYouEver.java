/**
 *
 * Copyright (c) 2005-2012. Centre for Research on Inner City Health, St. Michael's Hospital, Toronto. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for
 * Centre for Research on Inner City Health, St. Michael's Hospital,
 * Toronto, Ontario, Canada
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vhudson-jaxb-ri-2.1-793 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2009.05.24 at 10:52:14 PM EDT 
//


package oscar.ocan.domain.staff;

import java.math.BigInteger;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{}C1__Is_the_person_a_current_or_potential_risk_to_other_people_s_safety___If_rated_0_or_9__go_to_"/>
 *         &lt;element ref="{}C2__How_much_help_does_the_person_receive_from_friends_or_relatives_to_reduce_the_risk_that_he_o"/>
 *         &lt;element ref="{}C3a__How_much_help_does_the_person_receive_from_local_services_to_reduce_the_risk_that_he_or_she"/>
 *         &lt;element ref="{}C3b__How_much_help_does_the_person_need_from_local_services_to_reduce_the_risk_that_he_or_she_mi"/>
 *         &lt;element ref="{}CComments_"/>
 *         &lt;element ref="{}CActions_"/>
 *         &lt;element ref="{}CReview_date_"/>
 *         &lt;element ref="{}CBy_whom_"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "c1IsThePersonACurrentOrPotentialRiskToOtherPeopleSSafetyIfRated0Or9GoTo",
    "c2HowMuchHelpDoesThePersonReceiveFromFriendsOrRelativesToReduceTheRiskThatHeO",
    "c3AHowMuchHelpDoesThePersonReceiveFromLocalServicesToReduceTheRiskThatHeOrShe",
    "c3BHowMuchHelpDoesThePersonNeedFromLocalServicesToReduceTheRiskThatHeOrSheMi",
    "cComments",
    "cActions",
    "cReviewDate",
    "cByWhom"
})
@XmlRootElement(name = "C11__Safety_to_others__Do_you_think_you_could_be_a_danger_to_other_people_s_safety__Do_you_ever_")
public class C11SafetyToOthersDoYouThinkYouCouldBeADangerToOtherPeopleSSafetyDoYouEver {

    @XmlElement(name = "C1__Is_the_person_a_current_or_potential_risk_to_other_people_s_safety___If_rated_0_or_9__go_to_", required = true)
    protected BigInteger c1IsThePersonACurrentOrPotentialRiskToOtherPeopleSSafetyIfRated0Or9GoTo;
    @XmlElement(name = "C2__How_much_help_does_the_person_receive_from_friends_or_relatives_to_reduce_the_risk_that_he_o", required = true)
    protected BigInteger c2HowMuchHelpDoesThePersonReceiveFromFriendsOrRelativesToReduceTheRiskThatHeO;
    @XmlElement(name = "C3a__How_much_help_does_the_person_receive_from_local_services_to_reduce_the_risk_that_he_or_she", required = true)
    protected BigInteger c3AHowMuchHelpDoesThePersonReceiveFromLocalServicesToReduceTheRiskThatHeOrShe;
    @XmlElement(name = "C3b__How_much_help_does_the_person_need_from_local_services_to_reduce_the_risk_that_he_or_she_mi", required = true)
    protected BigInteger c3BHowMuchHelpDoesThePersonNeedFromLocalServicesToReduceTheRiskThatHeOrSheMi;
    @XmlElement(name = "CComments_", required = true)
    protected CComments cComments;
    @XmlElement(name = "CActions_", required = true)
    protected String cActions;
    @XmlElement(name = "CReview_date_", required = true)
    protected String cReviewDate;
    @XmlElement(name = "CBy_whom_", required = true)
    protected String cByWhom;

    /**
     * Gets the value of the c1IsThePersonACurrentOrPotentialRiskToOtherPeopleSSafetyIfRated0Or9GoTo property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getC1IsThePersonACurrentOrPotentialRiskToOtherPeopleSSafetyIfRated0Or9GoTo() {
        return c1IsThePersonACurrentOrPotentialRiskToOtherPeopleSSafetyIfRated0Or9GoTo;
    }

    /**
     * Sets the value of the c1IsThePersonACurrentOrPotentialRiskToOtherPeopleSSafetyIfRated0Or9GoTo property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setC1IsThePersonACurrentOrPotentialRiskToOtherPeopleSSafetyIfRated0Or9GoTo(BigInteger value) {
        this.c1IsThePersonACurrentOrPotentialRiskToOtherPeopleSSafetyIfRated0Or9GoTo = value;
    }

    /**
     * Gets the value of the c2HowMuchHelpDoesThePersonReceiveFromFriendsOrRelativesToReduceTheRiskThatHeO property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getC2HowMuchHelpDoesThePersonReceiveFromFriendsOrRelativesToReduceTheRiskThatHeO() {
        return c2HowMuchHelpDoesThePersonReceiveFromFriendsOrRelativesToReduceTheRiskThatHeO;
    }

    /**
     * Sets the value of the c2HowMuchHelpDoesThePersonReceiveFromFriendsOrRelativesToReduceTheRiskThatHeO property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setC2HowMuchHelpDoesThePersonReceiveFromFriendsOrRelativesToReduceTheRiskThatHeO(BigInteger value) {
        this.c2HowMuchHelpDoesThePersonReceiveFromFriendsOrRelativesToReduceTheRiskThatHeO = value;
    }

    /**
     * Gets the value of the c3AHowMuchHelpDoesThePersonReceiveFromLocalServicesToReduceTheRiskThatHeOrShe property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getC3AHowMuchHelpDoesThePersonReceiveFromLocalServicesToReduceTheRiskThatHeOrShe() {
        return c3AHowMuchHelpDoesThePersonReceiveFromLocalServicesToReduceTheRiskThatHeOrShe;
    }

    /**
     * Sets the value of the c3AHowMuchHelpDoesThePersonReceiveFromLocalServicesToReduceTheRiskThatHeOrShe property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setC3AHowMuchHelpDoesThePersonReceiveFromLocalServicesToReduceTheRiskThatHeOrShe(BigInteger value) {
        this.c3AHowMuchHelpDoesThePersonReceiveFromLocalServicesToReduceTheRiskThatHeOrShe = value;
    }

    /**
     * Gets the value of the c3BHowMuchHelpDoesThePersonNeedFromLocalServicesToReduceTheRiskThatHeOrSheMi property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getC3BHowMuchHelpDoesThePersonNeedFromLocalServicesToReduceTheRiskThatHeOrSheMi() {
        return c3BHowMuchHelpDoesThePersonNeedFromLocalServicesToReduceTheRiskThatHeOrSheMi;
    }

    /**
     * Sets the value of the c3BHowMuchHelpDoesThePersonNeedFromLocalServicesToReduceTheRiskThatHeOrSheMi property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setC3BHowMuchHelpDoesThePersonNeedFromLocalServicesToReduceTheRiskThatHeOrSheMi(BigInteger value) {
        this.c3BHowMuchHelpDoesThePersonNeedFromLocalServicesToReduceTheRiskThatHeOrSheMi = value;
    }

    /**
     * Gets the value of the cComments property.
     * 
     * @return
     *     possible object is
     *     {@link CComments }
     *     
     */
    public CComments getCComments() {
        return cComments;
    }

    /**
     * Sets the value of the cComments property.
     * 
     * @param value
     *     allowed object is
     *     {@link CComments }
     *     
     */
    public void setCComments(CComments value) {
        this.cComments = value;
    }

    /**
     * Gets the value of the cActions property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCActions() {
        return cActions;
    }

    /**
     * Sets the value of the cActions property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCActions(String value) {
        this.cActions = value;
    }

    /**
     * Gets the value of the cReviewDate property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCReviewDate() {
        return cReviewDate;
    }

    /**
     * Sets the value of the cReviewDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCReviewDate(String value) {
        this.cReviewDate = value;
    }

    /**
     * Gets the value of the cByWhom property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCByWhom() {
        return cByWhom;
    }

    /**
     * Sets the value of the cByWhom property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCByWhom(String value) {
        this.cByWhom = value;
    }

}
