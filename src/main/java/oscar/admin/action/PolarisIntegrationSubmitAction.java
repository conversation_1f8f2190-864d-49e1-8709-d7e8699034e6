/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package oscar.admin.action;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.var;
import org.apache.struts.Globals;
import org.apache.struts.action.Action;
import org.apache.struts.action.ActionErrors;
import org.apache.struts.action.ActionForm;
import org.apache.struts.action.ActionForward;
import org.apache.struts.action.ActionMapping;
import org.oscarehr.common.encryption.StringEncryptor;
import org.oscarehr.integration.polaris.PolarisCredentialStore;
import org.oscarehr.integration.polaris.PolarisUtils;
import org.oscarehr.integration.polaris.model.PolarisConfiguration;
import org.oscarehr.integration.polaris.service.PolarisConfigurationService;
import org.oscarehr.managers.SecurityInfoManager;
import org.oscarehr.util.LoggedInInfo;
import org.oscarehr.util.SpringUtils;

public class PolarisIntegrationSubmitAction extends Action {

  private final SecurityInfoManager securityInfoManager =
      SpringUtils.getBean(SecurityInfoManager.class);
  private final PolarisConfigurationService polarisConfigurationService =
      SpringUtils.getBean(PolarisConfigurationService.class);

  @Override
  public ActionForward execute(
      ActionMapping mapping,
      ActionForm form,
      HttpServletRequest request,
      HttpServletResponse response)
      throws Exception {
    if (!securityInfoManager.hasPrivilege(
        LoggedInInfo.getLoggedInInfoFromSession(request), "_admin", "w", null)) {
      throw new SecurityException("missing required security object (_admin)");
    }

    PolarisIntegrationForm polarisForm = (PolarisIntegrationForm) form;

    ActionErrors errors = polarisForm.validate(mapping, request);
    if (errors != null && !errors.isEmpty()) {
      request.setAttribute(Globals.ERROR_KEY, errors);

      return mapping.getInputForward();
    }

    var polarisConfiguration = polarisConfigurationService.getPolarisConfiguration();
    if (polarisConfiguration == null) {
      polarisConfiguration = new PolarisConfiguration();
    }

    polarisConfiguration.setAdminPolarisFhirEndpoint(polarisForm.getPolarisFhirEndpoint());
    polarisConfiguration.setAdminPolarisFhirClientId(polarisForm.getPolarisClientId());
    polarisConfiguration.setAdminPolarisFhirClientSecret(
        StringEncryptor.encrypt(polarisForm.getPolarisClientSecret()));
    polarisConfiguration.setAdminPolarisBasePath(
        PolarisUtils.getBasePath(polarisForm.getPolarisFhirEndpoint()));

    polarisConfigurationService.savePolarisConfiguration(polarisConfiguration);
    PolarisCredentialStore.reset();
    PolarisCredentialStore.init();

    return mapping.findForward("result");
  }
}
