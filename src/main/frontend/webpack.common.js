const glob = require("glob");
const path = require('path');
const webpack = require('webpack');
const CleanWebpackPlugin = require('clean-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const CreateTemplates = require('./create_templates');

const basePath = __dirname;
const targetPath = '../webapp';
const targetFolder = 'web';


module.exports = {

	// Set type of source map to use.  See https://webpack.js.org/configuration/devtool/
	devtool: 'source-map',

	module: {
		rules: [
			{
				// Typescript loader
				test: /\.tsx?$/,
				use: 'ts-loader'
			},
			{
				// URL loader.  Falls back to file loader for large files.
				test: /\.(woff(2)?|ttf|eot|svg|gif|png)(\?v=\d+\.\d+\.\d+)?$/,
				loader: 'url-loader',
				options: {
					limit: 10000,
				}
			},
			{
				// html loader
				test: /\.html?$/,
				use: 'html-loader'
			},
		]
	},
	plugins: [
		// Clears out the release folder before filling it back up
		new CleanWebpackPlugin([targetFolder], {
			root: basePath + '/' + targetPath
		}),

		// Fixes warning in moment-with-locales.min.js
        //   Module not found: Error: Can't resolve './locale' in ...
		new webpack.IgnorePlugin(/\.\/locale$/),

		// Provide access to various libraries from legacy code.
		new webpack.ProvidePlugin({
			$: "jquery",
			jQuery: "jquery",
			"window.jQuery": "jquery",
			moment: "moment",
			datepicker: "bootstrap-datepicker",
			pym: "pym.js"
		}),

		// Create the HTML to include webpack-created files
		new HtmlWebpackPlugin({
			template: 'raw-loader!./index.jsp',
			filename: 'index.jsp'
		}),
		new HtmlWebpackPlugin({
			template: 'raw-loader!./legacyFaxConfiguration.jsp',
			filename: 'legacyFaxConfiguration.jsp'
		}),
		new HtmlWebpackPlugin({
			template: 'raw-loader!./legacyFaxInbox.jsp',
			filename: 'legacyFaxInbox.jsp'
		}),
		new HtmlWebpackPlugin({
			template: 'raw-loader!./legacyFaxOutbox.jsp',
			filename: 'legacyFaxOutbox.jsp'
		}),
		new HtmlWebpackPlugin({
			template: 'raw-loader!./Know2actConfiguration.jsp',
			filename: 'Know2actConfiguration.jsp'
		}),
		new HtmlWebpackPlugin({
			template: 'raw-loader!./Know2actTemplate.jsp',
			filename: 'Know2actTemplate.jsp'
		}),
		new HtmlWebpackPlugin({
			template: 'raw-loader!./Know2actNotifications.jsp',
			filename: 'Know2actNotifications.jsp'
		}),

		// Copy files that aren't modified by webpack
		new CopyWebpackPlugin([

			// Copy templates
			{from: 'src/**/*.jsp'}
		],{}),

		new CreateTemplates(
			{from: 'src/**/*.jsp'}
		),
		new CopyWebpackPlugin([
			// Copy Polaris UMD file
			{ from: 'polaris/**/*.js'}
		], {}),
	],
	optimization: {

		// Split the file into chunks as webpack sees fit
		runtimeChunk: true,
		splitChunks: {
			chunks: 'all'
		}
	},
	resolve: {
		extensions: ['.tsx', '.ts', '.js']
	},
	output: {
		path: path.resolve(basePath + '/' + targetPath, targetFolder),

		// Name files with the chunk hash, that way if a chunk changes, it won't force reload of
		// the other chunks.
		filename: '[name].[chunkhash].js',
		chunkFilename: '[name].[chunkhash].js'
	},
	entry: {
		bundle: [
			'../webapp/share/javascript/Oscar.js',
			'../webapp/share/javascript/HealthCardParser.js',
			'./src/common/displayMessages.js',
			'./src/common/util/util.js',
			'./src/common/util/validations.js',
			'./src/common/util/serviceHelper.js',
			'./src/consults/common.js',
			'./app.js',
/*			'./node_modules/cp-calendar/module.js',
			'./node_modules/cp-calendar/directive.js',
			'./node_modules/cp-calendar/controller.js',
			'./node_modules/cp-calendar/event_controller.js',
			'./node_modules/cp-calendar/util.js',*/
			// TODO-legacy: move this back to node_modules after development
			/*
			'./cp-calendar/module.js',
			'./cp-calendar/directive.js',
			'./cp-calendar/controller.js',
			'./cp-calendar/event_controller.js',
			'./cp-calendar/util.js',
			*/
			'./src/common/module.js',

			'./src/common/util/module.js',
			'./src/common/util/angular-util.js',
			'./src/common/util/junoHttp.js',
			'./src/common/util/searchListHelper.js',

			'./src/common/services/module.js',
			'./src/common/services/appService.js',
			'./src/common/services/autoCompleteService.ts',
			'./src/common/services/billingService.js',
			'./src/common/services/consultService.ts',
			'./src/common/services/demographicService.ts',
			'./src/common/services/demographicsService.js',
			'./src/common/services/diseaseRegistryService.js',
			'./src/common/services/errorsService.js',
			'./src/common/services/eFormService.js',
			'./src/common/services/fieldHelperService.js',
			'./src/common/services/focusService.js',
			'./src/common/services/formService.js',
			'./src/common/services/globalStateService.js',
			'./src/common/services/inboxService.js',
			'./src/common/services/k2aService.js',
			'./src/common/services/labService.ts',
			'./src/common/services/measurementApi.service.ts',
			'./src/common/services/messageService.js',
			'./src/common/services/mhaService.js',
			'./src/common/services/noteService.js',
			'./src/common/services/patientDetailStatusService.js',
			'./src/common/services/personaService.js',
			'./src/common/services/programService.js',
			'./src/common/services/providerService.ts',
			'./src/common/services/reportingService.ts',
			'./src/common/services/resultsService.js',
			'./src/common/services/staticDataService.ts',
			'./src/common/services/summaryService.js',
			'./src/common/services/systemPreferenceService.js',
			'./src/common/services/ticklerService.js',
			'./src/common/services/reportByTemplateService.js',
			'./src/common/services/uxService.js',
			'./src/common/services/waitingListService.ts',
			'./src/common/services/polarisService.ts',
			'./src/record/module.js',
			'./src/lib/integration/polaris/service/CparScriptLoader.js',

			'./src/common/filters/module.js',
			'./src/common/filters/age.js',
			'./src/common/filters/cut.js',
			'./src/common/filters/offset.js',
			'./src/common/filters/startFrom.js',

			'./src/common/directives/module.js',
			'./src/common/directives/junoConfirmClick.js',
			'./src/common/directives/typeaheadHelper.js',
			'./src/common/directives/patientSearchTypeahead.js',
			'./src/common/directives/patientTypeahead.js',
			'./src/common/directives/datepickerPopup.js',
			'./src/common/directives/jqueryUIResizable.js',
			'./src/common/directives/jqueryUIDraggable.js',
			'./src/common/directives/ctxTransclude/ctxTransclude.js',
			'./src/common/directives/angular-ui-calendar.js',
			'./src/common/directives/selectOptionTooltip.js',

			'./src/common/directives/appointment/juno_appointment_status_select.js',

			'./src/common/directives/clinicaid/ca_field_alphadate.js',
			'./src/common/directives/clinicaid/ca_field_alpha.js',
			'./src/common/directives/clinicaid/ca_field_autocomplete.js',
			'./src/common/directives/clinicaid/ca_field_boolean.js',
			'./src/common/directives/clinicaid/ca_field_button_group.js',
			'./src/common/directives/clinicaid/ca_field_color.js',
			'./src/common/directives/clinicaid/ca_field_currency_filter.js',
			'./src/common/directives/clinicaid/ca_field_currency.js',
			'./src/common/directives/clinicaid/ca_field_date3.js',
			'./src/common/directives/clinicaid/ca_field_date.js',
			'./src/common/directives/clinicaid/ca_field_number.js',
			'./src/common/directives/clinicaid/ca_field_password.js',
			'./src/common/directives/clinicaid/ca_field_radio.js',
			'./src/common/directives/clinicaid/ca_field_select.js',
			'./src/common/directives/clinicaid/ca_field_text.js',
			'./src/common/directives/clinicaid/ca_field_time.js',
			'./src/common/directives/clinicaid/ca_field_toggle.js',
			'./src/common/directives/clinicaid/ca_focus_field.js',
			'./src/common/directives/clinicaid/ca_info_messages.js',
			'./src/common/directives/clinicaid/ca_key_bind_scope.js',
			'./src/common/directives/clinicaid/ca_pagination.js',
			'./src/common/directives/clinicaid/ca_quill.js',
			'./src/common/directives/clinicaid/ca_sticky_search_results_footer.js',
			'./src/common/directives/clinicaid/ca_zero_pad.js',
			'./src/common/directives/clinicaid/compile_html.js',
			'./src/common/directives/clinicaid/file_model.js',
			'./src/common/directives/clinicaid/resize_handler.js',
			'./src/common/directives/clinicaid/resize.js',
			'./src/common/directives/clinicaid/scroll_handler.js',
			'./src/common/directives/clinicaid/scroll.js',
			'./src/common/directives/clinicaid/zero_pad.js',

			'./src/common/components/module.js',
			'./src/common/components/modalComponent.js',
			'./src/common/components/filterPanel/filterPanelController.ts',
			'./src/common/components/formLabelWrapper/formLabelWrapperComponent.ts',
			'./src/common/components/junoButton/junoButtonComponent.js',
			'./src/common/components/junoButtonGroup/junoButtonGroupComponent.ts',
			'./src/common/components/junoRoundButton/junoRoundButtonComponent.js',
			'./src/common/components/junoSimpleCloseButton/junoSimpleCloseButtonComponent.ts',
			'./src/common/components/junoCheckBox/junoCheckBoxComponent.js',
			'./src/common/components/junoCodeInput/junoCodeInputComponent.js',
			'./src/common/components/junoFileChooser/junoFileChooserComponent.js',
			'./src/common/components/junoCollapseArea/junoCollapseAreaComponent.js',
			'./src/common/components/junoInput/junoInputComponent.js',
			'./src/common/components/junoSelectText/junoSelectTextComponent.ts',
			'./src/common/components/junoListItemSelector/junoListItemSelectorComponent.ts',
			'./src/common/components/junoInputSave/junoInputSaveComponent.js',
			'./src/common/components/junoPatientSelect/junoPatientSelectComponent.js',
			'./src/common/components/junoProgressBar/junoProgressBarComponent.js',
			'./src/common/components/junoSelect/junoSelectComponent.js',
			'./src/common/components/junoSelectTypeahead/junoSelectTypeaheadComponent.ts',
			'./src/common/components/junoTab/junoTabComponent.js',
			'./src/common/components/junoTab/components/module.js',
			'./src/common/components/junoTab/components/folderTab/folderTabComponent.js',
			'./src/common/components/junoTab/components/swimLaneTab/swimLaneTabComponent.js',
			'./src/common/components/junoDatePicker/junoDatePickerComponent.ts',
			'./src/common/components/junoDateSelect/junoDateSelectComponent.js',
			'./src/common/components/junoPartialDateSelect/junoPartialDateSelectComponent.js',
			...(glob.sync("./src/common/components/junoTable/**/*.ts", {nosort: true})),
			'./src/common/components/junoTimeSelect/junoTimeSelectComponent.js',
			'./src/common/components/junoDivider/junoDividerComponent.js',
			'./src/common/components/junoToast/junoToastComponent.js',
			'./src/common/components/junoToggle/junoToggleComponent.js',
			'./src/common/components/junoRadioSelect/junoRadioSelectComponent.ts',
			'./src/common/components/loadingIndicatorComponent.js',
			'./src/common/components/zeroStateDisplayComponent.js',
			'./src/common/components/accordionList/accordionListComponent.js',
			'./src/common/components/junoTypeahead/junoTypeaheadComponent.js',
			'./src/common/components/junoWarning/junoWarningComponent.ts',
			'./src/common/components/iconBadge/iconBadgeComponent.ts',
			'./src/common/components/iconCard/iconCardComponent.js',
			'./src/common/components/panel/panelComponent.js',
			'./src/common/components/security/junoSecurityCheckComponent.js',
			'./src/common/components/security/junoMissingSecurityComponent.js',

			'./src/common/layout/module.js',
			'./src/common/layout/pageWrapper/pageWrapperComponent.js',
			'./src/common/layout/printWrapper/printWrapperComponent.ts',

			'./src/common/modals/junoAlert/junoAlertComponent.js',
			'./src/common/modals/junoInputModal/junoInputModalComponent.js',
			'./src/common/modals/junoProgressModal/junoProgressModalComponent.js',
			'./src/common/modals/junoSimpleModal/junoSimpleModalComponent.js',
			...(glob.sync("./src/common/modals/mhaPatientDetailsModal/**/*.js", {nosort: true})),
			'./src/common/modals/appointmentQueueModal/appointmentQueueModalComponent.js',
			'./src/common/modals/bookAppointmentModal/bookAppointmentModalComponent.js',
			'./src/common/modals/appointmentQueueModal/components/availabilitySettings/availabilitySettingsComponent.js',

			'./src/common/security/module.js',
			'./src/common/security/securityApi.service.ts',
			'./src/common/security/securityRoles.service.ts',
			'./src/common/security/securityService.js',

			'./src/decisionSupport/module.js',
			'./src/decisionSupport/decisionSupportApi.service.ts',
			'./src/decisionSupport/rules/dsRuleEditModal/dsRuleEditModalComponent.ts',
			'./src/decisionSupport/rules/dsRuleBuilder/dsRuleBuilderComponent.ts',

			'./src/interceptor/module.js',
			'./src/interceptor/errorInterceptor.js',

			'./src/careTracker/module.js',
			'./src/careTracker/careTrackerManagerComponent.ts',
			'./src/careTracker/careTrackerEditComponent.ts',
			'./src/careTracker/careTrackerApi.service.ts',
			'./src/careTracker/components/careTrackerEditItem/careTrackerEditItemComponent.ts',
			'./src/careTracker/components/careTrackerItemGroup/careTrackerItemGroupComponent.ts',
			'./src/careTracker/components/careTrackerItemRule/careTrackerItemRuleComponent.ts',
			'./src/careTracker/components/careTrackerTrigger/careTrackerTriggerComponent.ts',

			'./src/layout/module.js',
			'./src/layout/bodyController.js',
			'./src/layout/primaryNavComponent.js',
			'./src/layout/leftAsideComponent.js',
			'./src/layout/toastArea/toastAreaComponent.js',
			'./src/layout/components/module.js',
			'./src/layout/components/appointmentQueue/appointmentQueueComponent.js',
			'./src/layout/components/appointmentQueue/components/appointmentCard/appointmentCardComponent.js',
			'./src/layout/components/appointmentQueue/modals/module.js',
			'./src/layout/components/appointmentQueue/modals/addQueuedAppointmentModal/addQueuedAppointmentModalComponent.ts',

			'./src/messaging/module.js',
			'./src/messaging/inbox/components/module.js',
			'./src/messaging/inbox/components/attachmentList/attachmentListComponent.js',
			'./src/messaging/inbox/components/inboxHeaderBar/inboxHeaderBarComponent.js',
			'./src/messaging/inbox/components/inboxSelect/inboxSelectComponent.js',
			'./src/messaging/inbox/components/messageableSearch/messageableSearchComponent.js',
			'./src/messaging/inbox/components/messageCard/messageCardComponent.js',
			'./src/messaging/inbox/components/messageList/messageListComponent.js',
			'./src/messaging/inbox/components/messageView/module.js',
			'./src/messaging/inbox/components/messageView/components/module.js',
			'./src/messaging/inbox/components/messageView/components/message/messageComponent.js',
			'./src/messaging/inbox/components/messageView/messageViewComponent.js',
			'./src/messaging/inbox/messagingInboxComponent.js',
			...(glob.sync("./src/messaging/inbox/modals/**/*.js", {nosort: true})),

			'./src/patient/module.js',
			'./src/patient/addDemographicModal/addDemographicModalComponent.ts',
			'./src/patient/demographicCardComponent.ts',
			'./src/patient/demographicApi.service.ts',

			'./src/dashboard/module.js',
			'./src/dashboard/dashboardController.js',
			'./src/dashboard/ticklerConfigureController.js',

			...(glob.sync("./src/integration/**/*.js", {nosort: true})),

			...(glob.sync("./src/record/components/**/*.js", {nosort: true})),
			...(glob.sync("./src/record/components/**/*.ts", {nosort: true})),
			...(glob.sync("./src/record/integration/**/*.js", {nosort: true})),
			...(glob.sync("./src/record/integration/**/*.ts", {nosort: true})),
			'./src/record/recordModule.js',
			'./src/record/recordController.js',
			'./src/record/summary/module.js',
			'./src/record/summary/summaryController.js',
			'./src/record/summary/recordPrintController.js',
			'./src/record/summary/components/groupNotes/groupNotesComponent.js',
			'./src/record/summary/components/summaryModule/summaryModuleComponent.js',
			'./src/record/summary/components/encounterNote/encounterNoteComponent.js',
			'./src/record/summary/components/encounterNoteList/encounterNoteListComponent.js',
			'./src/record/forms/module.js',
			'./src/record/forms/formsController.js',
			'./src/record/forms/components/formView/formViewComponent.js',
			'./src/record/forms/components/modeSelector/modeSelectorComponent.js',
			'./src/record/forms/components/groupSelector/groupSelectorComponent.js',
			'./src/record/details/module.js',
			'./src/record/details/detailsComponent.ts',
			'./src/record/details/swipecardController.js',
			'./src/record/details/components/mhaPatientConnection/mhaPatientConnectionComponent.js',
			'./src/record/details/components/demographicSection/demographicSectionComponent.js',
			'./src/record/details/components/contactSection/contactSectionComponent.js',
			'./src/record/details/components/demographicContactsSection/demographicContactsSectionComponent.js',
			'./src/record/details/components/healthInsuranceSection/healthInsuranceSectionComponent.js',
			'./src/record/details/components/careTeamSection/careTeamSectionComponent.js',
			'./src/record/details/components/rosterDisplay/rosterDisplayComponent.js',
			'./src/record/details/components/rosteredHistory/rosteredHistoryModalComponent.js',
			'./src/record/details/components/additionalInformationSection/additionalInformationSectionComponent.js',
			'./src/record/details/components/demographicContactsModal/demographicContactsModalComponent.js',
			'./src/record/details/components/patientInviteConfirmModal/mhaPatientInviteConfirmModalComponent.js',
			'./src/record/nav/module.js',
			'./src/record/nav/recordNavComponent.ts',
			'./src/record/phr/module.js',
			'./src/record/phr/phrController.js',

			'./src/record/healthTracker/module.js',
			'./src/record/healthTracker/healthTrackerComponent.ts',
			'./src/record/healthTracker/healthTrackerPageComponent.ts',
			'./src/record/healthTracker/careTracker/module.js',
			'./src/record/healthTracker/careTracker/careTrackerComponent.ts',
			'./src/record/healthTracker/careTracker/components/careTrackerItem/careTrackerItemComponent.ts',
			'./src/record/healthTracker/careTracker/components/careTrackerItemData/careTrackerItemDataComponent.ts',
			'./src/record/healthTracker/careTracker/components/careTrackerItemGraphModal/careTrackerItemGraphModalComponent.ts',
			'./src/record/healthTracker/measurement/module.js',
			'./src/record/healthTracker/measurement/measurementPageComponent.ts',

			'./src/tickler/module.js',
			'./src/tickler/ticklerListComponent.js',
			'./src/tickler/ticklerViewController.js',
			'./src/tickler/ticklerAddComponent.js',
			'./src/tickler/ticklerNoteController.js',
			'./src/tickler/ticklerCommentController.js',

			'./src/schedule/module.js',
			'./src/schedule/scheduleController.js',
			'./src/schedule/eventComponent.js',
			'./src/schedule/scheduleSearchComponent.js',
			'./src/schedule/scheduleService.js',

			'./src/admin/module.js',
			'./src/admin/adminController.js',
			'./src/admin/section/module.js',
			'./src/admin/section/fax/module.js',
			'./src/admin/section/fax/faxConfigurationComponent.ts',
			'./src/admin/section/fax/faxSendReceiveComponent.ts',
			'./src/admin/section/fax/components/faxConfigurationEditModal/faxConfigurationEditModalComponent.ts',
			'./src/admin/section/fax/components/faxInbox/faxInboxComponent.ts',
			'./src/admin/section/fax/components/faxOutbox/faxOutboxComponent.ts',
			'./src/admin/section/lab/module.js',
			'./src/admin/section/lab/config/labConfigComponent.ts',
			'./src/admin/section/lab/config/olis/module.js',
			'./src/admin/section/lab/config/olis/olisConfigComponent.ts',
			'./src/admin/section/landingPage/landingPageComponent.js',
			'./src/admin/section/panelManagement/panelManagementAdminComponent.ts',
			'./src/admin/section/editProviderPage/editProviderAdminComponent.js',
			'./src/admin/section/manageUserPage/manageUserAdminComponent.js',
			'./src/admin/section/securityRole/securityRoleConfigComponent.js',
			'./src/admin/section/securityRole/securityRoleConfigModalComponent.js',
			'./src/admin/section/securityRole/securityRoleSetModalComponent.js',
			'./src/admin/section/integrationModules/integrationModulesComponent.js',
			'./src/admin/section/hrm/HRMIndexComponent.js',
			'./src/admin/section/hrm/components/admin/HRMAdminComponent.js',
			'./src/admin/section/hrm/components/settings/HRMSettingsComponent.js',
			'./src/admin/section/hrm/components/category/HRMCategoryComponent.js',
			'./src/admin/section/hrm/modal/categoryDetails/categoryDetailsModalComponent.js',
			'./src/admin/section/iceFall/iceFallAdminComponent.js',
			'./src/admin/section/iceFall/settings/iceFallAdminSettingsComponent.js',
			'./src/admin/section/iceFall/activity/iceFallAdminActivityComponent.js',
			'./src/admin/section/imdHealth/imdHealthAdminComponent.js',
			'./src/admin/section/imdHealth/imdHealthLandingComponent.js',
			'./src/admin/section/know2act/module.js',
			'./src/admin/section/know2act/Know2actConfigController.js',
			'./src/admin/section/know2act/Know2actNotificationController.js',
			'./src/admin/section/know2act/Know2actTemplateController.js',
			'./src/admin/section/frameContent/frameContentController.js',
			'./src/admin/section/manageAppointmentQueuesPage/manageAppointmentQueuesComponent.ts',
			'./src/admin/section/mhaConfig/components/module.js',
			'./src/admin/section/mhaConfig/components/integrationList/integrationListComponent.js',
			'./src/admin/section/mhaConfig/components/integrationSettings/integrationSettingsComponent.js',
			'./src/admin/section/mhaConfig/mhaConfigComponent.js',
			'./src/admin/section/ontarioEhr/module.js',
			'./src/admin/section/ontarioEhr/components/addUpdateAuthorityModal/addUpdateAuthorityModalComponent.ts',
			'./src/admin/section/ontarioEhr/components/addUpdateServiceModal/addUpdateServiceModalComponent.ts',
			'./src/admin/section/ontarioEhr/components/addUpdateViewletModal/addUpdateViewletModalComponent.ts',
			'./src/admin/section/ontarioEhr/components/authorityConfig/authorityConfigComponent.ts',
			'./src/admin/section/ontarioEhr/components/serviceConfig/serviceConfigComponent.ts',
			'./src/admin/section/ontarioEhr/components/viewletConfig/viewletConfigComponent.ts',
			'./src/admin/section/ontarioEhr/ontarioEhrConfigComponent.ts',
			'./src/admin/section/dataManagement/module.js',
			'./src/admin/section/dataManagement/demographicImport/demographicImportComponent.js',
			'./src/admin/section/dataManagement/demographicExport/demographicExportComponent.js',
			'./src/admin/systemProperties/systemPropertiesComponent.js',
			'./src/admin/systemProperties/rx/propertiesRxComponent.js',
			'./src/admin/systemProperties/general/propertiesGeneralComponent.js',
			'./src/admin/systemProperties/inbox/propertiesInboxComponent.ts',
			'./src/admin/systemProperties/billing/propertiesBillingComponent.js',
			'./src/admin/systemProperties/uiValidation/propertiesUiValidationComponent.ts',
			'./src/admin/systemProperties/apiWhitelist/propertiesApiWhitelistComponent.ts',
			'./src/admin/systemProperties/components/junoSelectSave/junoSelectSaveComponent.js',
			'./src/admin/section/rosterStatus/rosterStatusManagementComponent.js',
			'./src/admin/section/rosterStatus/modals/editRosterStatusModalComponent.js',

			'./src/auth/module.js',
			'./src/auth/logout/logoutComponent.ts',

			'./src/billing/billingController.js',

			'./src/consults/module.js',
			...(glob.sync("./src/consults/**/*.ts", {nosort: true})),
			'./src/consults/consultResponseAttachmentController.js',
			'./src/consults/consultRequestListController.js',
			'./src/consults/consultResponseListController.js',
			'./src/consults/consultResponseController.js',

			'./src/inbox/module.js',
			'./src/inbox/inboxController.js',

			'./src/patient/search/module.js',
			'./src/patient/search/patientSearchComponent.js',
			'./src/patient/search/remotePatientResultsController.js',

			'./src/report/module.js',
			'./src/report/reportsController.js',
			'./src/report/reportBadAppointmentSheetController.js',
			'./src/report/reportDaySheetController.js',
			'./src/report/reportEdbListController.js',
			'./src/report/reportNoShowAppointmentSheetController.js',
			'./src/report/reportOldPatientsController.js',
			'./src/report/reportPatientChartListController.js',
			...(glob.sync("./src/report/**/*.ts", {nosort: true})),

			'./src/document/module.js',
			'./src/document/documentsController.js',

			'./src/settings/module.js',
			'./src/settings/settingsController.js',
			'./src/settings/changePasswordController.js',
			'./src/settings/quickLinkController.js',
			'./src/settings/summaryItemSettingsController.js',
			...(glob.sync("./src/settings/**/*.ts", {nosort: true})),

			'./src/help/module.js',
			'./src/help/supportController.js',
			'./src/help/helpController.js',
			'./src/index.ts',
		]
	}
};