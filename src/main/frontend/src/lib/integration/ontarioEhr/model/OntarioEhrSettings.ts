export default class OntarioEhrSettings
{
	private _oAuthClientId: string;
	private _oAuthRefreshIntervalMinutes: number;
	private _gatewayClientId: string;
	private _gatewayPublicKey: string;
	private _gatewayTimeoutSeconds: number;
	private _viewletTimeoutSeconds: number;

	get oAuthClientId(): string {
		return this._oAuthClientId;
	}

	set oAuthClientId(value: string) {
		this._oAuthClientId = value;
	}

	get oAuthRefreshIntervalMinutes(): number {
		return this._oAuthRefreshIntervalMinutes;
	}

	set oAuthRefreshIntervalMinutes(value: number) {
		this._oAuthRefreshIntervalMinutes = value;
	}

	get gatewayClientId(): string
	{
		return this._gatewayClientId;
	}

	set gatewayClientId(value: string)
	{
		this._gatewayClientId = value;
	}

	get gatewayPublicKey(): string
	{
		return this._gatewayPublicKey;
	}

	set gatewayPublicKey(value: string)
	{
		this._gatewayPublicKey = value;
	}

	get gatewayTimeoutSeconds(): number
	{
		return this._gatewayTimeoutSeconds;
	}

	set gatewayTimeoutSeconds(value: number)
	{
		this._gatewayTimeoutSeconds = value;
	}

	get viewletTimeoutSeconds(): number
	{
		return this._viewletTimeoutSeconds;
	}

	set viewletTimeoutSeconds(value: number)
	{
		this._viewletTimeoutSeconds = value;
	}
}