import AbstractConverter from "../../../conversion/AbstractConverter";
import OntarioEhrDHDRSearchResult from "../model/OntarioEhrDHDRSearchResult";
import {DHDRSearchResultModel} from "../../../../../generated";
import moment from "moment/moment";
import PhoneNumberToModelConverter from "../../../common/converter/PhoneNumberToModelConverter";
import {OntarioDhdrCategoryType} from "../model/OntarioDhdrCategoryType";
import OntarioEhrDHDRPatient from "../model/OntarioEhrDHDRPatient";
import {Sex} from "../../../demographic/model/Sex";

export default class OntarioEhrDhdrResultToModelConverter extends AbstractConverter<DHDRSearchResultModel, OntarioEhrDHDRSearchResult>
{
	convert(from: DHDRSearchResultModel): OntarioEhrDHDRSearchResult
	{
		let model = null;
		if(from)
		{
			let phoneConverter = new PhoneNumberToModelConverter();

			model = new OntarioEhrDHDRSearchResult();
			model.category = from.category as OntarioDhdrCategoryType;
			model.dispenseDate = from.dispenseDate ? moment(from.dispenseDate) : null;
			model.pickedUpDate = from.pickedUpDate ? moment(from.pickedUpDate) : null;
			model.brandName = from.brandName;
			model.genericName = from.genericName;
			model.drugStrength = from.drugStrength;
			model.drugForm = from.drugForm;
			model.drugDIN = from.drugDIN;
			model.rxNumber = from.rxNumber;
			model.reasonForUse = from.reasonForUse;
			model.therapeuticClass = from.therapeuticClass;
			model.therapeuticSubClass = from.therapeuticSubClass;
			model.dosage = from.dosage;
			model.frequency = from.frequency;
			model.dispensedCount = from.dispensedCount;
			model.dispensedUnit = from.dispensedUnit;
			model.daysSupply = from.daysSupply;
			model.refills = from.refills;
			model.quantityRemaining = from.quantityRemaining

			model.pharmacyName = from.pharmacyName;
			model.pharmacistName = from.pharmacistName;
			model.pharmacyFax = phoneConverter.convert(from.pharmacyFax);
			model.pharmacyPhone = phoneConverter.convert(from.pharmacyPhone);

			model.prescriberName = from.prescriberName;
			model.prescriberProfessionalIdDescription = from.prescriberProfessionalIdDescription;
			model.prescriberProfessionalIdValue = from.prescriberProfessionalIdValue;
			model.prescriberPhone = phoneConverter.convert(from.prescriberPhone);

			let fromPatient = from.patient;
			if(fromPatient)
			{
				let patientModel = new OntarioEhrDHDRPatient();
				patientModel.sex = fromPatient.sex as any as Sex;
				patientModel.dateOfBirth = fromPatient.dateOfBirth ? moment(fromPatient.dateOfBirth) : null;
				patientModel.healthNumber = fromPatient.healthNumber;
				patientModel.lastName = fromPatient.lastName;
				patientModel.firstName = fromPatient.firstName;

				model.patient = patientModel;
			}
		}
		return model;
	}
}