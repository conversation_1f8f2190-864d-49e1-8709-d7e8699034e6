/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */

/**
 * Service for loading CPAR bundle scripts dynamically.
 * 
 * This service ensures that the CPAR bundle is loaded only once per page load,
 * and handles loading a new bundle if the URL changes (e.g., when an admin updates
 * the configuration).
 */
angular.module('Record').service('cparScriptLoader', [
  '$document', 
  '$sce', 
  '$q', 
  '$log',
  function ($document, $sce, $q, $log) {
    let loadedUrl = null;
    let loadPromise = null;

    /**
     * Load a CPAR bundle. Returns a promise that resolves when the file
     * finishes executing. If the same URL is requested again, it resolves
     * immediately. If a *different* URL is requested, it loads the new one.
     * 
     * @param {string} url - The URL of the CPAR bundle to load
     * @returns {Promise} A promise that resolves when the bundle is loaded
     */
    this.load = function (url) {
      if (!url) { 
        return $q.reject('No URL supplied'); 
      }

      if (url === loadedUrl && loadPromise) {
        return loadPromise;
      }

      loadedUrl = url;
      loadPromise = $q((resolve, reject) => {
        const node = document.createElement('script');
        node.type = 'text/javascript';
        node.src = $sce.getTrustedResourceUrl(url);
        node.onload = () => { 
          $log.debug('CPAR bundle loaded:', url); 
          resolve(); 
        };
        node.onerror = (e) => { 
          reject('Failed to load CPAR bundle: ' + url); 
        };

        $document[0].body.appendChild(node);
      });

      return loadPromise;
    };
  }
]);