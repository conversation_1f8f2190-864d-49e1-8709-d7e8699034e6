import AbstractConverter from "../../../conversion/AbstractConverter";
import {ProfessionalSpecialistTo1} from "../../../../../generated";
import ProfessionalSpecialist from "../model/ProfessionalSpecialist";
import Address from "../../../common/model/Address";
import {AddressResidencyStatus} from "../../../common/model/AddressResidencyStatus";
import PhoneNumber from "../../../common/model/PhoneNumber";
import {PhoneType} from "../../../common/model/PhoneType";

export default class ProfessionalSpecialistModelConverter extends AbstractConverter<ProfessionalSpecialistTo1, ProfessionalSpecialist>
{
	public convert(from: ProfessionalSpecialistTo1): ProfessionalSpecialist
	{
		if (!from)
		{
			return null;
		}

		let model = new ProfessionalSpecialist(from.id);

		model.firstName = from.firstName;
		model.lastName = from.lastName;
		model.professionalLetters = from.professionalLetters;

		if(from.streetAddress)
		{
			let address = new Address(AddressResidencyStatus.Current);
			address.addressLine1 = from.streetAddress;
			model.address = address;
		}

		if(from.phoneNumber)
		{
			model.phoneNumber = new PhoneNumber(from.phoneNumber, null, PhoneType.Work);
		}
		if(from.faxNumber)
		{
			model.faxNumber = new PhoneNumber(from.faxNumber, null, PhoneType.Fax);
		}
		model.emailAddress = from.emailAddress;
		model.specialtyType = from.specialtyType;
		model.referralNo = from.referralNo;
		model.annotation = from.annotation;

		return model;
	}
}