export default class SpecialistSearchParams
{
	private _name: string;
	private _referralNumber: string;
	private _serviceId: number;
	private _page: number;
	private _perPage: number;

	get name(): string
	{
		return this._name;
	}

	set name(value: string)
	{
		this._name = value;
	}

	get referralNumber(): string
	{
		return this._referralNumber;
	}

	set referralNumber(value: string)
	{
		this._referralNumber = value;
	}

	get serviceId(): number
	{
		return this._serviceId;
	}

	set serviceId(value: number)
	{
		this._serviceId = value;
	}

	get page(): number
	{
		return this._page;
	}

	set page(value: number)
	{
		this._page = value;
	}

	get perPage(): number
	{
		return this._perPage;
	}

	set perPage(value: number)
	{
		this._perPage = value;
	}
}