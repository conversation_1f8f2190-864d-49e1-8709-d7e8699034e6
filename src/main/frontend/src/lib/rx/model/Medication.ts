import {FrequencyCode, MedicationModel, ResidualInfo} from "../../../../generated";
import SimpleProvider from "../../provider/model/SimpleProvider";
import moment, {Moment} from "moment/moment";
import PartialDateModel from "../../common/partialDate/model/partialDateModel";

export default class Medication
{
	private readonly id: number;

	private _prescribingProvider: SimpleProvider;
	private _outsideProviderName: string;
	private _outsideProviderOhip: string;
	private _writtenDate: PartialDateModel;
	private _rxStartDate: PartialDateModel;
	private _rxEndDate: PartialDateModel;
	private _createdDateTime: Moment;
	private _patientCompliance: boolean;
	private _pickupDateTime: Moment;
	private _rxStatus: MedicationModel.RxStatusEnum;
	private _scriptNo: number;
	private _drugForm: string;
	private _method: string;
	private _route: string;
	private _nonAuthoritative: boolean;
	private _regionalIdentifier: string;
	private _unit: string;
	private _takeMin: number;
	private _takeMax: number;
	private _frequencyCode: FrequencyCode;
	private _duration: string;
	private _genericName: string;
	private _durationUnit: string;
	private _quantity: string;
	private _repeat: number;
	private _longTerm: boolean;
	private _pastMed: boolean;
	private _dosage: string;
	private _strengthAmount: string;
	private _strengthUnit: string;
	private _lastRefillDate: string;
	private _refillDuration: number;
	private _refillQuantity: number;
	private _dispenseInterval: number;
	private _dispenseInternal: boolean;
	private _instructions: string;
	private _specialInstructions: string;
	private _comment: string;
	private _noSubs: boolean;
	private _archived: boolean;
	private _archivedReason: string;
	private _archivedDateTime: Moment;
	private _lastUpdateDateTime: Moment;
	private _residualInfo: ResidualInfo[];
	private _drugName: string;
	private _eTreatmentType: MedicationModel.EtreatmentTypeEnum;

	constructor(id)
	{
		this.id = id;
	}

	protected mapDurationUnit = (code: string, amount: number = 1): string =>
	{
		let suffix = (amount > 1) ? "s" : "";
		switch (code)
		{
			case "D": return "Day" + suffix;
			case "W": return "Week" + suffix;
			case "M": return "Month" + suffix;
			default: return code;
		}
	}

	get strengthAndFormDisplay(): string
	{
		let display: string = this.strengthAmount + " " + this.strengthUnit;
		if (this.drugForm)
		{
			display += " (" + this.drugForm + ")";
		}
		return display;
	}

	get frequencyDisplay(): string
	{
		let display: string = "";
		if(this.frequencyCode)
		{
			display += this.frequencyCode.code;
		}
		return display;
	}

	get instructionsDisplay(): string
	{
		if(this.takeMin === this.takeMax)
		{
			return String(this.takeMin);
		}
		return this.takeMin + "-" + this.takeMax;
	}

	get daysToExpire(): number
	{
		if(this.rxEndDate)
		{
			const now = moment();
			const endMoment = this.rxEndDate.toMoment();
			const remaining = endMoment.diff(now, 'days');
			return Math.max(remaining, 0);
		}
		return 0;
	}

	get dispenseDisplay(): string
	{
		let display = this.duration;
		if(this.durationUnit)
		{
			display += " " + this.mapDurationUnit(this.durationUnit, Number(this.duration));
		}
		return display;
	}

	// ====== standard getters and setters ======

	get prescribingProvider(): SimpleProvider
	{
		return this._prescribingProvider;
	}

	set prescribingProvider(value: SimpleProvider)
	{
		this._prescribingProvider = value;
	}

	get outsideProviderName(): string
	{
		return this._outsideProviderName;
	}

	set outsideProviderName(value: string)
	{
		this._outsideProviderName = value;
	}

	get outsideProviderOhip(): string
	{
		return this._outsideProviderOhip;
	}

	set outsideProviderOhip(value: string)
	{
		this._outsideProviderOhip = value;
	}

	get writtenDate(): PartialDateModel
	{
		return this._writtenDate;
	}

	set writtenDate(value: PartialDateModel)
	{
		this._writtenDate = value;
	}

	get rxStartDate(): PartialDateModel
	{
		return this._rxStartDate;
	}

	set rxStartDate(value: PartialDateModel)
	{
		this._rxStartDate = value;
	}

	get rxEndDate(): PartialDateModel
	{
		return this._rxEndDate;
	}

	set rxEndDate(value: PartialDateModel)
	{
		this._rxEndDate = value;
	}

	get createdDateTime(): Moment
	{
		return this._createdDateTime;
	}

	set createdDateTime(value: Moment)
	{
		this._createdDateTime = value;
	}

	get patientCompliance(): boolean
	{
		return this._patientCompliance;
	}

	set patientCompliance(value: boolean)
	{
		this._patientCompliance = value;
	}

	get pickupDateTime(): Moment
	{
		return this._pickupDateTime;
	}

	set pickupDateTime(value: Moment)
	{
		this._pickupDateTime = value;
	}

	get rxStatus(): MedicationModel.RxStatusEnum
	{
		return this._rxStatus;
	}

	set rxStatus(value: MedicationModel.RxStatusEnum)
	{
		this._rxStatus = value;
	}

	get scriptNo(): number
	{
		return this._scriptNo;
	}

	set scriptNo(value: number)
	{
		this._scriptNo = value;
	}

	get drugForm(): string
	{
		return this._drugForm;
	}

	set drugForm(value: string)
	{
		this._drugForm = value;
	}

	get method(): string
	{
		return this._method;
	}

	set method(value: string)
	{
		this._method = value;
	}

	get route(): string
	{
		return this._route;
	}

	set route(value: string)
	{
		this._route = value;
	}

	get nonAuthoritative(): boolean
	{
		return this._nonAuthoritative;
	}

	set nonAuthoritative(value: boolean)
	{
		this._nonAuthoritative = value;
	}

	get regionalIdentifier(): string
	{
		return this._regionalIdentifier;
	}

	set regionalIdentifier(value: string)
	{
		this._regionalIdentifier = value;
	}

	get unit(): string
	{
		return this._unit;
	}

	set unit(value: string)
	{
		this._unit = value;
	}

	get takeMin(): number
	{
		return this._takeMin;
	}

	set takeMin(value: number)
	{
		this._takeMin = value;
	}

	get takeMax(): number
	{
		return this._takeMax;
	}

	set takeMax(value: number)
	{
		this._takeMax = value;
	}

	get frequencyCode(): FrequencyCode
	{
		return this._frequencyCode;
	}

	set frequencyCode(value: FrequencyCode)
	{
		this._frequencyCode = value;
	}

	get duration(): string
	{
		return this._duration;
	}

	set duration(value: string)
	{
		this._duration = value;
	}

	get genericName(): string
	{
		return this._genericName;
	}

	set genericName(value: string)
	{
		this._genericName = value;
	}

	get durationUnit(): string
	{
		return this._durationUnit;
	}

	set durationUnit(value: string)
	{
		this._durationUnit = value;
	}

	get quantity(): string
	{
		return this._quantity;
	}

	set quantity(value: string)
	{
		this._quantity = value;
	}

	get repeat(): number
	{
		return this._repeat;
	}

	set repeat(value: number)
	{
		this._repeat = value;
	}

	get longTerm(): boolean
	{
		return this._longTerm;
	}

	set longTerm(value: boolean)
	{
		this._longTerm = value;
	}

	get pastMed(): boolean
	{
		return this._pastMed;
	}

	set pastMed(value: boolean)
	{
		this._pastMed = value;
	}

	get dosage(): string
	{
		return this._dosage;
	}

	set dosage(value: string)
	{
		this._dosage = value;
	}

	get strengthAmount(): string
	{
		return this._strengthAmount;
	}

	set strengthAmount(value: string)
	{
		this._strengthAmount = value;
	}

	get strengthUnit(): string
	{
		return this._strengthUnit;
	}

	set strengthUnit(value: string)
	{
		this._strengthUnit = value;
	}

	get lastRefillDate(): string
	{
		return this._lastRefillDate;
	}

	set lastRefillDate(value: string)
	{
		this._lastRefillDate = value;
	}

	get refillDuration(): number
	{
		return this._refillDuration;
	}

	set refillDuration(value: number)
	{
		this._refillDuration = value;
	}

	get refillQuantity(): number
	{
		return this._refillQuantity;
	}

	set refillQuantity(value: number)
	{
		this._refillQuantity = value;
	}

	get dispenseInterval(): number
	{
		return this._dispenseInterval;
	}

	set dispenseInterval(value: number)
	{
		this._dispenseInterval = value;
	}

	get dispenseInternal(): boolean
	{
		return this._dispenseInternal;
	}

	set dispenseInternal(value: boolean)
	{
		this._dispenseInternal = value;
	}

	get instructions(): string
	{
		return this._instructions;
	}

	set instructions(value: string)
	{
		this._instructions = value;
	}

	get specialInstructions(): string
	{
		return this._specialInstructions;
	}

	set specialInstructions(value: string)
	{
		this._specialInstructions = value;
	}

	get comment(): string
	{
		return this._comment;
	}

	set comment(value: string)
	{
		this._comment = value;
	}

	get noSubs(): boolean
	{
		return this._noSubs;
	}

	set noSubs(value: boolean)
	{
		this._noSubs = value;
	}

	get archived(): boolean
	{
		return this._archived;
	}

	set archived(value: boolean)
	{
		this._archived = value;
	}

	get archivedReason(): string
	{
		return this._archivedReason;
	}

	set archivedReason(value: string)
	{
		this._archivedReason = value;
	}

	get archivedDateTime(): Moment
	{
		return this._archivedDateTime;
	}

	set archivedDateTime(value: Moment)
	{
		this._archivedDateTime = value;
	}

	get lastUpdateDateTime(): Moment
	{
		return this._lastUpdateDateTime;
	}

	set lastUpdateDateTime(value: Moment)
	{
		this._lastUpdateDateTime = value;
	}

	get residualInfo(): ResidualInfo[]
	{
		return this._residualInfo;
	}

	set residualInfo(value: ResidualInfo[])
	{
		this._residualInfo = value;
	}

	get drugName(): string
	{
		return this._drugName;
	}

	set drugName(value: string)
	{
		this._drugName = value;
	}

	get eTreatmentType(): MedicationModel.EtreatmentTypeEnum
	{
		return this._eTreatmentType;
	}

	set eTreatmentType(value: MedicationModel.EtreatmentTypeEnum)
	{
		this._eTreatmentType = value;
	}
}