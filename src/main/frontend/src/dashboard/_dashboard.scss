// Copyright (c) 2001-2002. Department of Family Medicine, McMaster University. All Rights Reserved.
// This software is published under the GPL GNU General Public License.
// This program is free software; you can redistribute it and/or
// modify it under the terms of the GNU General Public License
// as published by the Free Software Foundation; either version 2
// of the License, or (at your option) any later version.
// This program is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
// GNU General Public License for more details.
// You should have received a copy of the GNU General Public License
// along with this program; if not, write to the Free Software
// Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
// This software was written for the
// Department of Family Medicine
// McMaster University
// Hamilton
// Ontario, Canada
#dashboard-page {
	#dashboard-header {
		@extend %juno-page-header;
		padding-right: 12px;

		//margin-bottom: $margin-md;
		h3 {
			color: $color-neutral-white;
		}
		h3, p {
			margin: 0;
		}
	}

	#dashboard-body {
		@extend %juno-primary-view-pane;
		@include juno-page-padding-default;

		.dashboard-row {
			margin-bottom: $margin-sm;
		}
		.dashboard-table {
			.dashboard-table-row {
				&:hover {
					background-color: $color-neutral-lighter;
				}
			}
		}
		#dashboard-body-right {
			overflow-y: auto;
		}
		.priority-flag {
			color: red;
		}

		.k2a-comment-container {
			p {
				overflow-wrap: break-word;
				text-align:left;
			}
		}
	}
}

@media (max-width: 672px) {
	#dashboard-page {
		#dashboard-header {
			h3 {
				font-size: 18px;
			}
		}
	}
}