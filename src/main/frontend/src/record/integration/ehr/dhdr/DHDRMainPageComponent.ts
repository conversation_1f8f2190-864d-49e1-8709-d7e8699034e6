/**
 * Copyright (c) 2012-2018. CloudPractice Inc. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for
 * CloudPractice Inc.
 * Victoria, British Columbia
 * Canada
 */

import ToastErrorHandler from "../../../../lib/error/handler/ToastErrorHandler";
import Demographic from "../../../../lib/demographic/model/Demographic";
import LoadingQueue from "../../../../lib/util/LoadingQueue";
import {JUNO_BUTTON_COLOR, JUNO_BUTTON_COLOR_PATTERN, JUNO_STYLE, LABEL_POSITION} from "../../../../common/components/junoComponentConstants";
import OntarioEhrDHDRSearchAlert from "../../../../lib/integration/ontarioEhr/model/OntarioEhrDHDRSearchAlert";
import {JunoSelectOption} from "../../../../lib/common/junoSelectOption";
import {DHDRSummaryViewType} from "./DHDRSummaryViewType";
import {SecurityPermissions} from "../../../../common/security/securityConstants";
import ToastService from "../../../../lib/alerts/service/ToastService";
import OntarioEhrService from "../../../../lib/integration/ontarioEhr/service/OntarioEhrService";
import moment from "moment/moment";

angular.module('Record.Integration.DHDR').component('dhdrMainPage',
	{
		templateUrl: 'src/record/integration/ehr/dhdr/DHDRMainPage.jsp',
		bindings: {
			componentStyle: "<?",
		},
		controller: [
			'$location',
			'$scope',
			'$state',
			'$filter',
			'$stateParams',
			'$uibModal',
			'NgTableParams',
			'demographicService',
			'providerService',
			'securityRolesService',
			function ($location,
			          $scope,
			          $state,
			          $filter,
			          $stateParams,
			          $uibModal,
			          NgTableParams,
			          demographicService,
					  providerService,
			          securityRolesService)
			{
				const ctrl = this;

				ctrl.LABEL_POSITION = LABEL_POSITION;
				ctrl.JUNO_BUTTON_COLOR = JUNO_BUTTON_COLOR;
				ctrl.JUNO_BUTTON_COLOR_PATTERN = JUNO_BUTTON_COLOR_PATTERN;
				ctrl.SecurityPermissions = SecurityPermissions;
				ctrl.ontarioEhrService = new OntarioEhrService();
				ctrl.errorHandler = new ToastErrorHandler();
				ctrl.toastService = new ToastService();
				ctrl.loadingQueue = new LoadingQueue();
				ctrl.serviceId = null as number;
				ctrl.demographicId = null as number;
				ctrl.isConnected = false;
				ctrl.patientRejectedOverride = false;

				ctrl.demographic = null as Demographic;
				ctrl.user = null;
				ctrl.searchResultAlerts = [] as OntarioEhrDHDRSearchAlert[];
				ctrl.comparisonView = false;
				ctrl.showDhdrTable = true;
				ctrl.initialized = false;

				ctrl.selectedViewMode = DHDRSummaryViewType.DrugDispense;
				ctrl.viewModeOptions = [
					{
						label: "Drug Dispense",
						value: DHDRSummaryViewType.DrugDispense,
					} as JunoSelectOption,
					{
						label: "Pharmacy Services",
						value: DHDRSummaryViewType.PharmacyServices,
					} as JunoSelectOption,
				];

				ctrl.$onInit = async (): Promise<void> =>
				{
					try
					{
						ctrl.componentStyle = JUNO_STYLE.DEFAULT;
						ctrl.loadingQueue.pushLoadingState();
						ctrl.serviceId = $location.search().service;
						ctrl.demographicId = $stateParams.demographicNo;

						ctrl.isConnected = await ctrl.ontarioEhrService.isConnected();
						if(!ctrl.isConnected)
						{
							ctrl.handleEHRNotConnected(true);
						}
						else if(securityRolesService.hasSecurityPrivileges(SecurityPermissions.OntarioEhrRead))
						{
							const initialView = $location.search().view;
							if(initialView)
							{
								ctrl.selectedViewMode = initialView as DHDRSummaryViewType;
							}
							ctrl.comparisonView = ($location.search().comparison === "true");

							const results = await Promise.all([
								demographicService.getDemographic(ctrl.demographicId),
								providerService.getMe()
							]);

							ctrl.demographic = results[0];
							ctrl.user = results[1];
						}
					}
					catch(e)
					{
						ctrl.errorHandler.handleError(e);
					}
					finally
					{
						ctrl.loadingQueue.popLoadingState();
						ctrl.initialized = true;
					}
				}

				ctrl.handleEHRNotConnected = (notify: boolean): void =>
				{
					if(notify)
					{
						ctrl.toastService.notificationToast("Please log into EHR to continue.");
					}
					$state.go("record.summary",
						{
							demographicNo: ctrl.demographicId,
						},
						{
							location: 'replace', // replaces page instead of adding to history
						});
				}

				// on ehr logout, leave page etc.
				$scope.$on("OntarioEhr.LogoutEvent", () => ctrl.handleEHRNotConnected(false));

				ctrl.onAlertsUpdated = (alerts: OntarioEhrDHDRSearchAlert[]) =>
				{
					ctrl.searchResultAlerts = alerts;
				}

				ctrl.onChangeViewMode = (value: DHDRSummaryViewType, index: number): void =>
				{
					ctrl.selectedViewMode = value;
					$location.search("view", value);
				}

				ctrl.onChangeComparisonMode = (checked: boolean, value: any): void =>
				{
					ctrl.comparisonView = checked;
					$location.search("comparison", checked.toString());
				}

				ctrl.currentDateTime = (): string =>
				{
					return Juno.Common.Util.formatMomentDateTime(moment(), Juno.Common.Util.DisplaySettings.dateTimeFormat);
				}
			}]
	});