<page-wrapper show-header="false">
	<page-body>
		<juno-security-check show-placeholder="true" permissions="$ctrl.SecurityPermissions.OntarioEhrRead">
			<print-wrapper footer-height="25px">
				<print-header>
					<dhdr-demographic-info
							class="m-b-4"
							ng-if="$ctrl.demographic"
							demographic="$ctrl.demographic">
					</dhdr-demographic-info>
					<dhdr-disclaimer ng-if="$ctrl.initialized"
					                 demographic-id="$ctrl.demographicId"
					                 service-id="$ctrl.serviceId"
					                 user-id="$ctrl.user.providerNo">
					</dhdr-disclaimer>
				</print-header>
				<print-body>
					<div class="integration-dhdr flex-column">
						<dhdr-disclaimer ng-if="$ctrl.initialized"
						                 demographic-id="$ctrl.demographicId"
						                 service-id="$ctrl.serviceId"
						                 user-id="$ctrl.user.providerNo"
						                 class="no-print">
						</dhdr-disclaimer>
						<div ng-repeat="alert in $ctrl.searchResultAlerts">
							<juno-warning>
								<span class="body-small-bold">Warning:</span>
								<span class="body-small">{{alert.message}}</span>
							</juno-warning>
						</div>
						<juno-warning ng-if="$ctrl.patientRejectedOverride">
							<span class="body-small-bold">Access to Drug and Pharmacy Service Information has been refused by the patient.</span>
						</juno-warning>

						<div class="flex-row align-items-center justify-content-between">
							<h3 class="header-color p-0">Digital Health Drug Repository (DHDR)</h3>
							<div class="flex-row flex-gap-16 align-items-center">
								<juno-toggle
										class="no-print"
										ng-if="$ctrl.comparisonView"
										label="Show DHDR"
										ng-model="$ctrl.showDhdrTable">
								</juno-toggle>
								<juno-toggle
										class="no-print"
										label="Comparison View"
										ng-model="$ctrl.comparisonView"
										change="$ctrl.onChangeComparisonMode(checked, value)">
								</juno-toggle>
								<juno-button-group
										class="no-print"
										initial-value="$ctrl.selectedViewMode"
										options="$ctrl.viewModeOptions"
										change="$ctrl.onChangeViewMode(value, index)">
								</juno-button-group>
							</div>
						</div>
						<dhdr-demographic-info
								class="m-b-24 no-print"
								ng-if="$ctrl.demographic"
								demographic="$ctrl.demographic">
						</dhdr-demographic-info>
						<div ng-if="$ctrl.isConnected" ng-show="$ctrl.showDhdrTable || !$ctrl.comparisonView">
							<dhdr-table-display
									demographic="$ctrl.demographic"
									summary-view-type="$ctrl.selectedViewMode"
									service-id="$ctrl.serviceId"
									on-alerts-change="$ctrl.onAlertsUpdated(alerts)"
									patient-rejected-override="$ctrl.patientRejectedOverride">
							</dhdr-table-display>
						</div>
						<div ng-if="$ctrl.comparisonView" class="flex-column page-break-before">
							<h3 class="header-color p-0">Prescribed</h3>
							<dhdr-medication-display>
							</dhdr-medication-display>
						</div>
					</div>
				</print-body>
				<print-footer>
					<div class="flex-row justify-content-between">
						<span>CONFIDENTIAL - report contains Personal Health Information</span>
						<span>Created by {{$ctrl.user.name}} at {{$ctrl.currentDateTime()}}</span>
					</div>
				</print-footer>
			</print-wrapper>
		</juno-security-check>
	</page-body>
</page-wrapper>