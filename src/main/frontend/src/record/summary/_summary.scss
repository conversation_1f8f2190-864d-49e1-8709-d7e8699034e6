.summary-section {
	@include juno-page-padding-default;

	.summary-grid {
		grid-template-columns: minmax(256px, auto) minmax(256px, auto) minmax(256px, auto);
		gap: 24px;
	}

	#summary-section-left{
		margin-bottom: $margin-md;
	}
	#summary-section-right{
		margin-bottom: $margin-lg;
	}
	.module-list {
		border-bottom: 1px solid;
		border-bottom-color: $color-neutral-lighter;
	}

	.summary-tabs {
		position: relative;
		@include juno-tab-md($page-tabs-height-alt);
		@include juno-tab-color-scheme-white(4px);
	}
	#note-list-filters {
		margin-bottom: $margin-sm;
	}
	#FlowSheetCustomActionForm {
		margin-bottom: 20px;
	}

	.print-button-wrapper {
		position: absolute;
		top: 0;
		right: 0;
	}
}

.notesModal {
	.modal-content {
		background-color: $color-primary-base;
	}
	.modal-body {
		background-color: $color-neutral-white;
	}
	.modal-header {
		color: $color-neutral-white;
	}
	.modal-footer {
		background-color: $color-neutral-lighter;
		padding-top: 20px;
		padding-bottom: 20px;

		.annotation-button-wrapper {
			width: 32px;
			min-width: 32px;
			height: 32px;
			i {
				font-size: 24px;
			}
		}
	}
}

@media (max-width: $screen-sm) {
	.summary-section {
		.note-list {
			margin-top: $margin-md;
		}
	}
}