/**
 * Copyright (c) 2012-2018. CloudPractice Inc. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for
 * CloudPractice Inc.
 * Victoria, British Columbia
 * Canada
 */

import {JUNO_STYLE, LABEL_POSITION} from "../junoComponentConstants";
import DeviceInfo from "../../../lib/util/DeviceInfo";

angular.module('Common.Components').component('junoTypeahead',
{
	templateUrl: 'src/common/components/junoTypeahead/junoTypeahead.jsp',
	bindings: {
		title: '@?',
		labelPosition: "<?",
		name: '@',
		model: '=',
		options: "<",
		placeholder: "@?",
		icon: "@?",
		onEnterKey: '&?',
		onChange: "&?",
		onSelected: "&?",
		disabled: '<?',
		initialText: "<?", // initial text to prefill the typeahead with
		typeaheadMinLength: "@?",
		componentStyle: "<?",
		// output the output of the ubi-typeahead directly, without translation
		rawOutput: "<?",
		// if true options are filtered on $viewValue. Set to false if you plan to self filter
		filterOptions: "<?",
		// Use with filterOptions: false. This callback is called when the option list needs to be fetched.
		// Most of the time you will use this to fetch a paged list of options. This callback is provided
		// the current search parameter as an argument.
		getOptionsCallback: "&?"
	},
	controller: ['$scope', "filterFilter", function ($scope, filterFilter)
	{
		let ctrl = this;
		$scope.LABEL_POSITION = LABEL_POSITION;

		let lastModel = null;

		ctrl.$onInit = function()
		{
			ctrl.selectedValue = ctrl.initialText || null;
			ctrl.typeaheadMinLength = ctrl.typeaheadMinLength ? ctrl.typeaheadMinLength : 1;
			ctrl.rawOutput = ctrl.rawOutput || false;
			if (ctrl.filterOptions === undefined)
			{
				ctrl.filterOptions = true;
			}
			ctrl.labelPosition = ctrl.labelPosition || LABEL_POSITION.TOP;
			ctrl.componentStyle = ctrl.componentStyle || JUNO_STYLE.DEFAULT;

			ctrl.deviceInfo = new DeviceInfo();
		};

		ctrl.$doCheck = () =>
		{
			if (ctrl.model && ctrl.options && ctrl.options.length >= 1 && !ctrl.rawOutput)
			{
				if (lastModel !== ctrl.model)
				{
					lastModel = ctrl.model;
					ctrl.selectedValue = Juno.Common.Util.typeaheadValueLookup(ctrl.model, ctrl.options);
				}
			}
			else if (ctrl.model && ctrl.options && ctrl.options.length >= 1)
			{
				if (lastModel !== ctrl.model)
				{
					lastModel = ctrl.model;
					ctrl.selectedValue = ctrl.model;
				}
			}
		};

		ctrl.getOptions = async (viewValue) =>
		{
			let options = ctrl.options;
			if (ctrl.filterOptions)
			{
				options = filterFilter(ctrl.options, viewValue);
			}
			else if (ctrl.getOptionsCallback)
			{
				options = await ctrl.getOptionsCallback({value: viewValue});
			}
			return options;
		}

		ctrl.doOnChange = () =>
		{
			// allow blank input in typeahead to register as a null selection / clear
			if(Juno.Common.Util.isBlank(ctrl.selectedValue))
			{
				ctrl.model = null;
				ctrl.emitSelectedEvent();
			}
			ctrl.emitChangeEvent();
		}

		ctrl.onSelect = () =>
		{
			if (ctrl.rawOutput)
			{
				ctrl.model = ctrl.selectedValue;
			}
			else
			{
				ctrl.model = ctrl.selectedValue.value;
			}

			ctrl.emitSelectedEvent();
			ctrl.emitChangeEvent();
		}

		ctrl.emitChangeEvent = () =>
		{
			if (ctrl.onChange instanceof Function)
			{
				if (ctrl.selectedValue.value)
				{
					ctrl.onChange({value: ctrl.selectedValue.value, option: ctrl.selectedValue});
				}
				else
				{
					ctrl.onChange({value: ctrl.selectedValue, option: ctrl.selectedValue});
				}
			}
		}
		ctrl.emitSelectedEvent = () =>
		{
			if (ctrl.onSelected instanceof Function)
			{
				ctrl.onSelected({value: ctrl.selectedValue, option: ctrl.selectedValue});
			}
		}

		ctrl.onKeyPress = function (event)
		{
			if (event.keyCode === 13)
			{// Enter key
				if (ctrl.onEnterKey)
				{
					ctrl.onEnterKey({});
				}
			}
		}

		ctrl.labelClasses = () =>
		{
			return [ctrl.labelPosition];
		};

		ctrl.componentClasses = () =>
		{
			return [ctrl.componentStyle];
		}

		ctrl.inputClasses = () =>
		{
			return {
				"shift-right-for-icon": !!ctrl.icon,
			}
		}

	}]
});