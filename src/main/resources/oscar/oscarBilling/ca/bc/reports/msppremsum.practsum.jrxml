<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.3.0.final using JasperReports Library version 4.1.1  -->
<!-- 2016-09-09T21:20:01 -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="practSum" pageWidth="535" pageHeight="842" columnWidth="535" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="oscar.oscarBilling.ca.bc.MSP.*"/>
	<parameter name="payeeNo" class="java.lang.String" isForPrompting="false"/>
	<parameter name="s21id" class="java.lang.String" isForPrompting="false"/>
	<parameter name="subS23" class="net.sf.jasperreports.engine.JasperReport" isForPrompting="false"/>
	<parameter name="adj" class="net.sf.jasperreports.engine.JasperReport" isForPrompting="false"/>
	<queryString>
		<![CDATA[select t_payment,t_practitionerno,t_practitionername,t_amtbilled,t_amtpaid from teleplanS22 where t_payeeno = $P{payeeNo} and s21_id=$P{s21id}  and t_s22type<>'D' order by s22_id]]>
	</queryString>
	<field name="t_payment" class="java.lang.String"/>
	<field name="t_practitionerno" class="java.lang.String"/>
	<field name="t_practitionername" class="java.lang.String"/>
	<field name="t_amtbilled" class="java.lang.String"/>
	<field name="t_amtpaid" class="java.lang.String"/>
	<variable name="test" class="java.lang.String" resetType="None">
		<variableExpression><![CDATA["test"]]></variableExpression>
		<initialValueExpression><![CDATA["test"]]></initialValueExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="26" splitType="Stretch">
			<staticText>
				<reportElement key="staticText" mode="Transparent" x="0" y="0" width="412" height="18" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
					<font size="14"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Practioner Summary]]></text>
			</staticText>
			<line>
				<reportElement key="line" mode="Opaque" x="0" y="22" width="534" height="1" forecolor="#000000" backcolor="#FFFFFF"/>
				<graphicElement fill="Solid">
					<pen lineWidth="2.0" lineStyle="Solid"/>
				</graphicElement>
			</line>
		</band>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="76" splitType="Stretch">
			<rectangle radius="0">
				<reportElement key="element-22" mode="Opaque" x="1" y="0" width="534" height="17" forecolor="#000000" backcolor="#999999"/>
				<graphicElement fill="Solid">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<line direction="BottomUp">
				<reportElement key="element-89" mode="Opaque" x="0" y="18" width="535" height="1" forecolor="#000000" backcolor="#FFFFFF"/>
				<graphicElement fill="Solid">
					<pen lineWidth="0.5" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement key="element-90" mode="Transparent" x="0" y="2" width="82" height="12" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Date]]></text>
			</staticText>
			<staticText>
				<reportElement key="element-90" mode="Transparent" x="86" y="2" width="107" height="12" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Practitioner]]></text>
			</staticText>
			<staticText>
				<reportElement key="element-90" mode="Transparent" x="250" y="2" width="107" height="12" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Billed]]></text>
			</staticText>
			<staticText>
				<reportElement key="element-90" mode="Transparent" x="357" y="2" width="107" height="12" forecolor="#FFFFFF" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Paid]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="dd-MMM-yy" isBlankWhenNull="true">
				<reportElement key="textField" mode="Transparent" x="0" y="20" width="82" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new SimpleDateFormat("yyyyMMdd").parse($F{t_payment})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" mode="Transparent" x="86" y="20" width="164" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{t_practitionername} + "(" + $F{t_practitionerno} + ")"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" mode="Transparent" x="250" y="20" width="107" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["$"+MSPReconcile.convCurValue($F{t_amtbilled})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" mode="Transparent" x="357" y="20" width="107" height="15" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["$"+MSPReconcile.convCurValue($F{t_amtpaid})]]></textFieldExpression>
			</textField>
			<subreport isUsingCache="true">
				<reportElement key="subreport-1" mode="Opaque" x="0" y="38" width="367" height="36" forecolor="#000000" backcolor="#FFFFFF"/>
				<subreportParameter name="s21id">
					<subreportParameterExpression><![CDATA[$P{s21id}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="providerNo">
					<subreportParameterExpression><![CDATA[$F{t_practitionerno}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{adj}]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
