<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.3.0.final using JasperReports Library version 4.1.1  -->
<!-- 2016-09-09T21:29:14 -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="pdf_rep_wo" pageWidth="535" pageHeight="842" columnWidth="535" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="startDate" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
	</parameter>
	<parameter name="endDate" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
	</parameter>
	<parameter name="demNoCnt" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["0"]]></defaultValueExpression>
	</parameter>
	<parameter name="billCnt" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["0"]]></defaultValueExpression>
	</parameter>
	<parameter name="billMasterCnt" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["0"]]></defaultValueExpression>
	</parameter>
	<parameter name="insurers" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
	</parameter>
	<parameter name="account" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
	</parameter>
	<parameter name="provider" class="java.lang.String" isForPrompting="false"/>
	<parameter name="payee" class="java.lang.String" isForPrompting="false"/>
	<field name="providerFirstName" class="java.lang.String"/>
	<field name="providerLastName" class="java.lang.String"/>
	<field name="billingtype" class="java.lang.String"/>
	<field name="serviceDate" class="java.lang.String"/>
	<field name="billMasterNo" class="java.lang.String"/>
	<field name="billing_no" class="java.lang.String"/>
	<field name="demoName" class="java.lang.String"/>
	<field name="demoNo" class="java.lang.String"/>
	<field name="billingUnit" class="java.lang.String"/>
	<field name="code" class="java.lang.String"/>
	<field name="amount" class="java.lang.String"/>
	<field name="reason" class="java.lang.String"/>
	<field name="dx1" class="java.lang.String"/>
	<field name="acctInit" class="java.lang.String"/>
	<variable name="invTotal" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[new Double($F{amount})]]></variableExpression>
	</variable>
	<variable name="invCount" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[new Integer($F{billMasterNo})]]></variableExpression>
	</variable>
	<variable name="lineCount" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[new Integer($F{billMasterNo})]]></variableExpression>
	</variable>
	<variable name="demoCount" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[new Integer($F{demoNo})]]></variableExpression>
	</variable>
	<variable name="oneGtotal" class="java.lang.Double" calculation="Sum"/>
	<variable name="cntMSP" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{billingtype}.equalsIgnoreCase("msp")?new Double($F{amount}):new Double(0)]]></variableExpression>
	</variable>
	<variable name="cntWCB" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{billingtype}.equalsIgnoreCase("wcb")?new Double($F{amount}):new Double(0)]]></variableExpression>
	</variable>
	<variable name="cntICBC" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{billingtype}.equalsIgnoreCase("ICBC")?new Double($F{amount}):new Double(0)]]></variableExpression>
	</variable>
	<variable name="cntPriv" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{billingtype}.equalsIgnoreCase("private")?new Double($F{amount}):new Double(0)]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="12" splitType="Stretch">
			<staticText>
				<reportElement key="element-90" mode="Transparent" x="56" y="0" width="19" height="10" forecolor="#333333" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Type]]></text>
			</staticText>
			<staticText>
				<reportElement key="element-90" mode="Transparent" x="267" y="0" width="59" height="10" forecolor="#333333" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[ServDate]]></text>
			</staticText>
			<staticText>
				<reportElement key="element-90" mode="Transparent" x="0" y="0" width="56" height="10" forecolor="#333333" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Line#]]></text>
			</staticText>
			<staticText>
				<reportElement key="element-90" mode="Transparent" x="141" y="0" width="100" height="10" forecolor="#333333" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Patient]]></text>
			</staticText>
			<staticText>
				<reportElement key="element-90" mode="Transparent" x="241" y="0" width="26" height="10" forecolor="#333333" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Amt]]></text>
			</staticText>
			<staticText>
				<reportElement key="element-92" mode="Transparent" x="374" y="0" width="67" height="10" forecolor="#333333" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Diagnostic Code]]></text>
			</staticText>
			<staticText>
				<reportElement key="element-93" mode="Transparent" x="75" y="0" width="33" height="10" forecolor="#333333" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Insurer]]></text>
			</staticText>
			<staticText>
				<reportElement key="element-96" mode="Transparent" x="108" y="0" width="33" height="10" forecolor="#333333" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Account
]]></text>
			</staticText>
			<staticText>
				<reportElement key="element-97" mode="Transparent" x="326" y="0" width="48" height="10" forecolor="#333333" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Item]]></text>
			</staticText>
		</band>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="11" splitType="Stretch">
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" mode="Transparent" x="56" y="0" width="19" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["WO"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" mode="Transparent" x="326" y="0" width="48" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{code}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" mode="Transparent" x="0" y="0" width="56" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{billMasterNo}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" mode="Transparent" x="141" y="0" width="100" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{demoName} + "(" + $F{demoNo}+ ")"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" mode="Transparent" x="241" y="0" width="26" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[NumberFormat.getCurrencyInstance().format(new Double($F{amount}))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-7" mode="Transparent" x="374" y="0" width="67" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dx1}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-25" mode="Transparent" x="75" y="0" width="33" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{billingtype}]]></textFieldExpression>
			</textField>
			<textField pattern="dd-MMM-yy" isBlankWhenNull="false">
				<reportElement key="textField-32" mode="Transparent" x="267" y="0" width="59" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new SimpleDateFormat("yyyyMMdd").parse($F{serviceDate})]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-33" mode="Opaque" x="108" y="0" width="33" height="10" forecolor="#000000" backcolor="#FFFFFF"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{acctInit}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
