#*
#* Copyright (c) 2001-2002. Department of Family Medicine, McMaster University. All Rights Reserved. *
#* This software is published under the GPL GNU General Public License.
#* This program is free software; you can redistribute it and/or
#* modify it under the terms of the GNU General Public License
#* as published by the Free Software Foundation; either version 2
#* of the License, or (at your option) any later version. *
#* This program is distributed in the hope that it will be useful,
#* but WITHOUT ANY WARRANTY; without even the implied warranty of
#* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
#* GNU General Public License for more details. * * You should have received a copy of the GNU General Public License
#* along with this program; if not, write to the Free Software
#* Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA. *
#*
#* <OSCAR TEAM>
#*
#* This software was written for the
#* Department of Family Medicine
#* McMaster University
#* Hamilton
#* Ontario, Canada
#*


# Confidentiality statement that is added to all printed materials (PDFs, page printouts, etc)
# please note that the statement with the latest version is selected for display, for example, out of statements
# v1, v2, v3, v3 will be displayed 
confidentiality_statement.v1=This document may contain personal health information.  Please consider confidentiality when distributing or disposing of this document.
confidentiality_statement.v2=Personal Health Information: CONFIDENTIAL

buildDateTime=${build.dateTime}
buildtag=${build.JOB_NAME}-${build.BUILD_NUMBER}

## Mysql connection

# mysql database name
# zeroDateTimeBehavior: Change the behavior of date 0000-00-00 to automatically become a valid date by rounding. The
#                       new default is to throw an exception, whereas previous versions of Connector/J converted to
#                       null. For type safety and to lower the number of exceptions thrown by OSCAR, round is the
#                       logical choice.
# useOldAliasMetadataBehavior: Due to issues with Hibernate column mapping, queries which rename columns will cause
#                              Hibernate to throw an exception. Previous version of Connector/J used a different
#                              method of determining column names which allowed the renaming of columns.
# jdbcCompliantTruncation: Fields which are not included in a query, and do not contain a default value in the database,
#                          will raise an exception in the JDBC specification. Previous versions of Connector/J
#                          were not JDBC compliant and did not follow the truncation specifications.
# IMPORTANT: The fields listed after the ? are required! Otherwise, OSCAR will not behave properly as it depends on
#            legacy functionality. Ensure that you leave the tags behind the field when renaming the database.
db_name = oscar_mcmaster?zeroDateTimeBehavior=round&useOldAliasMetadataBehavior=true&jdbcCompliantTruncation=false
db_name_readonly = oscar_mcmaster?zeroDateTimeBehavior=round&useOldAliasMetadataBehavior=true&jdbcCompliantTruncation=false

db_params = &useUnicode=yes&characterEncoding=UTF-8
db_params_readonly = &useUnicode=yes&characterEncoding=UTF-8

# username
db_username = root
db_username_readonly = readonly_user

# password for the username above
db_password = liyi
db_password_readonly = liyi

# db properties
db_type = mysql
db_driver = com.mysql.jdbc.Driver
db_uri = jdbc:mysql:///
db_uri_readonly = jdbc:mysql:///

# pool configuration
db_max_active = 32
db_max_idle = 8
db_max_wait = 20000

db_max_active_readonly = 32
db_max_idle_readonly = 8
db_max_wait_readonly = 20000

db_log_abandoned = true
db_remove_abandoned = true
db_remove_abandoned_timeout = 15


#Validation query string for mySql
db_validationQuery = select 1

#hibernate.dialect=org.hibernate.dialect.MySQLDialect
hibernate.dialect=org.oscarehr.util.persistence.OscarMySQL5Dialect

# true : enable retrieving statistics
# false : disable retrieving statistics
hibernate.generate_statistics=false

# true : enable showing sql statement
# false : disable showing sql statement
hibernate.show_sql=false

## Postgres Connections
#db_type = postgresql
#db_driver = org.postgresql.Driver
#db_uri = ***************************/

## Oracle connections
#db_name =
#db_type = oracle
#db_driver = oracle.jdbc.OracleDriver
#db_uri = ***************************************
#hibernate.dialect=org.hibernate.dialect.Oracle9iDialect

## DrugRef server
# drugref_url = http://yourDrugRefServerIP:portNumber
drugref_url = http://************:8001

# DrugRef search administration route ("Oral" is preset)
# off : turn off ;  on : turn on
drugref_route_search = off

drugref_route = topical,intravenous,disinfectant

# Disease Registry Coding System
dxResearch_coding_sys = icd9,ichppccode

#Turns on Allergy Checking
RX_ALLERGY_CHECKING=yes

#Turns on interaction checking from drugref using regional identifier
RX_INTERACTION_LOCAL_DRUGREF_REGIONAL_IDENTIFIER=yes

#Turn on RX3, "RX3=yes" to turn on RX3, "RX3=no" to turn off RX3
RX3=yes

## Rx options ##
# Turn on signature pad electronic signing for Rx3
#signature_tablet=yes
# To display the patient HIN on Rx
#showRxHin=false
# To display text at the foot of the demographic
#RX_FOOTER=
#To display fax number on Rx
RXFAX=yes
# To display text at the foot of the Rx (and other forms)
FORMS_PROMOTEXT=Created by: OSCAR The open-source EMR www.oscarcanada.org

#Turns renal dosing on
RENAL_DOSING_DS=yes

#Turns on mydrugref decision support
MYDRUGREF_DS=yes

# Prevention/Immunization
PREVENTION=yes
IMMUNIZATION_IN_PREVENTION=yes


##
#This is the base OscarDocument directory
#If this property is set it replaces all of the other properties with absolute file paths to the OscarDocument directory.
#The needed directories will be created from this directory
#ie. DOCUMENT_DIR will be create as /var/lib/OscarDocument/<Context>/document
#
#BASE_DOCUMENT_DIR=/var/lib/OscarDocument/

# Billing download folder
#HOME_DIR = /var/lib/tomcat6/webapps/OscarDocument/oscar_mcmaster/billing/download/

# Documents directory
#DOCUMENT_DIR = /var/lib/OscarDocument/oscar_mcmaster/document/
DOCUMENT_DOWNLOAD_METHOD = stream
DOC_FORWARD: /dms/complete.jsp
RA_FORWORD: /billing/CA/ON/genRA.jsp
EA_FORWORD: /billing/CA/ON/billingEAreport.jsp
TA_FORWARD: /billing/CA/BC/genTA.jsp

DOCUMENT_ORIGINAL_DIR=original/
DOCUMENT_CORRUPT_DIR=corrupt/

BILLING_BASE_DIR=billing/
BILLING_REMITTANCE_DIR=remittance/
BILLING_REMITTANCE_FAILED_DIR=remittance/failed/

RESOURCE_BASE_DIR=resource/
LAB_BASE_DIR=labs/

LOG_BASE_DIR=log/
LOG_IMPORT_DIR=import/
LOG_EXPORT_DIR=export/

document.ghostscript_path=/usr/bin/gs

# Demographic export/import
# TeMPorary DIRectory for export/import  demographic files
#TMP_DIR: /var/lib/OscarDocument/oscar_mcmaster/export/

# Export information
Vendor_Product = CloudPractice Inc, Juno EMR
Support_Contact = Juno EMR (1-888-686-8560 <EMAIL>)
ontariomd_cds_diabetes_link = https://www.ontariomd.ca/cms/xml/ontariomd_cds_diabetes.xsd

#### E2E
# E2E_DIFF - on=differential export, off=full export
# E2E_DIFF_DAYS - integer number specifying number of days to look back at

#ModuleNames=E2E
#E2E_URL = http://localhost:3001/records/create
#E2E_DIFF = on
#E2E_DIFF_DAYS = 14

#### for IBD clinic
#meditech_id = yes
#mc_number = yes
#eform_in_appointment = yes

#### Spire
spire_server_user=user_name
spire_server_password=password
spire_server_hostname=hostname

spire_download_dir=/var/lib/spire/labs/

#ModuleNames=Spire

#### HL7
hl7_a04_build_dir=/var/lib/adt/
l7_a04_sent_dir=/var/lib/adt/sent/
hl7_a04_fail_dir=/var/lib/adt/failed/
	
HL7_A04_GENERATION=false
	
HL7_A04_TRANSPORT_ADDR=127.0.0.1
HL7_A04_TRANSPORT_PORT=3987
	
# Uncomment to enable sending A04 data to Emerald
#ModuleNames=EmeraldA04


#### Billing

## The new billing system
# true : turn on the new billing system
# all other value will turn off the new billing system
isNewONbilling=true

## Invoice Reports
## yes: turn on Invoice Reports for the new billing system on admin page
## other values turn off Invoice Reports for the new billing system on admin page
TESTING=yes

## Enable 'check all' for updating billing price
SOB_CHECKALL=yes

# instance_type: determines what features are shown in the interface
# Options: ON, BC, AB
instance_type=ON

# billing_type: determines what billing system is used
# Options: ON, BC, CLINICAID
billing_type=CLINICAID

admin.show_old_billing=false

# hctype: this property is used to select the default value of HC Type field
# when adding a new demographic. If this property is not specified, the billing_type 
# is used. Note that users can configure their own personal default option as well,
# by visiting the preference screen and selecting "Set Default HC Type"
#   options: ON - Ontario
#            QC - Quebec
# And so on for other provinces in Canada, or states in US
# hctype=ON

# defaultsex has two options. If this property is not specified, users can still
# configure their own personal default option, by visiting the preference screen
# and selecting "Set Default Sex"
# M: Male
# F: Female
# defaultsex=F

# bill center codes
# G stands for Hamilton
# J - Kingston
# P - London
# E - Mississauga
# F - Oshawa
# D - Ottawa
# R - Sudbury
# U - Thunder Bay
# N - Toronto
billcenter = G

default_view = GP
clinic_view = 3866
clinic_no = 3821

# yes will auto fill the admission date on the billing screen
inPatient = no

# visit_type can be: Clinic Visit, Outpatient Visit, Hospital Visit, ER, Nursing Home, and Home Visit
visit_type = Clinic Visit
visittype = O|Physician's office
visitlocation = P1|PEMBERTON
dataCenterId = 00000
#msp_error_codes = # file location of msp error codes

#Super power for billing
BILLING_SUPERUSER=999998

#Enable auto populating of payment field on billing review
BILLING_REVIEW_AUTO_PAYMENT=no

#Private billing letterhead logo image document type
invoice_head_logo_doctype=invoice letterhead

## yes : TURNS ON NEW BC BILLING
## no : TURNS OFF NEW BC BILLING
NEW_BC_TELEPLAN=yes

## yes: turn on the default BC alt billing
## no : turn off the default BC alt billing
#BC_DEFAULT_ALT_BILLING=yes

## group billing or not
# group_billing = on

## Prepare billing site Ids
#scheduleSiteID=site1|site2|site3

# User rights
SUPERUSER = oscardoc

## eform image file path
#eform_image = /usr/local/OscarDocument/oscar_mcmaster/eform/images/
# eform_databaseap_config = /usr/local/tomcat/OscarDocument/oscar_mcmaster/eform/apconfig.xml

## forms
save_as_xml = true
#form_record_path = /usr/local/tomcat/webapps/OscarDocument/oscar_mcmaster/form/records/

### send to osdsf thru XMLRPC
#osdsfRPCURL=
#pdfFORMDIR = /usr/local/tomcat/webapps/OscarDocument/oscar_mcmaster/form


## oscarComm
## When the provider has the program access role of "oscarcomm",
## the link named as "oscarComm" will appear on the CME page if the value for the key "oscarcomm" here is on
# oscarcomm = on

## Path
## adjust the following value according to your factual needs
tomcat_path = /usr/local/tomcat/
project_home = oscar_mcmaster
backup_path = /home/<USER>/
oscarMeasurement_css=/OscarDocument/oscar_mcmaster/oscarEncounter/oscarMeasurements/styles/
#oscarMeasurement_css_upload_path=/usr/local/OscarDocument/oscar_mcmaster/oscarEncounter/oscarMeasurements/styles

oscarMeasurement_css_download_method = stream

### Measurement
#MEASUREMENT_DS_DIRECTORY=
#MEASUREMENT_DS_HTML_DIRECTORY=

### Surveillance
#surveillance_directory=
#surveillance_config_file=

### Prevention
#these can either be a full path or classpath:path/to/file
#PREVENTION_ITEMS=classpath:oscar/oscarPrevention/PreventionItems.xml
#PREVENTION_FILE=
#PREVENTION_CONFIG_SETS=

### Workflow
#WORKFLOW_DS_DIRECTORY=

### ECT
## -1 : disable ECT auto save timer
## 1 : enable ECT auto save timer
#ECT_AUTOSAVE_TIMER=-1

# set ECT save feedback timer in milliseconds
#ECT_SAVE_FEEDBACK_TIMER=2500

###
#demographicExt=

# Login info
login_local_ip = 192.168
login_max_failed_times = 3
login_max_duration = 10
login_lock = true

# Password strength policy
password_min_length = 8
password_min_groups = 3
password_pin_min_length = 4
password_group_lower_chars = abcdefghijklmnopqrstuvwxyz
password_group_upper_chars = ABCDEFGHIJKLMNOPQRSTUVWXYZ
password_group_digits = **********
password_group_special = \! @\#$%^&*()_+|~-\=\\`{}[]\:";'<>?,./

# Auto refresh settings, -1 to disable
refresh.appointmentprovideradminday.jsp = 180
refresh.encounterLayout.jsp = -1
# refresh.encounterLayout.jsp = 300

#Show link to single page chart in classic appointment screen
SINGLE_PAGE_CHART=false

# Template code
schedule_templatecode = true

# Label Printing Pref
label.1no = 1
label.2no = 1
label.3no = 1
label.4no = 1
label.5no = 1
label.left = 200
label.top = 0
label.height = 145
label.gap = 0
label.fontSize = 10
# New demographic
phoneprefix = 905-

#set to true to enable
new_label_print = false

# Login screen
logintitle =
logintext =
loginlogo =

# Alternate view for receptionist - set to 'yes' to view appointment timeslots
# as the size of the template period, set to 'no' to view the timeslots as
# the size of the receptionist's preference
receptionist_alt_view = no

# Default viewall of the main appointment screen                 
# false  - when the appointment screen loads it will show only the scheduled physicians for today, similar to how it was in 9.12
# true   - show the schedules for all the providers, including blank ones
# default_schedule_viewall = true


#Show appt reason in the appt view
#SHOW_APPT_REASON=yes

#Show demographic HIN and chart numbers for appointment screen
SHOW_PATIENT_APPOINTMENT_PHN_CHART = false

#Enable abilty to change appt statuses
#ENABLE_EDIT_APPT_STATUS=yes

# Fax configuration
faxEnable = no
faxLogo =
faxIdentifier = zwf4t%8*9@s
faxURI = https://67.69.12.117:14043/OSCARFaxWebService
faxKeystore = /root/oscarFax/oscarFax.keystore

# Patient status options
inactive_statuses = 'IN','DE','IC','ID','MO','FI'

## Province names
## 2 fields per province delimited by |
## 1st field is value stored in database, 2nd is display value, uncomment for BC
#province_names = AB|AB-Alberta|BC|BC-British Columbia|MB|MB-Manitoba|NB|NB-New Brunswick|NF|NF-Newfoundland & Labrador|NT|NT-Northwest Territory|NS|NS-Nova Scotia|NU|NU-Nunavut|ON|ON-Ontario|PE|PE-Prince Edward Island|QC|QC-Quebec|SK|SK-Saskatchewan|YT|YT-Yukon|US|US resident

##Pathnet
#pathnet_url=Location of Pathnet DLL
#pathnet_username=User to access the system
#pathnet_password=User?s Password

##CDM Reminder Codes
#CDM_ALERTS=250,428,4280

##The following list indicates which codes will be used for Individual Counseling alerts
#COUNSELING_CODES=18220,18120,17220,17120,16220,16120,13220,12220,12120,00120

##Alert Polling frequency in milliseconds
#ALERT_POLL_FREQUENCY=240000

#HSFO config
#hsfo.loginSiteCode=99
#hsfo.userID=user
#hsfo.loginPassword=paswd
#hsfo.xmlVersionDate=2007-02-12

#hsfo.webServiceURL=
## not null : start quartz scheduler
#hsfo.loginSiteCode=

DX_QUICK_LIST_BILLING_REVIEW=yes
DX_QUICK_LIST_DEFAULT=Chronic Diseases

#LAB_TYPES=

### LAB
## yes : Incoming HL7 Documents will be matched on sex+DOB+HIN ignoring the name of the patient
## no :  Incoming HL7 Documents will be matched on sex+DOB+HIN+last_name+first_name
LAB_NOMATCH_NAMES=yes

## confirmAck=yes : Acknowledged inbox documents will require confirmation dialog prior to being filed
## confirmAck=no :  Acknowledged inbox documents will be filed on acknowledgement
#confirmAck=no

## TURNS ON THE NEW STYLE ECHART
USE_NEW_ECHART=yes

#TURNS ON THE NEW CASEMANAGEMENT INTERFACE IN THE ENCOUNTER.
CASEMANAGEMENT=all
#CHRONOLOGICAL ORDER OF DISPLAY FOR ENCOUNTER NOTES, UP is ascending
CMESort=UP

### Retrieve more recent saved notes
## off : retrieve all notes saved temporarily
## on : retrieve more recent temporarily saved notes in past 2 weeks
#maxTmpSave=on

#################CAISI##############################

# your host server's IP
host=127.0.0.1

## Cookie-revolver security framework
## off: turn off cookie-revolver security framework
## on: turn on cookie-revolver security framework
cr_security=off

## Load CAISI Application Context
#plugins=on
#caisi=on
#ModuleNames=Caisi,ERx,HRM,Indivo
#NEW_CME_SWITCH=on

# When caisi is enabled
# true: encounter transportation time input is mandatory. 
# false: encounter transportation time input is not mandatory.
# ENCOUNTER_TIME_MANDATORY=true

## Load all providers' clients while entering the caseload page
CASELOAD_DEFAULT_ALL_PROVIDERS=false

##caisi plugins

## program module
## off : turn off CAISI program module
## on : turn on CAISI program module
program=on

## ticklerplus control
## off: turn off CAISI tickler module
## on : turn on CAISI tickler module
#ticklerplus=on

## OCAN assessment warning message popup window
## OCAN_warning_window=on

## off : disable client name drop down list on the tickler list page
## on : all clients' names are in the drop down select list
clientdropbox=off


# The following is the amount of time redirect tracking entries are kept.
# As an example, it might not be useful to have link tracking beyond a year
# at which point you would set this to about 1000*60*60*24*365 = 31536000000
# This helps prevent build up of useless data in the database.
# If it's commmented out or set to -1 data will not be culled.
#REDIRECT_TRACKING_DATA_RETENTION_MILLIS=31536000000

## CAISI properties for agencies, starts here

## Enable the Streethealth Mental Health Report on PMM
streethealth=yes

## Notes written on CAISI CME have the password protection
## true : enable the password field
## false : disable the password field
casemgmt.note.password.enabled=true

## Client referral has the option of temporary admission
## true : enable temporary admission
## false : disable tmporary admission
pmm.refer.temporaryAdmission.enabled=true

## client search
## true : allow to search all clients in all programs
## false : only allow the provider search the clients in this provider's program domain
pmm.client.search.outside.of.domain.enabled=true


# Submit OCAN (Ontario Common Assessment of Need) forms via IAR (Integrated Assessment Record) web service
# Replace the user, password, idPrefix with your own ones.
# If you don't have idPrefix (most of you don't have), please comment out ocan.iar.idPrefix or give emptye value like this: ocan.iar.idPrefix=
#ocan.iar.user=caisihsp
#ocan.iar.password=hsp1caisi
#ocan.iar.url=https://iarvt.ccim.on.ca/iarws-2.0/services/SubmissionService
#ocan.iar.idPrefix=TWC


## OHIP health card number
## yes : collect OHIP number
## no : don't collect OHOP number
GET_OHIP_INFO=yes

## Toronto RFQ
## yes : enable Toronto RFQ, which means new features for RFQ will be added and part of caisi/oscar features will be removed.
## no : disable Toronto RFQ
TORONTO_RFQ=no

## facility
## true : filter notes and programs based on the facility
## false : don't filter notes or programs by facility
FILTER_ON_FACILITY=false

## the nested discharge reasons
## yes : use the nested discharge reasons that used in sherbourne health center
## no : not use nested discharge reasons
ALT_DISCHARGE_REASON=no

## auto generated provider no.
## yes or true : the provider no. auto generated
## no or false : the provider no. not auto generated
AUTO_GENERATE_PROVIDER_NO=no

## encrypt PIN number
## yes : encrypt PIN
## no : not encrypt PIN
IS_PIN_ENCRYPTED=no

## CAISI logo
## true : use CAISI logo on PMM
## false : don't use CAISI logo on PMM
USE_CAISI_LOGO=true

# Type of shared issues between facilities, through Integrator
COMMUNITY_ISSUE_CODETYPE=icd10

# integerator update period in milliseconds
INTEGRATOR_UPDATE_PERIOD=43200000

# integerator throttle delay in milliseconds, should be less than 100, it's a delay on each WS call so it adds up quickly. Also it means a 100 delay would set a max of 10 requests per second already which is very slow.
INTEGRATOR_THROTTLE_DELAY=100

# switch to force a full update, if "no" then a hack interm update will be preformed
INTEGRATOR_FORCE_FULL=yes

# how long for integrator to wait between socket errors
INTEGRATOR_SLEEP_ON_ERROR=300000

# Has to be enabled for Ontario MD funded sites.  This will autoselect primaryEMR to yes if roster_Status = RO
#FORCED_ROSTER_INTEGRATOR_LOCAL_STORE=yes

#Integrator update thread runs as this oscar user. use the providerNo. No entry means -1 or system,system.
#INTEGRATOR_USER=

# vmstat logging period in milliseconds
VMSTAT_LOGGING_PERIOD=900000
VMSTAT_LOGGING_ENABLED=false

#Codes that wcb requires a form for.
WCB_FORM_REQUIRED_CODES=19937,19938,19939,19940,19941,19943,19944,19167,19173,19174,19175,19134,19135

# These parameters are for kiosk check ins, the late allowance is the allowable time check ins will be allowed after an appointment time
# is how early some one can check in prior to their appointment time. As an example with late=2 and early=3, if some one checks in at
# say 3pm, it will look for appointments from 1pm to 6pm. These numbers are in hours.
AdtA09Handler.CHECK_IN_LATE_ALLOWANCE=2
AdtA09Handler.CHECK_IN_EARLY_ALLOWANCE=4


# These are the default signature lines in a echart note.
# eg [Signed on 16-Aug-2010 14:11 by doctor oscardoc]
# substitute values must be in the form ${SUBSTITUTE_NAME} right now there are three dynamic values DATE, USERSIGNATURE and ROLENAME
# If the SUBSTITUTE_NAME isn't one of those 3 it will look in the resource bundle for the current locale for a value.
# eg oscarEncounter.class.EctSaveEncounterAction.msgSigned in english is "Signed on"
# If the SUBSTITUTE_NAME isn't found in the Resource Bundle it will be blank in the signing line
ECHART_SIGN_LINE=[${oscarEncounter.class.EctSaveEncounterAction.msgSigned} ${DATE} ${oscarEncounter.class.EctSaveEncounterAction.msgSigBy} ${USERSIGNATURE}]\n
ECHART_VERSIGN_LINE=[${oscarEncounter.class.EctSaveEncounterAction.msgVerAndSig} ${DATE} ${oscarEncounter.class.EctSaveEncounterAction.msgSigBy} ${USERSIGNATURE}]\n

# This value should equal the fully qualified path to the wkhtmltopdf command line utility. It may also work if it's just the command
# and the executable is in the path of the running process.
# This is used for converting html screens to pdf's. As an example to send eforms to MyOscar it first converts the eform to pdf.
# This value should not be null.
WKHTMLTOPDF_COMMAND=wkhtmltopdf
# Space-delimited arguments to be passed ot the WKHTMLTOPDF_COMMAND when invoking the converter
WKHTMLTOPDF_ARGS=--print-media-type
#OHSUPPORT-4050 custom label size eform printing command
WKHTMLTOPDF_ARGS_LABEL=--print-media-type -T 0 -R 0 -B 0 -L 0 --page-width 90 --page-height 28

# Port and protocol (scheme) of the running OSCAR server where HTML version of eforms for PDF generation can be
# downloaded from this is also used by olis printer
#oscar_port=8080
oscar_protocol=http

# This is the error correction level QR Codes will be rendered with. Do not change this value unless
# you know what you're doing. This is the com.google.zxing.qrcode.decoder.ErrorCorrectionLevel values
QR_CODE_ERROR_CORRECTION_LEVEL=H

# This sets if the provider default for QR codes is enabled or not, true=enabled, false=disabled
QR_CODE_ENABLED_PROVIDER_DEFAULT=false

# This scales the qr code image by the set factor. The scale needs to be large enough such that the print out is readable
# by scaners and not too fragile to things like folding of the paper or minor scratches etc.
# Do not change this unless you really know what you're doing. Valid values are positive integer values. A value of 1 means no scaling will be done.
QR_CODE_IMAGE_SCALE_FACTOR=2


#URL used to search the online manual for context sensitive help. The %s is replaced with the search term.
HELP_SEARCH_URL=https://help.junoemr.com/support/home

#Help link URL
resource_base_url=https://help.junoemr.com

# For clinic with multiple satellites, so that each provider can be assigned to multiple sites and have different location when set schedule, book appt., billing etc.
## off : turn off multisites support, oscar will behave as before
## on : turn on multisites support, use Admin-Misc-Satellite-sites Admin to manage sites and assign them to the provider in provider profile
# multisites=on


# multisites function. When add a new provide, should system validate the provider id range.
# false : turn off provider id validation (default value for backward compatible)
# true : turn on provider id validation. Range will be defined by sites admin
multioffice.formalize.provider.id=false
# the id range for doctors. other staff's id ranges are defined in the site admin page.
multioffice.formalize.doctor.minimum.provider.id=1
multioffice.formalize.doctor.maximum.provider.id=100

# multisites function: so as to identify the role name of the real admin role (the super root in oscar)
multioffice.admin.role.name=admin

###### MyOSCAR ######
MY_OSCAR = no
# How frequent oscar will send/retrieve documents with indivo (in minutes) (default 5 minutes)
MY_OSCAR_EXCHANGE_INTERVAL = 5
#ModuleNames=Indivo

# url for myoscar client because there's a link in the application to it.
myOSCAR.url=https://kindredphr.com/

# MyOscarServer base url, this should be of the form http://<ip>:<host>/<context> it should exclude any final qualifiers like /ws etc
myoscar_server_base_url=https://maple.myoscar.org:8443/myoscar_server/ws

# This is the url of the oscar_myoscar_sync component, set to blank/null to disable component.
# oscar_myoscar_sync_component_url=https://127.0.0.1:8206/oscar_myoscar_data_sync/admin/edit_oscar_instance.jsf
oscar_myoscar_sync_component_url=

# This is the url of the clinic component, set to blank/null to disable component.
# oscar_myoscar_clinic_component_url=https://127.0.0.1:8206/oscar_myoscar_clinic_component_url/admin/configure_clinc.jsp
oscar_myoscar_clinic_component_url=

# This should be the name of the survey in the myoscar system that corresponds to the posi medication survey / symptom checklist survey
# this value is so the system knows which survey results to ask the myoscar server for when generating the reports.
MYOSCAR_SYMPTOM_CHECKLIST_REPORT_TEMPLATE_NAME=Symptom Checklist

# This should control the date format used the application
DATE_FORMAT=yyyy-MM-dd
TIME_FORMAT=HH:mm:ss

# this will disable the password requirements check, i.e. length, etc.
IGNORE_PASSWORD_REQUIREMENTS=false

#System values for OMD CIHI Data Export
vendorId=vendorId not set
vendorBusinessName=McMaster University
vendorCommonName=McMaster University
softwareName=Open Source Clinical Application Resource
softwareCommonName=OSCAR
version=version not set
versionDate=versionDate not set

#Email Configuration
email.host=localhost
email.port=25
email.username=
email.password=
email.protocol=smtp
email.connection_security=ssl
#email.recipient_override=<EMAIL>
#email.print_instead_of_send=true


# cxf web services, this is the address you tell other people, so in most cases other than testing the ip must be a proper address, not localhost. This should not include the final /ws portion 
ws_endpoint_url_base=https://127.0.0.1:8081/oscar

# If it's behind a proxy, use x-forwarded-for
web_service_allowed_ips.has_proxy=false
# Prefix of local IP to verify that the proxy is on the local network (default should work for openshift)
web_service_allowed_ips.local_ip_prefix=10.

#limit one appointment per demographic per day per group (schedule group).
#Prevents rejected claims for OHIP Fee for Service where multiple appts of the same
#type are booked on the same day for the same demographic causes the claim to be rejected
allowMultipleSameDayGroupAppt=yes

#Indivica contributed diabetes flowsheet with sparklines
new_flowsheet_enabled=false

# RMA Billing
rma_enabled=false

#Patient Workflow enhancments
workflow_enhance=false
workflow_enhance.quick_appt_booking=false

indivica_hc_read_enabled=false

#Indivica contributed intake form.
appt_intake_form=off

# appt status value for Here or Arrived
#appt_status_here=H

#size of quick chart in eChart. default is 20 if not specified
#quick_chart_size=20


#Turn this to enable the ability to store a local copy of what is returned from the integrator
#INTEGRATOR_LOCAL_STORE=yes

#CAISI should not search for duplicates outside your program domain when true.
caisi.new_client.strict_search=false

#use the program manager pt search.
caisi.search.workflow=true

#Turn on display of whether demographic form is complete for the demographic
#shown on the add appointment and edit appointment windows.
#Provide semi-colon seperated list of forms to display
#(just as they appear in encounterForm table's form_table column)
appt_formTbl=

#Indivica Rx Enhancmenets
rx_enhance=false

# True if signatures should be enabled in the rx module and false otherwise.
rx_signature_enabled=true

printPDF_referring_prac=yes
oscar_port=443

# True if consultation printing should generate a pdf with attachments and false otherwise.
consultation_pdf_enabled=true

#number of days until tickler text turns red
tickler_warn_period=1

# True if signature should be enabled in eforms and false otherwise.
eform_signature_enabled=false

# EForm Generator flags to include indivica fax, sign and print components.
eform_generator_indivica_signature_enabled=false
eform_generator_indivica_print_enabled=false
eform_generator_indivica_fax_enabled=false

# True if Indivica Rich Text Letter is enabled and false otherwise.
indivica_rich_text_letter_enabled=false

#Seconds until a integrated facilty is considered Stale ( has not pushed to the integrator)  
#Seconds until a integrated facilty is considered Stale ( has not pushed to the integrator)
#This will cause a warning in the echart to alert user that this might not be all the data.
#-1 will disable it.  Disabled by default
seconds_till_considered_stale=-1

# wait list email email notifications : true | false
enable_wait_list_email_notifications=false
# period in ms, 86400000 should be about 1 day
wait_list_email_notification_period=86400000
# a csv list of programId's to do email notifications on
wait_list_email_notification_program_ids=

#True: Enable ability to add additional messages to the same tickler and update tickler status
tickler_edit_enabled=false
tickler_email_enabled=false
#The tickler email from address
#tickler_email_from_address=<EMAIL>

# Default ticklers to the assigned provider
# True: Set the default report for Ticklers main interface to be set to the current provider
# False: Ticklers main interface always default to report ALL providers
tickler_default_assigned_provider=false

#The tickler email subject
#tickler_email_subject=Tickler Reminder Test

# True if using indivica's consultation attachment and false otherwise.
consultation_indivica_attachment_enabled=false

# consultation requests can have the include Dr with name checked by default.
consultation_letterhead_title.include_dr_by_default=false

#OHSUPPORT-2929 - disable the 'please reply to' header message in consultation print/fax pages.
# -- Only for consultation_fax_enabled=true faxing/printing
consultation_fax_disable_header_message=false

# MOH File Management
moh_file_management_enabled=false
ONEDT_INBOX=/usr/local/tomcat/webapps/OscarDocument/oscar_mcmaster/onEDTDocs/inbox/
ONEDT_OUTBOX=/usr/local/tomcat/webapps/OscarDocument/oscar_mcmaster/onEDTDocs/outbox/
ONEDT_SENT=/usr/local/tomcat/webapps/OscarDocument/oscar_mcmaster/onEDTDocs/sent/
ONEDT_ARCHIVE=/usr/local/tomcat/webapps/OscarDocument/oscar_mcmaster/onEDTDocs/archive/

#Antenal Enhanced form - lab req to use .. either 07 or 10
onare_labreqver=07

born_env=T
born_sftp_host=ftp.test.com
born_sftp_port=22
born_sftp_username=test
born_sftp_password=test
born_sftp_remote_dir=/

born18m_env=T
born18m_orgcode=mcmaster
born18m_sftp_host=ftp.test.com
born18m_sftp_port=22
born18m_sftp_username=test
born18m_sftp_password=test
born18m_sftp_remote_dir=/

born18m_eform_rourke=Rourke Baby Record
born18m_eform_ndds=Nipissing District Developmental Screen
born18m_eform_report18m=Summary Report: 18-month Well Baby Visit

born18m_upload_hour=01

#ModuleNames=BORN,BORN18M

#this is to override for req provider in the labreq 07/10 forms.
#written for Maternity Centre where all labs need to come out as the NP w/ billing no
#lab_req_override=true
#lab_req_provider=Dr Clinic Head
#lab_req_billing_no=654321


#Enabling this will essentially bypass the medication module to use the external prescriber.
#Do not enable if you are running an Ontario Funding Eligible instance, you shouldn't enable this, as the 
#validation of that version is based on the usage of the internal medication module.

# Enable and configure an external prescription service
util.erx.enabled=false
# software vendor, used by SOAP bridge
util.erx.vendor=McMaster University
# software name, used by SOAP bridge
util.erx.software=OSCAR
# software version, used by SOAP bridge
util.erx.version=11
# time interval to synchronise prescription from the External Prescriber, in ms (default 24hrs).
#NOTE: You need to enable the ERx applicationContext module name to enable the interval Task Scheduler. See ModuleNames property.
util.erx.synchronize_interval=86400000
# default username used by SOAP bridge
util.erx.clinic_username=username
# default password used by SOAP bridge
util.erx.clinic_password=password
# default facility ID used by SOAP bridge
util.erx.clinic_facility_id=123
# training mode disabled used by SOAP bridge
util.erx.clinic_training_mode=false
# The local of the clinic, 1 (fr-CA), 2 (en-CA), 3 (en-US), used by SOAP bridge
util.erx.clinic_locale=2
# default URL used by the SOAP Bridge to synchronize prescriptions
util.erx.webservice_url=https://www.zoommed.com/zrx/ZRxPMISBridge/PMISBridge.asmx
# default URL used by in the oscarRx link for the External Prescriber
util.erx.oscarerx_sso_url=https://www.zoommed.com/zrx/ZRxWebMobile/ProductSearch.aspx?

#Display Chart No on prescription
showRxChartNo=false

#Default info for redirect study site
redirectstudysite_default_baseURL = http://competeii.mcmaster.ca/PDSsecurity/login.asp
redirectstudysite_default_username = yilee18
redirectstudysite_default_password = 515750564848564853485353544852485248484851575150

web_service_client.connection_timeout_ms=1500
web_service_client.received_timeout_ms=3000

#Enter bill code of default bill center to be populated on Add Provider Record window
default_bill_center=

#Enter 1 of the following:
#
# search_name
# search_phone
# search_dob
# search_address
# search_hin
# search_chart_no
#
# to set the default search criteria for demographic search
default_search_mode=search_name

#Clinic has a MRP clinic model if yes
#Clinic model isn't based on MRP but Provider per appointment if no
mrp_model=yes

# Set:
#pdfLabelApptProvider=/path/to/label-appt.xml
#
# To choose between Dr. printed on PDF Label being MRP or Appointment Provider 
# when an appointment context can be applied (i.e. if appointment number is known, Appointment Provider is used)
# ...
# Can also configure OSCAR to also look for the default MRP label.xml in a specified 
# location by setting:
#pdfLabelMRP=/path/to/label.xml

#The amount of time an appointment timeslot will be reserved to a provider as viewing and/or locked, 
#if they don't choose a different timeslot, click "Cancel", or click the "X" in the upper right corner of the Add Appointment window
#and move off of the add appointment window (e.g. to search for a demographic)
#by default 0 turns off the lock/reservation feature
appointment_locking_timeout=0

#Show prevention item comments on Prevention Print PDF
prevention_show_comments=false

#Show a due date on Ontario third party invoice printouts
#invoice_due_date=0

# wait-list eform front proxy
form_intake_program_access_service_id=10016
form_intake_program_access_fid=1
form_intake_program_cash_service_id=10016
form_intake_program_cash_fid=1

#If set to true, popup javascript "Are you sure?" msg if user tries to delete a bill from Billing History
warnOnDeleteBill=false

#If chart no. required on the lab requisition form, set to true
lab_req_include_chartno=false

#If true, mark the form id on the lab req 07/10 form in the Additional Clinical Information section for lab tracking
use_lab_clientreference=false

#The identifier to add as part of the temp consult fax filenames (pdf/txt) put in the jvm tmp directory
#e.g. CRF-OSCAREMR<reqId><systemTime>.pdf
#fax_clinic_id=OSCAREMR

#Pick up latest demographic and clinic contact info to populate BillTo and RemitTo fields on 3rd Party Invoice Report.
useDemoClinicInfoOnInvoice=true

#The phone number of the billing officer, if applicable to the clinic
#clinic_billing_phone=

#If 3rd Party invoices are printed, where the PDF invoice gets stored to
INVOICE_DIR=/usr/local/tomcat/webapps/OscarDocument/oscar_mcmaster/billing/invoices

DEMOGRAPHIC_PATIENT_HEALTH_CARE_TEAM=true
DEMOGRAPHIC_PATIENT_CLINIC_STATUS=true
DEMOGRAPHIC_PATIENT_ROSTERING=true
DEMOGRAPHIC_WAITING_LIST=true

NEW_CONTACTS_UI_EXTERNAL_CONTACT=false


#ckd_notification_scheme=dsa
ckd_notification_scheme=tickler
#ckd_notification_receiver=1
#ckd_flowsheet=diabetes
ORN_PILOT=no
orn_labreqver=10
#ModuleNames=ORN

health_tracker=false


#for waitlist project..when no notes, assign a default issue to them
wl_default_issue=false

#Where to find the additional/external billing rule XML files
#decision_support_dir=
#The XML filenames (e.g. ONA003A.xml) containing the billing rules, comma seperated list
#decision_support_files=

#hide those one line invoice notes in the echart note screen
#encounter.hide_invoices=true

#Incoming faxes and incoming documents goes here. Need also to create subfolders:  Fax, Mail, File, Refile  for each queue_id
# Example: for the default queue, create the following folders:
#/usr/local/tomcat/webapps/OscarDocument/oscar_mcmaster/incomingdocs/1
#/usr/local/tomcat/webapps/OscarDocument/oscar_mcmaster/incomingdocs/1/Fax
#/usr/local/tomcat/webapps/OscarDocument/oscar_mcmaster/incomingdocs/1/Mail
#/usr/local/tomcat/webapps/OscarDocument/oscar_mcmaster/incomingdocs/1/File
#/usr/local/tomcat/webapps/OscarDocument/oscar_mcmaster/incomingdocs/1/Refile
INCOMINGDOCUMENT_DIR=/usr/local/tomcat/webapps/OscarDocument/oscar_mcmaster/incomingdocs

# true : move the deleted page or deleted pdf into the deleted folder
# false: delete the page or pdf without moving to the deleted folder
# The deleted folder is automatically created if not exist
# Example, for the default queue, the deleted folders are
#/usr/local/tomcat/webapps/OscarDocument/oscar_mcmaster/incomingdocs/1/Fax_deleted
#/usr/local/tomcat/webapps/OscarDocument/oscar_mcmaster/incomingdocs/1/Mail_deleted
#/usr/local/tomcat/webapps/OscarDocument/oscar_mcmaster/incomingdocs/1/File_deleted
#/usr/local/tomcat/webapps/OscarDocument/oscar_mcmaster/incomingdocs/1/Refile_deleted
INCOMINGDOCUMENT_RECYCLEBIN= true

#true = can split, rotate, remove pages or replace content with a new file once the document has been assigned to a patient
#false = cannot split, rotate, remove pages or replace content with a new file once the document has been assigned to a patient
ALLOW_UPDATE_DOCUMENT_CONTENT= false

# Maximum memory usage for loading PDFs, in bytes
PDF_MAX_MEM_USAGE=512000000

displayNotesOnScheduleScreen=false
displayAlertsOnScheduleScreen=false

# Enabled informed consent check
#privateConsentEnabled=true
# limit the display of the informed/private consent to only certain programs.
#privateConsentPrograms=10034,10035

# For CAISI users - on login, force users to explicitely select a program.
#useProgramLocation=false

### Clinicaid integration settings ###
# URL for the www.clinicaid.ca server you are integrated with
clinicaid_domain=https://demo.clinicaid.ca

# URL for accessing the clinicaid API
clinicaid_api_domain=https://demo.clinicaid.ca

# API key assigned to you by www.clinicaid.ca
clinicaid_api_key=2dcf43ff-7e4c-4d62-8f35-************

# Account name assigned to you by www.clinicaid.ca
clinicaid_instance_name=oscar_demo

clinicaid_dev_mode=false
### End of Clinicaid properties ###

### MyHealthAccess integration settings ###
myhealthaccess_telehealth_enabled=true
myhealthaccess_protocol=https
myhealthaccess_domain=www.myhealthaccess.ca
myhealthaccess_api_uri=/api/rest/v1/my_health_access
### End of MyHealthAccess integration settings ###

### AQS (Appointment Queue Service) integration settings ###
aqs_protocol=https
aqs_domain=aqs.cloudpractice.ca
aqs_api_uri=/api/v1

aqs_default_queue_limit=25
aqs_default_availability_day_start=08:00
aqs_default_availability_day_end=17:00
aqs_default_on_demand_expiration_threshold_seconds=120

### End of AQS integration settings ###

### iMDHealth integration settings
imdhealth_api_domain=ca.api.imdhealth.com
imdhealth_app_domain=app.imdhealth.com
imdhealth_scheme=https
### End of iMDHealth integration settings

### Icefall integration settings
icefall_api_domain = api.canopygrowthweb.com
icefall_scheme = https
### End of Icefall integration settings

#true = this will activate the mandatory password reset on first login.
#false = this will deactive the feature, users can reset password
mandatory_password_reset=true

########### CBI data submission : start ###########
#to start the scheduler for data submission uncomment this
#ModuleNames=CBI

#job repeat interval. in millieseconds
#5 mins = 300000 , 12 hours = 43200000
CBI_JOB_INTERVAL=43200000

#cbi webservice url
CBI_WS_URL=https://ws.cbiproject.ca:8443/CBIService/cbitest?wsdl

#cbi webservice username
CBI_WS_USERNAME=

#cbi webservice password
CBI_WS_PASSWORD=

CBI_WS_CLIENT_QNAME=CBIServiceImplService
CBI_WS_CLIENT_QNAME_URL=http://ws.cbi.com/

# cbi form submission failure reminder
# yes: pop up message to remind the provider to check failed submission.
# no: don't remind.
CBI_REMINDER_WINDOW=no

CBI_REMIND_ON_UPDATE_DEMOGRAPHIC=false

########### CBI data submission : end###########

# to set site selection feature true/false
#useProgramLocation=true

####### Sharing Center Properties
sharingcenter.enabled=false

## Sharing Center (IHE Keystore & Truststore locations)
TOMCAT_KEYSTORE_FILE=/home/<USER>/ihe/ihe.keystore.jks
TOMCAT_KEYSTORE_PASSWORD=changeit
TOMCAT_TRUSTSTORE_FILE=/home/<USER>/ihe/ihe.truststore.jks
TOMCAT_TRUSTSTORE_PASSWORD=changeit

# MCEDT Settings
mcedt.enabled=false
# boolean flag that represents if the SOAP call details are logged
mcedt.logging.skip=false
# username used for reading keystore content
mcedt.keystore.user=
# password used for reading keystore content
mcedt.keystore.pass=
# username placed into the user name token (i.e. service user name)
mcedt.service.user=
# password placed into the user name token (i.e. service pass)
mcedt.service.pass=
# MCEDT web service URL
mcedt.service.url=
# MCEDT service conformance key (provided when registering for MCEDT) 
mcedt.service.conformanceKey=
# MCEDT primary service ID (provided when registering for MCEDT)
mcedt.service.id=
## MCEDT Designated service ID's (a comma delimited list of 5 or 6 digit numbers, including mcedt.service.id above)
mcedt.service.designated.ids=

# enable automated mcedt mailbox(true/false) requires MOH File Management parameters
# true: Automated MCEDT mainbox
# false: manual MCEDT interface. Default
mcedt.mailbox.enabled=true
#File that stores last downloaded mcedt downloadble file id on EDT_INBOX 
mcedt.last.downloadedID.file=.LastDownloadedID
# file that stores update password

#Experimental UI for OSCAR 14
cobalt=no

#custom label
showSexualHealthLabel=false

# HCV Settings
# validation type(simple, online)
# simple - old variant, only checks not null and hin size 
hcv.type=simple
# boolean flag that represents if the SOAP call details are logged
hcv.logging.skip=true
# username used for reading keystore content
hcv.keystore.user=signatureAlias
# password used for reading keystore content
hcv.keystore.pass=aliasPassword
# username placed into the user name token (i.e. service user name)
hcv.service.user=<EMAIL>
# password placed into the user name token (i.e. service pass)
hcv.service.pass=Password0!
# HCV web service URL
hcv.service.url=https://ws.conf.ebs.health.gov.on.ca:1440/HCVService/HCValidationService
# hcv service conformance key (provided when registering for HCV) 
hcv.service.conformanceKey=844b6fcf-07e1-4b30-963d-d15b30a61bad
# HCV service ID (provided when registering for HCV)
hcv.service.id=010637

# Enable GZIP encoding and compression
INTEGRATOR_COMPRESSION_ENABLED=false
#show the SOAP messages for integrator
INTEGRATOR_LOGGING_ENABLED=false

invoice_reports.print.hide_name=false

#enable to have better demographic relationship support. acceptable values are true and false
NEW_CONTACTS_UI=true

#@deprecate after OSCAR 15
#defaulted to false. keep this way unless you need some time to migrate some process over. FR#1120
#program_domain.show_echart=false

#show hamilton public health items in admin menu
admin.hph=false

#in the caisi client manager summary page, only see the CDSs for your program domain
caisi.cds.restrict_by_program_domain=false

#when enabled, you can set drugs to internally dispensed, and access
#the dispensing screen.backed by a simple inventory/product manager
rx.enable_internal_dispensing=false

#override the odb_formulary file
#odb_formulary_file=

#hide the drug of choice button from rx3 interface
#rx.drugofchoice.hide=true

#ONTARIO_MD_INCOMINGREQUESTOR=yes

#show a menu in the main bar on schedule page to search professional specialists
referral_menu=no

#by default, when using the caisi registration intake, saving it causes the provider to become the MRP.
#set this to false to avoid that from happening.
caisi.registration_intake.updateMRPOnSave=true

#for kidconnect 0-5 program
#unique identifier provided by BORN
born_orgcode=mcmaster
#names of all the forms used in the project
born_eform_rourke=Rourke Baby Record
born_eform_sumrptmarkers=Summary Report: Well Baby Visit
born_eform_ndds1m2m=NDDS 1&2 Months
born_eform_ndds4m=NDDS 4 Months
born_eform_ndds6m=NDDS 6 Months
born_eform_ndds9m=NDDS 9 Months
born_eform_ndds12m=NDDS 12 Months
born_eform_ndds15m=NDDS 15 Months
born_eform_ndds18m=NDDS 18 Months
born_eform_ndds24m=NDDS 24 Months
born_eform_ndds30m=NDDS 30 Months
born_eform_ndds3y=NDDS 3 Years
born_eform_ndds4y=NDDS 4 Years
born_eform_ndds5y=NDDS 5 Years
born_eform_ndds6y=NDDS 6 Years

#by default, a measurement group is ordered by typeDisplayName.
#if you order by ID, you can set up data to show up in any order.
oscarMeasurements.orderGroupById=false

#eaaps was research project
eaaps.enabled=false

#default provider for BORNSchedulerJob (not HIAL)
born_scheduler_job_run_as_provider=999998

#default provider for HSFO jobs
hsfo_job_run_as_provider=999998

#default provider for MyMeds job (MyMedsStudy.java)
mymeds_job_run_as_provider=999998

lab.handler.AlphaHL7.enabled=true
lab.handler.CML.enabled=true
lab.handler.EPSILON.enabled=false
lab.handler.PATHL7.enabled=false
lab.handler.GDML.enabled=true
lab.handler.HHSEMR.enabled=false
lab.handler.HRMXML.enabled=true
lab.handler.ICL.enabled=false
lab.handler.IHA.enabled=false
lab.handler.MDS.enabled=true
lab.handler.MEDVUE.enabled=false
lab.handler.OLIS_HL7.enabled=true
lab.handler.OSCAR_TO_OSCAR_HL7_V2.enabled=false
lab.handler.PDFDOC.enabled=false
lab.handler.PFHT.enabled=false
lab.handler.SIOUX.enabled=false
lab.handler.TDIS.enabled=true
lab.handler.Spire.enabled=false
lab.handler.OUL_R21.enabled=false
lab.handler.ORU_R01.enabled=false
lab.handler.BIOTEST.enabled=false
lab.handler.CLS.enabled=false
lab.handler.CDL.enabled=false
lab.handler.TRUENORTH.enabled=false
lab.handler.OTHER.enabled=true

ENCOUNTER_TIME_MANDATORY=false
TRANSPORTATION_TIME_MANDATORY=false

#this will make patient search have a wildcard at front of it
search.searchName.addLeadingWildcard=false


## A new patient consent module in a nice versatile and compact package. 
## View instruction documentation.
USE_NEW_PATIENT_CONSENT_MODULE=true

#in the master demo
skip_postal_code_validation=true

#don't include MRP in print report by setting to false
print.includeMRP=true

#if true, it will use the provider's current program instead of clinic contact info
print.useCurrentProgramInfoInHeader=false

#Turn on resident review workflow
#resident_review=true

# auto complete referral doctors in demographic master file.
billingreferral_demographic_refdoc_autocomplete=true

# If there are eform groups and a large eform_data table, the eform list
# page becomes very slow because of the eform count.  This disables that
# count.
#disable_eform_group_count=true

# always show the E|B|M etc. links in appointment view
APPT_ALWAYS_SHOW_LINKS=true

display_date_format=yyyy-MM-dd
display_datetime_format=yyyy-MM-dd HH:mm:ss

# Automatically link assigned document to demographic's provider
assign_document.link_docs_to_provider=yes

# Enable the schedule in the new interface
schedule.enabled=yes

#OHSUPPORT-4786 Enable/Disable the alert when assigning a label to a lab
disable_lab_label_alert=yes

# OHSUPPORT-3434 -- add email link to the patient email address in the demographic master file.
enable_demographic_email_link=false

# Settings for the appointment_optimized.jsp file
optimized_schedule_enable=true
optimized_schedule_default_new=true
TOGGLE_REASON_BY_PROVIDER=yes
new_eyeform_enabled=false
APPT_SHOW_SHORT_LETTERS=false

# rest service rate limiting filter settings
rate_limit_filter.rate_limit_enabled=true
rate_limit_filter.rate_limit=50
rate_limit_filter.period_in_ms=10000

admin.show_rest_log_report=false

# custom sql reporting control settings
report_by_example.enable_restrictions=true
report_by_example.enforce_restrictions=true
report_by_example.max_rows=100000
report_by_example.max_results=1000

report_by_template.enable_restrictions=true
report_by_template.enforce_restrictions=true
report_by_template.max_rows=500000
report_by_template.max_results=1000
report_by_template.csv_max_results=50000

# Show patient emails in the consultations requests list
view_consultation_requests.show_patient_emails=false

fax.max_send_attempts=5
fax.inbound.enabled=true
fax.outbound.enabled=true

common.server.master_check.enabled=true
common.server.master_check.domain=junoemr.com
common.server.master_check.secure_subdomain=secure
common.server.master_check.primary_server_regex_pattern=^primary([\\d]+)
common.server.master_check.secondary_server_regex_pattern=^secondary([\\d]+)
common.server.master_check.dns_server_list=ns0.dnsmadeeasy.com,ns1.dnsmadeeasy.com,ns2.dnsmadeeasy.com,ns3.dnsmadeeasy.com,ns4.dnsmadeeasy.com

juno_encounter.enabled=true
juno_encounter.link_to_old_encounter_page=false

#assign a default rolename for new providers
default_provider_role_name = doctor

# Dashboard enable for BC instances (can be applied globally, doesn't show up on non-BC instances)
enable_dashboards=true

SHOW_SCHEDULE_PAYMENT_OWING=true
STRING_ENCRYPTION_KEY=eb693ec8252cd630
PRACTICE_ID=${PRACTICE_ID}