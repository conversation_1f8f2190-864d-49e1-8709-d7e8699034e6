<?xml version="1.0" encoding="UTF-8"?>
	<!--
		Copyright (c) 2001-2002. Centre for Research on Inner City Health, St.
		Michael's Hospital, Toronto. All Rights Reserved. This software is
		published under the GPL GNU General Public License. This program is
		free software; you can redistribute it and/or modify it under the
		terms of the GNU General Public License as published by the Free
		Software Foundation; either version 2 of the License, or (at your
		option) any later version. This program is distributed in the hope
		that it will be useful, but WITHOUT ANY WARRANTY; without even the
		implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR
		PURPOSE. See the GNU General Public License for more details. You
		should have received a copy of the GNU General Public License along
		with this program; if not, write to the Free Software Foundation,
		Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA. OSCAR
		TEAM This software was written for Centre for Research on Inner City
		Health, St. Michael's Hospital, Toronto, Ontario, Canada
	-->
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping>
	<class name="org.oscarehr.casemgmt.model.Issue" table="issue">
		<id name="id" column="issue_id" unsaved-value="0">
			<generator class="native" />
		</id>
		<property name="code" />
		<property name="description" />
		<property name="role" />
		<property name="update_date" type="java.util.Date" />
		<property name="priority" />
        <property name="type" />
        <property name="sortOrderId" />
	</class>
</hibernate-mapping>