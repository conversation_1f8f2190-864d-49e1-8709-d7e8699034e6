<?xml version="1.0" encoding="UTF-8"?>
<indicatorTemplateXML>
	<author>Colcamex Resources Inc. for Oscar EMR</author>
	<uid></uid>
	<heading>
		<category>Preventive Health Care</category>
		<subCategory>Immunization</subCategory>
		<name>Patient Care: Influenza Immunization</name>
		<definition>% of patient population age 65 and older who received influenza immunization within the last 12 months.</definition>
		<framework>Based on CIHI and MOHLTC</framework>
		<frameworkVersion>01-01-2012</frameworkVersion>
		<notes>Indicator does not include patients that refused the flu shot, whereas the Drilldown query does list them</notes>
	</heading>
	<indicatorQuery>
		<version>10-03-2016</version>
		<params>
			<!-- 
				Required parameter provider. Value options are: 
					[ an individual providerNo, or provider range ] ie: value="370, 1001" 
					"all" ie: value="all" including null.
					"loggedInProvider" ie:
				Default is "loggedInProvider"
				Use this parameter in the query as ${provider}
				This parameter should be used for fetching patient's assigned to a MRP.
				ie: WHERE demographic.provider_no = ${provider}
			-->
			<parameter id="provider" name="provider_no" value="loggedInProvider" />
			<parameter id="prevention" name="Immunization" value="'flu'" />	
			<parameter id="pstatus" name="PatientStatus" value="'%AC%'" />	
		</params>
		<range>
			<lowerLimit id="age" label="Min Patient Age" name="Age" value="65" />
			
			<lowerLimit id="date" label="From Date" name="Date" value="DATE_SUB( NOW(), INTERVAL 12 MONTH )" />
			<upperLimit id="date" label="Date Today" name="Date" value="NOW()" />
		</range>
		<query>
			<!-- Indicator SQL Query here -->
			SELECT 

				IF ( COUNT(fin.patient) > 0,
					ROUND( COUNT(fin.flu) / COUNT(fin.patient) * 100, 1) 
				,0 ) AS "% With Influenza Immunization",
			
				IF ( COUNT(fin.patient) > 0,
					100 - ROUND( COUNT(fin.flu) / COUNT(fin.patient) * 100, 1 )
				,0 ) AS "% No Influenza Immunization"
			
			FROM(
				SELECT 
				d.demographic_no AS patient,
				FLU.id as flu
			
				FROM demographic d
			
				-- LOOK FOR INFLUENZA VACCINES
				LEFT JOIN ( 
					SELECT p.demographic_no, p.id
					FROM preventions p	
					WHERE p.prevention_type LIKE ${prevention}
					AND p.deleted = 0
					AND p.refused = 0
					AND DATE(p.prevention_date) > ${lowerLimit.date} 
					AND p.demographic_no > 0
					GROUP BY p.demographic_no HAVING COUNT(p.demographic_no) > -1
				) FLU 
				ON (d.demographic_no = FLU.demographic_no )
			
				-- WHERE THE PATIENTS AGE AT THE TIME OF THE FLU SHOT
				WHERE d.patient_status LIKE ${pstatus} 
				AND d.provider_no LIKE ${provider}
				AND d.demographic_no > 0 
				AND ( ROUND( ABS( DATEDIFF( DATE( CONCAT(d.year_of_birth,"-",d.month_of_birth,"-",d.date_of_birth) ), 
					${lowerLimit.date} ) ) / 365.25 ) >= ${lowerLimit.age} )
			
			)fin
		</query>
	</indicatorQuery>
	<drillDownQuery>
		<version>10-03-2016</version>
		<params>
			<parameter id="provider" name="provider_no" value="loggedInProvider" />
			<parameter id="prevention" name="Immunization" value="'flu'" />
			<parameter id="pstatus" name="PatientStatus" value="'%AC%'" />
		</params>
		<range>
			<lowerLimit id="age" label="Min Patient Age" name="Age" value="65" />
			
			<lowerLimit id="date" label="From Date" name="Date" value="DATE_SUB( NOW(), INTERVAL 12 MONTH )" />
			<upperLimit id="date" label="Date Today" name="Date" value="NOW()" />
		</range>
		<displayColumns>
			<column id="demographic" name="d.demographic_no" title="Patient Id" primary="true" />
			<column id="name" name="CONCAT( d.last_name, ', ', d.first_name )" title="Patient Name" primary="false" />
			<column id="dob" name="DATE_FORMAT( CONCAT( d.month_of_birth, '-', d.date_of_birth, '-', d.year_of_birth ), '%m-%d-%Y' )" title="Date of Birth (mm-dd-yy)" primary="false" />
			<column id="age" name="ROUND( ABS( DATEDIFF( DATE( CONCAT(d.year_of_birth,'-',d.month_of_birth,'-',d.date_of_birth) ), NOW() ) ) / 365.25 )" title="Age" primary="false" />
			<column id="preventionDate" name="IFNULL( DATE_FORMAT( DATE(FLU.prevention_date), '%m-%d-%Y' ) , '0')" title="Immunization Date" primary="false" />
			<column id="refused" name="IF(FLU.refused > 0, 'yes', 'no')" title="Immunization Refused" primary="false" />
		</displayColumns>
		<exportColumns>
			<column id="demographic" name="d.demographic_no" title="Patient Id" primary="true" />
			<column id="firstName" name="d.first_name" title="First Name" primary="false" />
			<column id="lastName" name="d.last_name" title="Last Name" primary="false" />
			<column id="dob" name="DATE_FORMAT( CONCAT( d.month_of_birth, '-', d.date_of_birth, '-', d.year_of_birth ), '%m-%d-%Y' )" title="Date of Birth (mm-dd-yy)" primary="false" />
			<column id="age" name="ROUND( ABS( DATEDIFF( DATE( CONCAT(d.year_of_birth,'-',d.month_of_birth,'-',d.date_of_birth) ), NOW() ) ) / 365.25 )" title="Age" primary="false" />
			<column id="preventionDate" name="IFNULL( DATE_FORMAT( DATE(FLU.prevention_date), '%m-%d-%Y' ) , '0')" title="Immunization Date" primary="false" />
			<column id="refused" name="IF(FLU.refused > 0, 'yes', 'no')" title="Immunization Refused" primary="false" />
		</exportColumns>
		<query>
			<!-- Drilldown SQL Query here -->
			SELECT 
			*
			FROM demographic d
		
			-- LOOK FOR INFLUENZA VACCINES
			LEFT JOIN ( 
				SELECT p.demographic_no, p.id, MAX(p.prevention_date) AS prevention_date, p.refused
				FROM preventions p	
				WHERE p.prevention_type LIKE ${prevention}
				AND p.deleted = 0
				AND DATE(p.prevention_date) > ${lowerLimit.date} 
				AND p.demographic_no > 0
				GROUP BY p.demographic_no HAVING COUNT(p.demographic_no) > -1
			) FLU 
			ON (d.demographic_no = FLU.demographic_no )
		
			-- WHERE THE PATIENTS AGE AT THE TIME OF THE FLU SHOT
			WHERE d.patient_status LIKE ${pstatus}
			AND d.provider_no LIKE ${provider}
			AND d.demographic_no > 0 
			AND ( ROUND( ABS( DATEDIFF( DATE( CONCAT(d.year_of_birth,"-",d.month_of_birth,"-",d.date_of_birth) ), 
				${lowerLimit.date} ) ) / 365.25 ) >= ${lowerLimit.age} )
		</query>
	</drillDownQuery>
</indicatorTemplateXML>