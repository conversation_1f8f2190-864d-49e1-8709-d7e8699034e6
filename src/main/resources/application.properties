#
# Copyright (c) 2012-2018. CloudPractice Inc. All Rights Reserved.
# This software is published under the GPL GNU General Public License.
# This program is free software; you can redistribute it and/or
# modify it under the terms of the GNU General Public License
# as published by the Free Software Foundation; either version 2
# of the License, or (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
#
# This software was written for
# CloudPractice Inc.
# Victoria, British Columbia
# Canada
#

# Log level should be set here.  These log levels affect logback.  log4j messages are being routed
# through logback as well.
#logging.level.org.oscarehr=DEBUG
#logging.level.org.hibernate=DEBUG
#logging.level.root=DEBUG

# Use cookies instead of URL session tracking
server.servlet.session.tracking-modes=COOKIE

# Group write statements when compiling JSP files to keep file size down slightly
# Some Juno JSPs were hitting the size limit.  This is not a great solution, but it works for now.
server.servlet.jsp.init-parameters.mappedfile=false

# Enable JSP recompiling
server.servlet.jsp.init-parameters.development=true
server.servlet.jsp.init-parameters.modificationTestInterval=0

# handle x-forward-for proxy settings via tomcat
server.forward-headers-strategy=native
# do not leave blank, or all proxies will be accepted
server.tomcat.remoteip.internal-proxies=${TRUSTED_PROXIES:192\\.168\\.\\d{1,3}\\.\\d{1,3}}

# Don't restart spring boot on recompile because Juno is Slow to start
spring.devtools.restart.enabled=false

# Force UTF-8 encoding, even if encoding information is not provided
spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
spring.http.encoding.force=true

# Turn off Hibernate 5 enhanced ID generators
spring.jpa.hibernate.use-new-id-generator-mappings=false

# Allow overriding a bean with the same name
spring.main.allow-bean-definition-overriding=true

# Base name of the resource bundle
spring.messages.basename=oscarResources

# Configure the Spring MVC views to use JSP templates
spring.mvc.view.prefix=/WEB-INF/jsp/
spring.mvc.view.suffix=.jsp

juno.redis-session-store.enabled=false

# App info settings
info.app.encoding=@project.build.sourceEncoding@
info.app.java.source=@java.version@
info.app.java.target=@java.version@

# Spring boot actuator settings
management.endpoint.health.group.system.include=systemReadinessProbe
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.group.readiness.include=readinessState,systemReadinessProbe
management.endpoint.health.probes.enabled=true
management.endpoint.health.show-details=always
management.health.livenessstate.enabled=true
management.health.mail.enabled=false
management.health.readinessstate.enabled=true
management.info.git.mode=full
juno.administrator-config.login-name=oscar_host
juno.administrator-config.session-cookie-enabled=${LOGIN_SESSION_COOKIE_ENABLED:true}
juno.administrator-config.session-cookie-name=${LOGIN_SESSION_COOKIE_NAME:juno-login-token}
juno.administrator-config.session-cookie-file=/var/secrets/waf/login_cookie_values.json

spring.jpa.properties[hibernate.generate_statistics]=true

# ------------------- OLIS settings -------------------
juno.olis.keystore=/var/secrets/olis/olis_prod.jks
juno.olis.ssl-keystore=/var/secrets/olis/olis_prod.jks
juno.olis.ssl-keystore-password=${OLIS.KEYSTORE.PASSWORD}
juno.olis.ssl-keystore-alias=1
juno.olis.truststore=/var/secrets/olis/jssecacerts.olis.ehealthontario.ca
juno.olis.truststore-password=${OLIS.TRUSTSTORE.PASSWORD}
juno.olis.returned-cert=/var/secrets/olis/82508423.cer.txt
juno.olis.vendor-id=2.16.840.1.113883.3.239.14
juno.olis.request-url=https://olis.ehealthontario.ca/ssha.olis.webservices.ER7/OLIS.asmx
juno.olis.response-schema=src/main/resources/org/oscarehr/olis/response.xsd

# olis recommends 30 minute polling interval
juno.olis.default-polling-interval-min=30
# how many months are fetched at a time, max 12 or api rejects requests
juno.olis.max-fetch-months=12

# feature to remove unsaved olis results from search. enable for conformance
juno.olis.enable-search-result-removal=true
#  === legacy properties ===
juno.olis.simulate=false
#T=testing, P=production, deprecated by olis
juno.olis.processing-id=P
# ----------------- End OLIS settings -----------------

# ----------------- Fax Integration -----------------
juno.fax-config.datastore-location=/opt/juno/datastore/
juno.fax-config.ringcentral-api-location=https://platform.ringcentral.com
juno.fax-config.ringcentral-redirect-url=https://redirect.junoemr.com
juno.fax-config.ringcentral-client-secret=${FAX.RINGCENTRAL.CLIENT_SECRET:}
juno.fax-config.ringcentral-client-id=${FAX.RINGCENTRAL.CLIENT_ID:}
# ----------------------------------------------------

# ----------------------- HRM ------------------------
juno.hrm.default-polling-interval-seconds=3600
juno.hrm.min-polling-interval-seconds=1200
juno.hrm.sftp-timeout-seconds=20
juno.hrm.access-key-location=/var/secrets/hrm/juno_hrm_prod.ppk
juno.hrm.base-directory=hrm/
juno.hrm.decrypt-remote-files=true

# Turn on to download from a local directory when using the "Fetch Now button"
juno.hrm.local-override-enabled=false
# Absolute Path to location of local HRM files to process
juno.hrm.local-override-directory=""
# Decrypt the files passed in to the local override
juno.hrm.decrypt-local-files=true
# -----------------------------------------------------

# ----------------------- Netcare ---------------------
juno.netcare-config.conformance-code=CLDPR0822
juno.netcare-config.launcher-url=https://plb.albertanetcare.ca/Citrix/AccessPlatform16/site/launcher.aspx
juno.netcare-config.login-url=.albertanetcare.ca/cha/PLBLogin.htm
juno.netcare-config.logout-url=.albertanetcare.ca/concerto/Logout.htm

# ----------------------- AQS ------------------------
juno.aqs.aqs-domain=${AQS_DOMAIN:aqs.cloudpractice.ca}
juno.aqs.aqs-protocol=https
juno.aqs.aqs-api-uri=/api/v1

# ----------------------- Myhealthaccess ------------------------
juno.myhealthaccess.myhealthaccess-telehealth-enabled=true
juno.myhealthaccess.myhealthaccess-protocol=https
juno.myhealthaccess.myhealthaccess-dev-mode=false
juno.myhealthaccess.myhealthaccess-api-uri=/api/rest/v1/my_health_access
juno.myhealthaccess.myhealthaccess-domain=${MYHEALTHACCESS_DOMAIN:www.myhealthaccess.ca}
juno.myhealthaccess.cloudmd-domain=${CLOUDMD_DOMAIN:cloudmd.myhealthaccess.ca}

# ----------------------- External API (IP Whitelist) ------------------------
juno.external-api.whitelist-enabled=true
juno.external-api.system-ips-whitelist=${WHITELIST_TRUSTED_IPS}

# ------------- Ontario EHR  ----------------

### --- Clinical Connect
juno.ontario-ehr.ccv.default-name=Clinical Connect Viewer
juno.ontario-ehr.ccv.default-url=https://swotrain.clinicalconnect.ca
juno.ontario-ehr.ccv.identity-provider-url=https://federation.ehealthontario.ca/fed/idp

### --- Health Partner Gateway (HPG)
juno.ontario-ehr.hpg.default-name=Health Partner Gateway
juno.ontario-ehr.hpg.default-key=HPG_url

### --- eConsult
juno.ontario-ehr.econsult.default-name=EConsult
juno.ontario-ehr.econsult.default-key=OTNeConsult_url

### --- eReport
juno.ontario-ehr.ereport.default-name=EReport
juno.ontario-ehr.ereport.default-key=eReport_url

### --- DHDR
juno.ontario-ehr.dhdr.default-name=Digital Health Drug Repository
juno.ontario-ehr.dhdr.default-url=/MedicationDispense
juno.ontario-ehr.dhdr.drug-formulary-url=https://www.ontario.ca/page/medication-coverage-results/
juno.ontario-ehr.dhdr.special-authorization-digital-information-exchange-url=https://www.health.gov.on.ca/en/pro/programs/sadie/

### --- OAuth/Open ID Connect
juno.ontario-ehr.oidc.discovery-url=https://login.oneidfederation.ehealthontario.ca/oidc/.well-known/openid-configuration
juno.ontario-ehr.oidc.discovery-version=3.0
juno.ontario-ehr.oidc.login-redirect-url=https://redirect.junoemr.com
juno.ontario-ehr.oidc.logout-redirect-url=https://redirect.junoemr.com/logged_out/ontario_ehr
juno.ontario-ehr.oidc.jwt-assertion-aud=https://login.oneidfederation.ehealthontario.ca/sso/oauth2/realms/root/realms/idaasoidc/access_token
juno.ontario-ehr.oidc.signing-certificate=/var/secrets/ehr/prod.ehr_cert.p12
juno.ontario-ehr.oidc.signing-certificate-password=${ONTARIO_EHR.PASSWORD}
juno.ontario-ehr.oidc.default-token-refresh-interval=30

juno.ontario-ehr.oidc.use-url-overrides=true
juno.ontario-ehr.oidc.url-overrides.authorization-url=https://login.oneidfederation.ehealthontario.ca/oidc/authorize
juno.ontario-ehr.oidc.url-overrides.token-url=https://login.oneidfederation.ehealthontario.ca/oidc/access_token
juno.ontario-ehr.oidc.url-overrides.jwks-url=https://login.oneidfederation.ehealthontario.ca/oidc/connect/jwk_uri
juno.ontario-ehr.oidc.url-overrides.revoke-token-url=https://login.oneidfederation.ehealthontario.ca/oidc/oauth2/token/revoke
juno.ontario-ehr.oidc.url-overrides.end-session-url=https://login.oneidfederation.ehealthontario.ca/oidc/connect/endSession
juno.ontario-ehr.oidc.url-overrides.issuer=https://login.oneidfederation.ehealthontario.ca/sso/oauth2/realms/root/realms/idaasoidc

### --- One Access Gateway (OAG) / CMS
juno.ontario-ehr.oag.toolbar-fhir-key=FHIR_iss
juno.ontario-ehr.oag.toolbar-cms-key=hub.url
juno.ontario-ehr.oag.default-timeout-seconds=300

# -----------------------------------------------------

# ----------------------- Pendo ------------------------
# API key for Pendo analytics integration
juno.pendo-config.api-key=299953c7-87c8-48e1-6e04-dad9d436ccc0
pendo_api_key=${pendo_api_key}
# -----------------------------------------------------
juno.pendo-config.api-key=${PENDO_API_KEY:}