basic.empty.showtable=false
basic.show.header=true

# page | list
sort.amount=list

export.amount=list
export.decorated=true

paging.banner.group_size=8
paging.banner.placement=both


css.tr.even=even
css.tr.odd=odd
css.th.sorted=sorted
css.th.ascending=order1
css.th.descending=order2
css.table=
css.th.sortable=sortable

# factory classes for extensions
factory.requestHelper=org.displaytag.util.DefaultRequestHelperFactory

# factory class for decorators
factory.decorator=org.displaytag.decorator.DefaultDecoratorFactory

# locale provider (Jstl provider by default)
locale.provider=org.displaytag.localization.I18nJstlAdapter

# locale.resolver (nothing by default, simply use locale from request)
#locale.resolver=

export.types=csv excel xml pdf

export.csv.class=org.displaytag.export.CsvView
export.excel.class=org.displaytag.export.ExcelView
export.xml.class=org.displaytag.export.XmlView
export.pdf.class=org.displaytag.export.PdfView

export.csv=true
export.csv.label=<span class="export csv">CSV </span>
export.csv.include_header=false
export.csv.filename=

export.excel=true
export.excel.label=<span class="export excel">Excel </span>
export.excel.include_header=true
export.excel.filename=

export.xml=true
export.xml.label=<span class="export xml">XML </span>
export.xml.filename=

export.pdf=false
export.pdf.label=<span class="export pdf">PDF </span>
export.pdf.include_header=true
export.pdf.filename=

export.rtf=false
export.rtf.label=<span class="export rtf">RTF </span>
export.rtf.include_header=true
export.rtf.filename=

# messages

basic.msg.empty_list=Nothing found to display.
basic.msg.empty_list_row=<tr class="empty"><td colspan="{0}">Nothing found to display.</td></tr>
error.msg.invalid_page=invalid page

export.banner=<div class="exportlinks">Export options: {0}</div>
export.banner.sepchar= |

paging.banner.item_name=document
paging.banner.items_name=documents

paging.banner.no_items_found=
paging.banner.one_item_found=<span class="pagebanner">One document found to display.</span>
paging.banner.all_items_found=<span class="pagebanner">{0} {1} found,displaying all {2}.</span>
paging.banner.some_items_found=<span class="pagebanner">{0} {1} found, displaying {2} to {3}.</span>

paging.banner.full=<span class="pagelinks">[<a href="{1}">first</a>/<a href="{2}">prev</a>] page {0}[<a href="{3}">next</a>/<a href="{4}">last</a>]</span>
paging.banner.first=<span class="pagelinks">[first/prev] page {0}[<a href="{3}">next</a>/<a href="{4}">last</a>]</span>
paging.banner.last=<span class="pagelinks">[<a href="{1}">first</a>/<a href="{2}">prev</a>] page {0} [next/last]</span>
paging.banner.onepage=

paging.banner.page.selected=<strong>{0}</strong>
paging.banner.page.link=<a href="{1}" title="Go to page {0}">{0}</a>
paging.banner.page.separator=, \

# external sort and pagination
pagination.sort.param=sort
pagination.sortdirection.param=dir
pagination.pagenumber.param=page
pagination.searchid.param=searchid
pagination.sort.asc.value=asc
pagination.sort.desc.value=desc
pagination.sort.skippagenumber=true

# unused
save.excel.banner=<a href="{0}" rel="external">save ({1} bytes)</a>
save.excel.filename=export.xls

