/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */

package oscar.oscarLab.ca.on;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.oscarehr.common.dao.MeasurementDao;
import org.oscarehr.common.dao.MeasurementMapDao;
import org.oscarehr.util.SpringUtils;
import org.springframework.context.ApplicationContext;
import oscar.oscarLab.ca.all.parsers.MessageHandler;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;

@RunWith(MockitoJUnitRunner.class)
public class CommonLabTestValuesTest {

  private static final Integer DEMOGRAPHIC_ID = 123;
  private static final String IDENT_CODE = "TEST_IDENT";
  private static final String LOINC_CODE = "TEST_LOINC";
  private static final String LAB_1 = "LAB1";
  private static final String LAB_2 = "LAB2";
  private static final String LAB_3 = "LAB3";
  private static final String LAB_4 = "LAB4";
  private static final String OBX_IDENT = "OBX_IDENT";
  private static final String COMMON_LOINC_CODE = "LOINC_CODE";

  @Mock
  private MeasurementDao measurementDao;

  @Mock
  private MeasurementMapDao measurementMapDao;

  @Mock
  private MessageHandler messageHandler;

  @Before
  public void setUp() {
    setupSpringUtilsMock();
  }

  @Test
  public void givenDemographicAndIdentCode_whenFindAndAddLabNumbers_thenReturnCombinedLabNumbers() {
    List<Object> labNumbers = new ArrayList<>(Arrays.asList(LAB_1, LAB_2));
    List<Object> loincLabNumbers = new ArrayList<>(Arrays.asList(LAB_3, LAB_4));
    when(measurementDao.findLabNumbers(DEMOGRAPHIC_ID, IDENT_CODE)).thenReturn(labNumbers);
    when(measurementMapDao.findLoincCodeByIdentCode(IDENT_CODE)).thenReturn(LOINC_CODE);
    when(measurementDao.findLabNumbersByLoincCode(DEMOGRAPHIC_ID, LOINC_CODE))
        .thenReturn(loincLabNumbers);

    List<Object> result = CommonLabTestValues.findAndAddLabNumbers(DEMOGRAPHIC_ID, IDENT_CODE);
    assertEquals(4, result.size());
    assertTrue(result.contains(LAB_1));
    assertTrue(result.contains(LAB_2));
    assertTrue(result.contains(LAB_3));
    assertTrue(result.contains(LAB_4));

    verify(measurementDao).findLabNumbers(DEMOGRAPHIC_ID, IDENT_CODE);
    verify(measurementMapDao).findLoincCodeByIdentCode(IDENT_CODE);
    verify(measurementDao).findLabNumbersByLoincCode(DEMOGRAPHIC_ID, LOINC_CODE);
  }

  @Test
  public void givenLoincCodeFound_whenAddLabNumbersByLoinc_thenAddLabNumbersToList() {
    List<Object> labNumbers = new ArrayList<>(Arrays.asList(LAB_1, LAB_2));
    List<Object> loincLabNumbers = new ArrayList<>(Arrays.asList(LAB_3, LAB_4));
    when(measurementMapDao.findLoincCodeByIdentCode(IDENT_CODE)).thenReturn(LOINC_CODE);
    when(measurementDao.findLabNumbersByLoincCode(DEMOGRAPHIC_ID, LOINC_CODE))
        .thenReturn(loincLabNumbers);

    CommonLabTestValues.addLabNumbersByLoinc(measurementDao, DEMOGRAPHIC_ID, IDENT_CODE, labNumbers);
    assertEquals(4, labNumbers.size());
    assertTrue(labNumbers.contains(LAB_1));
    assertTrue(labNumbers.contains(LAB_2));
    assertTrue(labNumbers.contains(LAB_3));
    assertTrue(labNumbers.contains(LAB_4));

    verify(measurementMapDao).findLoincCodeByIdentCode(IDENT_CODE);
    verify(measurementDao).findLabNumbersByLoincCode(DEMOGRAPHIC_ID, LOINC_CODE);
  }

  @Test
  public void givenNoLoincCode_whenAddLabNumbersByLoinc_thenReturnEarlyWithoutAddingLabNumbers() {
    List<Object> labNumbers = new ArrayList<>(Arrays.asList(LAB_1, LAB_2));

    when(measurementMapDao.findLoincCodeByIdentCode(IDENT_CODE)).thenReturn(null);

    CommonLabTestValues.addLabNumbersByLoinc(measurementDao, DEMOGRAPHIC_ID, IDENT_CODE, labNumbers);
    assertEquals(2, labNumbers.size());
    assertTrue(labNumbers.contains(LAB_1));
    assertTrue(labNumbers.contains(LAB_2));

    verify(measurementMapDao).findLoincCodeByIdentCode(IDENT_CODE);
    verify(measurementDao, never()).findLabNumbersByLoincCode(any(Integer.class), anyString());
  }

  @Test
  public void givenNoLabNumbers_whenAddLabNumbersByLoinc_thenReturnEarlyWithoutAddingLabNumbers() {
    List<Object> labNumbers = new ArrayList<>(Arrays.asList(LAB_1, LAB_2));
    when(measurementMapDao.findLoincCodeByIdentCode(IDENT_CODE)).thenReturn(LOINC_CODE);
    when(measurementDao.findLabNumbersByLoincCode(DEMOGRAPHIC_ID, LOINC_CODE))
        .thenReturn(new ArrayList<>());

    CommonLabTestValues.addLabNumbersByLoinc(measurementDao, DEMOGRAPHIC_ID, IDENT_CODE, labNumbers);
    assertEquals(2, labNumbers.size());
    assertTrue(labNumbers.contains(LAB_1));
    assertTrue(labNumbers.contains(LAB_2));

    verify(measurementMapDao).findLoincCodeByIdentCode(IDENT_CODE);
    verify(measurementDao).findLabNumbersByLoincCode(DEMOGRAPHIC_ID, LOINC_CODE);
  }

  @Test
  public void givenDifferentMatchingConditions_whenFindValuesForTest_thenCorrectlyIdentifyMatches() {
    boolean directMatch = OBX_IDENT.equals(IDENT_CODE);
    String testIdentCode = COMMON_LOINC_CODE;
    boolean loincCodeMatchesIdentCode = COMMON_LOINC_CODE != null && 
        COMMON_LOINC_CODE.equals(testIdentCode);
    boolean loincCodesMatch = COMMON_LOINC_CODE != null && COMMON_LOINC_CODE != null && 
        COMMON_LOINC_CODE.equals(COMMON_LOINC_CODE);
    assertFalse("Direct match should be false", directMatch);
    assertTrue("LOINC code of OBX identifier equals identCode should be true", 
        loincCodeMatchesIdentCode);
    assertTrue("LOINC code of identCode equals LOINC code of OBX identifier should be true", 
        loincCodesMatch);
  }

    private void setupSpringUtilsMock() {
    try {
      Field field = SpringUtils.class.getDeclaredField("beanFactory");
      field.setAccessible(true);
      ApplicationContext mockContext = Mockito.mock(ApplicationContext.class);
      field.set(null, mockContext);

      Mockito.when(mockContext.getBean("measurementDao")).thenReturn(measurementDao);
      Mockito.when(mockContext.getBean("measurementMapDao")).thenReturn(measurementMapDao);
      Mockito.when(mockContext.containsBean("measurementDao")).thenReturn(true);
      Mockito.when(mockContext.containsBean("measurementMapDao")).thenReturn(true);
    } catch (Exception e) {
      Assert.fail("Failed to set up test: " + e.getMessage());
    }
  }
}
