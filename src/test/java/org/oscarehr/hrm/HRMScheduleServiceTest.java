/**
 * Copyright (c) 2012-2018. CloudPractice Inc. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for
 * CloudPractice Inc.
 * Victoria, British Columbia
 * Canada
 */

package org.oscarehr.hrm;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.LocalTime;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.oscarehr.common.server.ServerStateHandler;
import org.oscarehr.config.JunoProperties;
import org.oscarehr.hospitalReportManager.model.HrmFetchResultsModel;
import org.oscarehr.hospitalReportManager.service.HRMScheduleService;
import org.oscarehr.hospitalReportManager.service.HRMService;
import org.springframework.beans.factory.annotation.Autowired;


public class HRMScheduleServiceTest
{
	@Autowired
	@InjectMocks
	private HRMScheduleService hrmScheduleService;

	@Mock
	private HRMService hrmService;

	@Mock
	private ServerStateHandler serverStateHandler;

	@Mock
	private JunoProperties junoProps;

	@Before
	public void setUp()
	{
		MockitoAnnotations.initMocks(this);
		when(hrmService.isHRMEnabled()).thenReturn(true);
        when(hrmService.isHRMFetchEnabled()).thenReturn(true);
        when(serverStateHandler.isThisServerMaster()).thenReturn(true);
	}

	@Test
	public void fetchOnSchedule_all_enabled()
	{
		hrmScheduleService.fetchOnSchedule();
		verify(hrmService, times(1)).consumeRemoteHRMDocuments();
	}

	@Test
	public void fetchOnSchedule_not_master_server()
	{
		when(serverStateHandler.isThisServerMaster()).thenReturn(false);
		hrmScheduleService.fetchOnSchedule();
		verify(hrmService, never()).consumeRemoteHRMDocuments();
	}

	@Test
	public void fetchOnSchedule_hrm_not_enabled()
	{
		when(hrmService.isHRMEnabled()).thenReturn(false);
		hrmScheduleService.fetchOnSchedule();
		verify(hrmService, never()).consumeRemoteHRMDocuments();
	}

	@Test
	public void fetchOnSchedule_hrmFetch_not_enabled()
	{
		when(hrmService.isHRMFetchEnabled()).thenReturn(false);
		hrmScheduleService.fetchOnSchedule();
		verify(hrmService, never()).consumeRemoteHRMDocuments();
	}

	@Test
	public void fetchNow_localOverride_not_enabled()
	{
		HrmFetchResultsModel expected = setUpFetchResultsModal(true, true, 3, 3);
		JunoProperties.Hrm hrmConfig = mock(JunoProperties.Hrm.class);

		when(junoProps.getHrm()).thenReturn(hrmConfig);
		when(hrmConfig.isLocalOverrideEnabled()).thenReturn(false);
		when(hrmService.consumeRemoteHRMDocuments()).thenReturn(expected);

		HrmFetchResultsModel actual = hrmScheduleService.fetchNow();
		actual.setStartTime(LocalDateTime.now().with(LocalTime.MIDNIGHT));

		assertEquals(expected, actual);
		verify(hrmService, times(1)).consumeRemoteHRMDocuments();

	}

	@Test
	public void fetchNow_localOverride_enabled()
	{
		String testLocalDirectory = "/mnt/storage/hrm/";
		HrmFetchResultsModel expected = setUpFetchResultsModal(true, true, 5, 5);
		JunoProperties.Hrm hrmConfig = mock(JunoProperties.Hrm.class);

		when(junoProps.getHrm()).thenReturn(hrmConfig);
		when(hrmConfig.isLocalOverrideEnabled()).thenReturn(true);
		when(hrmConfig.getLocalOverrideDirectory()).thenReturn(testLocalDirectory);
		when(hrmService.consumeLocalHRMDocuments(Paths.get(testLocalDirectory))).thenReturn(expected);

		HrmFetchResultsModel actual = hrmScheduleService.fetchNow();
		actual.setStartTime(LocalDateTime.now().with(LocalTime.MIDNIGHT));

		assertEquals(expected, actual);
		verify(hrmService, times(1)).consumeLocalHRMDocuments(Paths.get(testLocalDirectory));

	}

	@Test
	public void fetchNow_not_master_server()
	{
		HrmFetchResultsModel expected = setUpFetchResultsModal(false, false, 0, 0);
		when(serverStateHandler.isThisServerMaster()).thenReturn(false);
		HrmFetchResultsModel actual = hrmScheduleService.fetchNow();
		actual.setStartTime(LocalDateTime.now().with(LocalTime.MIDNIGHT));

		assertEquals(expected, actual);
	}


	private HrmFetchResultsModel setUpFetchResultsModal(boolean loginSuccess, boolean downloadSuccess, int reportsDownloaded, int reportsProcessed)
	{
		HrmFetchResultsModel fetchResultsModel = new HrmFetchResultsModel();
		fetchResultsModel.setLoginSuccess(loginSuccess);
		fetchResultsModel.setDownloadSuccess(downloadSuccess);
		fetchResultsModel.setStartTime(LocalDateTime.now().with(LocalTime.MIDNIGHT));
		fetchResultsModel.setReportsDownloaded(reportsDownloaded);
		fetchResultsModel.setReportsProcessed(reportsProcessed);

		return fetchResultsModel;
	}
}
