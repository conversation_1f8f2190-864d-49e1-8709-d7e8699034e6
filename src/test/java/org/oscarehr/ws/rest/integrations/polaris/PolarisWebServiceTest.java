/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package org.oscarehr.ws.rest.integrations.polaris;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import javax.ws.rs.core.Response;
import lombok.val;
import org.junit.Before;
import org.junit.Test;
import org.oscarehr.integration.polaris.PolarisApiConnectionException;
import org.oscarehr.integration.polaris.model.CparUiAttributes;
import org.oscarehr.ws.rest.response.GenericRestResponse.ResponseStatus;
import org.oscarehr.ws.rest.response.RestResponse;

public class PolarisWebServiceTest {

  private static final String TEST_ORG_ID = "test-org-id";
  private static final String TEST_MEDPLUM_BASE_URL = "https://test.medplum.com";
  private static final String TEST_ACCESS_TOKEN = "test-access-token";
  private static final String TEST_COMPONENT_URL
      = "https://packages.staging.apps.health/cpar/0.0.6/cpar.umd.js";

  private TestPolarisWebService polarisWebService;

  @Before
  public void setUp() {
    polarisWebService = new TestPolarisWebService();
  }

  // Test double class that simulates PolarisWebService behavior
  private static class TestPolarisWebService {
    private CparUiAttributes mockAttributes;
    private Exception mockException;
    private boolean securityCheckCalled = false;
    private boolean serviceMethodCalled = false;

    public void setMockAttributes(CparUiAttributes attributes) {
      this.mockAttributes = attributes;
    }

    public void setMockException(Exception exception) {
      this.mockException = exception;
    }

    public boolean wasSecurityCheckCalled() {
      return securityCheckCalled;
    }

    public boolean wasServiceMethodCalled() {
      return serviceMethodCalled;
    }

    public Response getCparUiAttributes() {
      try {
        // Simulate security check
        securityCheckCalled = true;

        // Simulate service call
        serviceMethodCalled = true;
        if (mockException != null) {
          if (mockException instanceof RuntimeException) {
            throw (RuntimeException) mockException;
          } else if (mockException instanceof PolarisApiConnectionException) {
            throw (PolarisApiConnectionException) mockException;
          } else {
            throw new RuntimeException(mockException);
          }
        }

        return Response.ok(RestResponse.successResponse(mockAttributes)).build();
      } catch (SecurityException e) {
        return buildErrorResponse(Response.Status.FORBIDDEN, "Permission denied");
      } catch (UnsupportedOperationException e) {
        return buildErrorResponse(Response.Status.SERVICE_UNAVAILABLE, e.getMessage());
      } catch (IllegalStateException e) {
        return buildErrorResponse(Response.Status.NOT_FOUND, e.getMessage());
      } catch (PolarisApiConnectionException e) {
        return buildErrorResponse(Response.Status.INTERNAL_SERVER_ERROR, e.getMessage());
      }
    }

    private Response buildErrorResponse(final Response.Status status, final String message) {
      return Response.status(status)
          .entity(RestResponse.errorResponse(message))
          .build();
    }
  }

  @Test
  public void givenSecurityCheckPasses_whenGetCparUiAttributes_thenCallService() {
    val mockAttributes = new CparUiAttributes(
        TEST_ORG_ID,
        TEST_MEDPLUM_BASE_URL,
        TEST_ACCESS_TOKEN,
        TEST_COMPONENT_URL
    );
    polarisWebService.setMockAttributes(mockAttributes);

    polarisWebService.getCparUiAttributes();

    // Verify that security check and service method were called
    assertEquals(true, polarisWebService.wasSecurityCheckCalled());
    assertEquals(true, polarisWebService.wasServiceMethodCalled());
  }

  @Test
  public void givenCompleteAttributes_whenGetCparUiAttributes_thenIncludeAllInResponse() {
    val mockAttributes = new CparUiAttributes(
        TEST_ORG_ID,
        TEST_MEDPLUM_BASE_URL,
        TEST_ACCESS_TOKEN,
        TEST_COMPONENT_URL
    );

    polarisWebService.setMockAttributes(mockAttributes);

    val response = polarisWebService.getCparUiAttributes();

    assertNotNull(response);
    assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());

    val restResponse = (RestResponse<CparUiAttributes>) response.getEntity();
    assertNotNull(restResponse);
    assertEquals(ResponseStatus.SUCCESS, restResponse.getStatus());
    assertNotNull(restResponse.getBody());
    assertEquals(CparUiAttributes.ORGANIZATION_IDENTIFIER_SYSTEM,
        restResponse.getBody().getOrganizationIdentifierSystem());
    assertEquals(TEST_ORG_ID, restResponse.getBody().getOrganizationIdentifierValue());
    assertEquals(TEST_MEDPLUM_BASE_URL, restResponse.getBody().getMedplumBaseUrl());
    assertEquals(CparUiAttributes.DEFAULT_THEME, restResponse.getBody().getTheme());
    assertEquals(TEST_ACCESS_TOKEN, restResponse.getBody().getMedplumAccessToken());
    assertEquals(String.format(CparUiAttributes.PATIENT_IDENTIFIER_SYSTEM_TEMPLATE, TEST_ORG_ID),
        restResponse.getBody().getIdentifierSystem());
  }

  @Test
  public void givenCparIntegrationDisabled_whenGetCparUiAttributes_thenReturnErrorResponse() {
    val errorMessage = "CPAR integration is not enabled";
    polarisWebService.setMockException(new UnsupportedOperationException(errorMessage));

    val response = polarisWebService.getCparUiAttributes();

    assertNotNull(response);
    assertEquals(Response.Status.SERVICE_UNAVAILABLE.getStatusCode(), response.getStatus());

    val restResponse = (RestResponse<?>) response.getEntity();
    assertNotNull(restResponse);
    assertEquals(ResponseStatus.ERROR, restResponse.getStatus());
    assertNotNull(restResponse.getError());
    assertEquals(errorMessage, restResponse.getError().getMessage());
  }

  @Test
  public void givenMissingRequiredField_whenGetCparUiAttributes_thenReturnErrorResponse() {
    val errorMessage = "Organization identifier is required";
    polarisWebService.setMockException(new IllegalStateException(errorMessage));

    val response = polarisWebService.getCparUiAttributes();

    assertNotNull(response);
    assertEquals(Response.Status.NOT_FOUND.getStatusCode(), response.getStatus());

    val restResponse = (RestResponse<?>) response.getEntity();
    assertNotNull(restResponse);
    assertEquals(ResponseStatus.ERROR, restResponse.getStatus());
    assertNotNull(restResponse.getError());
    assertEquals(errorMessage, restResponse.getError().getMessage());
  }

  @Test
	public void givenConnectionError_whenGetCparUiAttributes_thenReturnErrorResponse() {
		val errorMessage = "Error refreshing or loading OAuth credentials";
		polarisWebService.setMockException(new PolarisApiConnectionException(errorMessage));

		val response = polarisWebService.getCparUiAttributes();

		assertNotNull(response);
		assertEquals(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(), response.getStatus());

		val restResponse = (RestResponse<?>) response.getEntity();
		assertNotNull(restResponse);
		assertEquals(ResponseStatus.ERROR, restResponse.getStatus());
		assertNotNull(restResponse.getError());
		assertEquals(errorMessage, restResponse.getError().getMessage());
	}
}
