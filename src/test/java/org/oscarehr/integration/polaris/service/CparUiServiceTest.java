/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package org.oscarehr.integration.polaris.service;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

import java.util.UUID;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.oscarehr.common.encryption.StringEncryptor;
import org.oscarehr.integration.polaris.PolarisApiConnectionException;
import org.oscarehr.integration.polaris.PolarisApiConnector;
import org.oscarehr.integration.polaris.model.CparUiAttributes;
import org.oscarehr.integration.polaris.dao.PolarisConfigurationDao;
import org.oscarehr.integration.polaris.model.PolarisConfiguration;
import lombok.val;

public class CparUiServiceTest {

  private static final String TEST_MEDPLUM_BASE_URL = "test.medplum.com";
  private static final String TEST_CLIENT_ID = "test-client-id";
  private static final String TEST_ACCESS_TOKEN = "test-access-token";
  private static final String ENCRYPTED_TEST_CLIENT_SECRET =
      StringEncryptor.encrypt("test-client-secret");
  private static final String TEST_COMPONENT_URL
      = "https://packages.staging.apps.health/cpar/0.0.6/cpar.umd.js";
  public static final String WHITESPACE = " ";

  @InjectMocks
  private CparUiService cparUiService;

  @Mock
  private PolarisConfigurationDao polarisConfigurationDao;

  @Mock
  private PolarisApiConnector polarisApiConnector;

  @Before
  public void setUp() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void givenAllValuesAvailable_whenGetCparUiAttributes_thenReturnCompleteAttributes()
      throws PolarisApiConnectionException {
    val polarisConfig = createPolarisConfigurationWithUuid(true);

    when(polarisConfigurationDao.getPolarisConfiguration()).thenReturn(polarisConfig);
    when(polarisApiConnector.getLatestToken()).thenReturn(TEST_ACCESS_TOKEN);

    val result = cparUiService.getCparUiAttributes();

    assertEquals(
        CparUiAttributes.ORGANIZATION_IDENTIFIER_SYSTEM, result.getOrganizationIdentifierSystem());
    assertEquals(CparUiAttributes.DEFAULT_THEME, result.getTheme());
    assertEquals(polarisConfig.getOrganizationId(), result.getOrganizationIdentifierValue());
    assertEquals(TEST_MEDPLUM_BASE_URL, result.getMedplumBaseUrl());
    assertEquals(TEST_ACCESS_TOKEN, result.getMedplumAccessToken());
    assertEquals(
        String.format(
            CparUiAttributes.PATIENT_IDENTIFIER_SYSTEM_TEMPLATE, polarisConfig.getOrganizationId()),
        result.getIdentifierSystem());
  }

  @Test(expected = IllegalStateException.class)
  public void givenMissingOrgId_whenGetCparUiAttributes_thenThrowException()
      throws PolarisApiConnectionException {
    val polarisConfig = new PolarisConfiguration();

    polarisConfig.setIsCparEnabled(true);
    polarisConfig.setOrganizationId(null);

    when(polarisConfigurationDao.getPolarisConfiguration()).thenReturn(polarisConfig);

    cparUiService.getCparUiAttributes();
  }

  @Test(expected = IllegalStateException.class)
  public void givenMissingMedplumPreferences_whenGetCparUiAttributes_thenThrowException()
      throws PolarisApiConnectionException {
    val polarisConfig = createPolarisConfigurationWithUuid(true);

    polarisConfig.setAdminPolarisBasePath(null);
    polarisConfig.setAdminPolarisFhirClientId(null);
    polarisConfig.setAdminPolarisFhirClientSecret(null);

    when(polarisConfigurationDao.getPolarisConfiguration()).thenReturn(polarisConfig);

    cparUiService.getCparUiAttributes();
  }

  @Test(expected = IllegalStateException.class)
  public void givenNothingFetched_whenGetCparUiAttributes_thenThrowException()
      throws PolarisApiConnectionException {
    when(polarisConfigurationDao.getPolarisConfiguration()).thenReturn(null);

    cparUiService.getCparUiAttributes();
  }

  @Test(expected = IllegalStateException.class)
  public void givenWhitespaceOrganizationId_whenGetCparUiAttributes_thenThrowException()
      throws PolarisApiConnectionException {
    val polarisConfig = new PolarisConfiguration();

    polarisConfig.setIsCparEnabled(true);
    polarisConfig.setOrganizationId(WHITESPACE);

    cparUiService.getCparUiAttributes();
  }

  @Test(expected = IllegalStateException.class)
  public void givenEmptyMedplumBaseUrl_whenGetCparUiAttributes_thenThrowException()
      throws PolarisApiConnectionException {
    val polarisConfig = createPolarisConfigurationWithUuid(true);

    polarisConfig.setAdminPolarisBasePath("");

    when(polarisConfigurationDao.getPolarisConfiguration()).thenReturn(polarisConfig);

    cparUiService.getCparUiAttributes();
  }

  @Test(expected = UnsupportedOperationException.class)
  public void givenCparIntegrationDisabled_whenGetCparUiAttributes_thenThrowException()
      throws PolarisApiConnectionException {
    val polarisConfig = createPolarisConfigurationWithUuid(false);
    when(polarisConfigurationDao.getPolarisConfiguration()).thenReturn(polarisConfig);

    cparUiService.getCparUiAttributes();
  }

  @Test(expected = IllegalStateException.class)
  public void givenMissingComponentUrl_whenGetCparUiAttributes_thenThrowException()
      throws PolarisApiConnectionException {
    val polarisConfig = createPolarisConfigurationWithUuid(true);

    polarisConfig.setCparComponentUrl("");

    when(polarisConfigurationDao.getPolarisConfiguration()).thenReturn(polarisConfig);

    cparUiService.getCparUiAttributes();
  }

  private PolarisConfiguration createPolarisConfigurationWithUuid(final boolean isCparEnabled) {
    val config = new PolarisConfiguration();

    config.setOrganizationId(UUID.randomUUID().toString());
    config.setIsCparEnabled(isCparEnabled);
    config.setAdminPolarisBasePath(TEST_MEDPLUM_BASE_URL);
    config.setAdminPolarisFhirClientId(TEST_CLIENT_ID);
    config.setAdminPolarisFhirClientSecret(ENCRYPTED_TEST_CLIENT_SECRET);
    config.setCparComponentUrl(TEST_COMPONENT_URL);

    return config;
  }
}
