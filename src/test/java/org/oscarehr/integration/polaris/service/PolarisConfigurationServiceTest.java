/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package org.oscarehr.integration.polaris.service;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertThrows;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import java.util.UUID;
import lombok.val;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.oscarehr.integration.polaris.dao.PolarisConfigurationDao;
import org.oscarehr.integration.polaris.model.PolarisConfiguration;
import org.springframework.test.context.ActiveProfiles;

@ActiveProfiles("test")
public class PolarisConfigurationServiceTest {

  public static final String TEST_SAMPLE_URL = "https://example.com/cpar.umd.js";
  @Mock
  private PolarisConfigurationDao polarisConfigurationDao;
  private PolarisConfigurationService service;

  @Before
  public void setUp() {
    openMocks(this);
    service = new PolarisConfigurationService(polarisConfigurationDao);
  }

  @Test
  public void givenPolarisConfig_whenGeneratePolarisOrganizationId_thenReturnGeneratedId() {
    val polarisConfig = new PolarisConfiguration();
    when(polarisConfigurationDao.getPolarisConfiguration()).thenReturn(polarisConfig);

    String generatedId = service.generatePolarisOrganizationId();

    assertNotNull(generatedId);
    assertFalse(generatedId.isEmpty());
    assertEquals(generatedId, polarisConfig.getOrganizationId());
    assertEquals(36, generatedId.length());
    assertEquals(UUID.fromString(generatedId).toString(), generatedId);
    verify(polarisConfigurationDao).merge(polarisConfig);
  }

  @Test
  public void givenNoPolarisConfig_whenGeneratePolarisOrganizationId_thenThrowException() {
    when(polarisConfigurationDao.getPolarisConfiguration()).thenReturn(null);

    assertThrows(IllegalStateException.class, () -> service.generatePolarisOrganizationId());
    verify(polarisConfigurationDao, never()).merge(any(PolarisConfiguration.class));
  }

  @Test
  public void givenPolarisConfig_whenGetPolarisUuid_thenReturnUuid() {
    val polarisConfig = createPolarisConfigWithUuid();
    when(polarisConfigurationDao.getPolarisConfiguration()).thenReturn(polarisConfig);

    val result = service.getPolarisUuid();

    assertNotNull(result);
    assertEquals(polarisConfig.getOrganizationId(), result);
  }

  @Test
  public void givenNoPolarisConfig_whenGetPolarisUuid_thenReturnNull() {
    when(polarisConfigurationDao.getPolarisConfiguration()).thenReturn(null);

    val result = service.getPolarisUuid();

    assertNull(result);
  }

  @Test
  public void givenPolarisConfigWithoutUuid_whenGetPolarisUuid_thenReturnNull() {
    val polarisConfig = new PolarisConfiguration();
    when(polarisConfigurationDao.getPolarisConfiguration()).thenReturn(polarisConfig);

    val result = service.getPolarisUuid();

    assertNull(result);
  }

  @Test
  public void givenPolarisConfigWithBlankUuid_whenGetPolarisUuid_thenReturnFalsePair() {
    val polarisConfig = new PolarisConfiguration();
    polarisConfig.setOrganizationId("");
    when(polarisConfigurationDao.getPolarisConfiguration()).thenReturn(polarisConfig);

    val result = service.getPolarisUuid();

    assertNull(result);
  }

  private PolarisConfiguration createPolarisConfigWithUuid() {
    val polarisConfig = new PolarisConfiguration();
    val organizationUuid = UUID.randomUUID().toString();
    polarisConfig.setOrganizationId(organizationUuid);
    return polarisConfig;
  }

  @Test
  public void givenPolarisConfigWithComponentUrl_whenGetCparComponentUrl_thenReturnUrl() {
    val polarisConfig = new PolarisConfiguration();
    polarisConfig.setCparComponentUrl(TEST_SAMPLE_URL);
    when(polarisConfigurationDao.getPolarisConfiguration()).thenReturn(polarisConfig);

    val result = service.getCparComponentUrl();

    assertEquals(TEST_SAMPLE_URL, result);
  }

  @Test
  public void givenPolarisConfigWithoutComponentUrl_whenGetCparComponentUrl_thenReturnDefaultUrl() {
    val polarisConfig = new PolarisConfiguration();
    when(polarisConfigurationDao.getPolarisConfiguration()).thenReturn(polarisConfig);

    val result = service.getCparComponentUrl();

    assertNull(result);
  }

  @Test
  public void givenNoPolarisConfig_whenGetCparComponentUrl_thenReturnDefaultUrl() {
    when(polarisConfigurationDao.getPolarisConfiguration()).thenReturn(null);

    val result = service.getCparComponentUrl();

    assertNull(result);
  }

  @Test
  public void givenPolarisConfig_whenSetCparComponentUrl_thenUpdateConfig() {
    val polarisConfig = new PolarisConfiguration();
    when(polarisConfigurationDao.getPolarisConfiguration()).thenReturn(polarisConfig);

    service.setCparComponentUrl(TEST_SAMPLE_URL);

    assertEquals(TEST_SAMPLE_URL, polarisConfig.getCparComponentUrl());
    verify(polarisConfigurationDao).merge(polarisConfig);
  }

  @Test
  public void givenNoPolarisConfig_whenSetCparComponentUrl_thenThrowException() {
    when(polarisConfigurationDao.getPolarisConfiguration()).thenReturn(null);

    assertThrows(IllegalStateException.class,
        () -> service.setCparComponentUrl(TEST_SAMPLE_URL));
    verify(polarisConfigurationDao, never()).merge(any(PolarisConfiguration.class));
  }
}
