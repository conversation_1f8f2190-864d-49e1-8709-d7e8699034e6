/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package org.oscarehr.integration.polaris;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.read.ListAppender;
import java.util.Collections;
import java.util.Map;
import java.util.UUID;
import lombok.val;
import org.hl7.fhir.r4.model.Patient;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.oscarehr.integration.polaris.model.PolarisConfiguration;
import org.oscarehr.integration.polaris.service.PolarisConfigurationService;
import ch.qos.logback.classic.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;

@RunWith(MockitoJUnitRunner.class)
public class PolarisApiConnectorTest {

  //@Mock private SystemPreferenceService mockSystemPreferenceService;
  @Mock private PolarisCredentialStoreWrapper mockCredentialStoreWrapper;
  @Mock
  private PolarisConfigurationService polarisConfigurationService;

  @Before
  public void setUp() throws Exception {
    val polarisConfiguration = createPolarisConfigurationWithUuid(true);

    polarisConfiguration.setAdminPolarisFhirEndpoint("https://data.dev.apps.health/fhir/R4");
    polarisConfiguration.setAdminPolarisFhirClientId(
        "fe5ea6c59153e6bbdb83a571de330266f62d7096ec4397a48ad69b311bab1468");
    polarisConfiguration.setAdminPolarisFhirClientSecret(
        "kacynNSFWkCdPxOCymDgpztIjcXUEoZUHMgovT8yykdfkbwjiSPwJyE6ImBhkSXPXKdsT3K+qsje\n"
            + "evgZIi0aa44GPRmlI0R+849zyjR3pkel4Tj6QI/Obwzr5vvJywCB");
    polarisConfiguration.setAdminPolarisBasePath("https://data.dev.apps.health/");

    when(polarisConfigurationService.getPolarisConfiguration()).thenReturn(polarisConfiguration);
  }

  @Test
  public void givenEmptyUrl_whenConstructor_thenEmptyRestUrlAndWarn() {
    val polarisConfiguration = createPolarisConfigurationWithUuid(true);

    polarisConfiguration.setAdminPolarisFhirEndpoint("");
    when(polarisConfigurationService.getPolarisConfiguration()).thenReturn(polarisConfiguration);

    val logger = (Logger) LoggerFactory.getLogger(PolarisApiConnector.class);
    ListAppender<ILoggingEvent> listAppender = new ListAppender<>();
    listAppender.start();
    logger.addAppender(listAppender);

    val connector = new PolarisApiConnector(polarisConfigurationService, mockCredentialStoreWrapper);
    val found = listAppender.list.stream()
        .anyMatch(event -> event.getFormattedMessage().contains("PolarisApiConnector failed to construct: Polaris API URL not set"));

    assertEquals("", connector.restApiUrl);
    assertTrue(found);
  }

  @Test
  public void givenInvalidUrl_whenConstructor_thenEmptyRestUrlAndWarn() {
    val polarisConfiguration = createPolarisConfigurationWithUuid(true);

    polarisConfiguration.setAdminPolarisFhirEndpoint("bad url");
    when(polarisConfigurationService.getPolarisConfiguration()).thenReturn(polarisConfiguration);

    val logger = (Logger) LoggerFactory.getLogger(PolarisApiConnector.class);
    ListAppender<ILoggingEvent> listAppender = new ListAppender<>();
    listAppender.start();
    logger.addAppender(listAppender);

    val connector = new PolarisApiConnector(polarisConfigurationService, mockCredentialStoreWrapper);
    val found = listAppender.list.stream()
        .anyMatch(event -> event.getFormattedMessage().contains("PolarisApiConnector failed to construct: Invalid Polaris API URL"));

    assertEquals("", connector.restApiUrl);
    assertTrue(found);
  }

  @Test(expected = PolarisApiConnectionException.class)
  public void givenNullFhirObject_whenPost_thenThrow() throws Exception {
    val fakeHeaders = new HttpHeaders();
    val connector = new PolarisApiConnector(polarisConfigurationService, mockCredentialStoreWrapper);

    fakeHeaders.set(HttpHeaders.AUTHORIZATION, "Bearer dummy");
    PolarisApiConnector spyConnector = Mockito.spy(connector);

    doReturn(fakeHeaders).when(spyConnector).getAuthorizationBearerHeadersWithFetch();

    spyConnector.makeFhirRequest("/Patient", Collections.emptyMap(), "POST", null, Map.class);
  }

  @Test(expected = PolarisApiConnectionException.class)
  public void givenNullFhirObject_whenPut_thenThrow() throws Exception {
    val fakeHeaders = new HttpHeaders();
    val connector = new PolarisApiConnector(polarisConfigurationService, mockCredentialStoreWrapper);

    fakeHeaders.set(HttpHeaders.AUTHORIZATION, "Bearer dummy");
    PolarisApiConnector spyConnector = Mockito.spy(connector);

    doReturn(fakeHeaders).when(spyConnector).getAuthorizationBearerHeadersWithFetch();

    spyConnector.makeFhirRequest("/Patient", Collections.emptyMap(), "PUT", null, Map.class);
  }

  @Test(expected = PolarisApiConnectionException.class)
  public void givenDeleteMethod_whenRequest_thenThrow() throws Exception {
    val fakeHeaders = new HttpHeaders();
    val connector = new PolarisApiConnector(polarisConfigurationService, mockCredentialStoreWrapper);

    fakeHeaders.set(HttpHeaders.AUTHORIZATION, "Bearer dummy");
    PolarisApiConnector spyConnector = Mockito.spy(connector);

    doReturn(fakeHeaders).when(spyConnector).getAuthorizationBearerHeadersWithFetch();

    spyConnector.makeFhirRequest("/Patient", Collections.emptyMap(), "DELETE", null, Map.class);
  }

  @Test(expected = IllegalArgumentException.class)
  public void givenInvalidMethod_whenRequest_thenThrow() throws Exception {
    val fakeHeaders = new HttpHeaders();
    val connector = new PolarisApiConnector(polarisConfigurationService, mockCredentialStoreWrapper);

    fakeHeaders.set(HttpHeaders.AUTHORIZATION, "Bearer dummy");
    PolarisApiConnector spyConnector = Mockito.spy(connector);

    doReturn(fakeHeaders).when(spyConnector).getAuthorizationBearerHeadersWithFetch();

    spyConnector.makeFhirRequest("/Patient", Collections.emptyMap(), "PATCH", null, Map.class);
  }

  @Test
  public void givenValidPostRequest_whenMakeFhirRequest_thenReturnsResponse() throws Exception {
    val patient = new Patient();
    val connector = new PolarisApiConnector(polarisConfigurationService, mockCredentialStoreWrapper);
    PolarisApiConnector spyConnector = Mockito.spy(connector);

    val fakeHeaders = new HttpHeaders();
    fakeHeaders.set(HttpHeaders.AUTHORIZATION, "Bearer dummy");

    doReturn(fakeHeaders).when(spyConnector).getAuthorizationBearerHeadersWithFetch();
    doReturn("response")
        .when(spyConnector)
        .doPost(anyString(), any(), anyString(), eq(String.class));

    String result =
        spyConnector.makeFhirRequest(
            "/Patient", Collections.emptyMap(), "POST", patient, String.class);
    assertEquals("response", result);
  }

  @Test
  public void givenValidGetRequest_whenMakeFhirRequest_thenReturnsResponse() throws Exception {
    val connector = new PolarisApiConnector(polarisConfigurationService, mockCredentialStoreWrapper);
    PolarisApiConnector spyConnector = Mockito.spy(connector);

    val fakeHeaders = new HttpHeaders();
    fakeHeaders.set(HttpHeaders.AUTHORIZATION, "Bearer dummy");

    doReturn(fakeHeaders).when(spyConnector).getAuthorizationBearerHeadersWithFetch();

    doReturn("response").when(spyConnector).doGet(anyString(), any(), eq(String.class));

    String result =
        spyConnector.makeFhirRequest("/Patient", Collections.emptyMap(), "GET", null, String.class);
    assertEquals("response", result);
  }

  @Test
  public void givenValidPutRequest_whenMakeFhirRequest_thenReturnsResponse() throws Exception {
    val patient = new Patient();
    val connector = new PolarisApiConnector(polarisConfigurationService, mockCredentialStoreWrapper);
    PolarisApiConnector spyConnector = Mockito.spy(connector);

    val fakeHeaders = new HttpHeaders();
    fakeHeaders.set(HttpHeaders.AUTHORIZATION, "Bearer dummy");

    doReturn(fakeHeaders).when(spyConnector).getAuthorizationBearerHeadersWithFetch();

    doReturn("response")
        .when(spyConnector)
        .doPut(anyString(), any(), anyString(), eq(String.class));

    String result =
        spyConnector.makeFhirRequest(
            "/Patient", Collections.emptyMap(), "PUT", patient, String.class);

    assertEquals("response", result);
  }

  @Test
  public void givenValidToken_whenGetAuthorizationBearerHeaders_thenReturnHeaders()
      throws Exception {
      val connector = new PolarisApiConnector(polarisConfigurationService, mockCredentialStoreWrapper);
    when(mockCredentialStoreWrapper.getAccessToken()).thenReturn("valid-token");

    HttpHeaders headers = connector.getAuthorizationBearerHeaders();
    assertEquals("Bearer valid-token", headers.getFirst(HttpHeaders.AUTHORIZATION));
  }

  @Test(expected = PolarisApiConnectionException.class)
  public void givenNullToken_whenGetAuthorizationBearerHeaders_thenThrow() throws Exception {
      val connector = new PolarisApiConnector(polarisConfigurationService, mockCredentialStoreWrapper);
    when(mockCredentialStoreWrapper.getAccessToken()).thenReturn(null);
    connector.getAuthorizationBearerHeaders();
  }

  @Test
  public void givenValidAccessToken_whenGetLatestToken_thenReturnCachedToken() throws Exception {
      val connector = new PolarisApiConnector(polarisConfigurationService, mockCredentialStoreWrapper);
    when(mockCredentialStoreWrapper.getAccessToken()).thenReturn("valid-token");

    val spyConnector = Mockito.spy(connector);
    doReturn(true).when(spyConnector).isTokenValid();

    val token = spyConnector.getLatestToken();

    assertEquals("valid-token", token);
    verify(spyConnector, never()).fetchAuthTokens();
  }

  @Test
  public void givenNullAccessToken_whenGetLatestToken_thenFetchAndReturnNewToken()
      throws Exception {
          val connector = new PolarisApiConnector(polarisConfigurationService, mockCredentialStoreWrapper);

    when(mockCredentialStoreWrapper.getAccessToken())
        .thenReturn(null) // (before fetch)
        .thenReturn("new-token"); // (after fetch)

    val spyConnector = Mockito.spy(connector);

    lenient().doReturn(false).when(spyConnector).isTokenValid();
    doNothing().when(spyConnector).fetchAuthTokens();

    val token = spyConnector.getLatestToken();

    assertEquals("new-token", token);
    verify(spyConnector).fetchAuthTokens();
  }

  @Test
  public void givenInvalidToken_whenGetLatestToken_thenFetchAndReturnNewToken() throws Exception {
      val connector = new PolarisApiConnector(polarisConfigurationService, mockCredentialStoreWrapper);
    when(mockCredentialStoreWrapper.getAccessToken())
        .thenReturn("expired-token")
        .thenReturn("fresh-token");

    val spyConnector = Mockito.spy(connector);
    doReturn(false).when(spyConnector).isTokenValid();
    doNothing().when(spyConnector).fetchAuthTokens();

    val token = spyConnector.getLatestToken();

    assertEquals("fresh-token", token);
    verify(spyConnector).fetchAuthTokens();
  }

  @Test
  public void givenBadBaseUrlString_whenGetConfigurationEndpoints_thenReturnNull() {
    val polarisConfiguration = createPolarisConfigurationWithBasePath("bad-url");
    when(polarisConfigurationService.getPolarisConfiguration())
        .thenReturn(polarisConfiguration);
    val connector = new PolarisApiConnector(polarisConfigurationService,
        mockCredentialStoreWrapper);
    assertNull(connector.getConfigurationEndpoints());
  }

  @Test
  public void givenValidBaseUrlString_whenGetConfigurationEndpoints_thenReturnEndpoints() {
    val connector = new PolarisApiConnector(polarisConfigurationService,
        mockCredentialStoreWrapper);

    val spyConnector = Mockito.spy(connector);
    val polarisEndpointConfiguration = new PolarisEndpointConfiguration();
    doReturn(polarisEndpointConfiguration).when(spyConnector).doGet(any(), any());

    val result = spyConnector.getConfigurationEndpoints();
    assertNotNull(result);
  }

  private PolarisConfiguration createPolarisConfigurationWithUuid(final boolean isCparEnabled) {
    val config = new PolarisConfiguration();

    config.setOrganizationId(UUID.randomUUID().toString());
    config.setIsCparEnabled(isCparEnabled);

    return config;
  }

  private PolarisConfiguration createPolarisConfigurationWithBasePath(final String basePath) {
    val config = createPolarisConfigurationWithUuid(true);
    config.setAdminPolarisBasePath(basePath);
    return config;
  }
}
