/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package org.oscarehr.integration.polaris;

import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.Map;
import org.junit.Assert;
import org.junit.Test;

public class PolarisUtilsTest {

  @Test
  public void givenValidUrl_whenIsValidUrlCalled_thenReturnTrue() {
    Assert.assertTrue(PolarisUtils.isValidUrl("http://example.com"));
    Assert.assertTrue(PolarisUtils.isValidUrl("https://example.com"));
    Assert.assertTrue(PolarisUtils.isValidUrl("https://example.com:8080/path"));
  }

  @Test
  public void givenInvalidUrl_whenIsValidUrlCalled_thenReturnFalse() {
    Assert.assertFalse(PolarisUtils.isValidUrl("htp:/example.com"));
    Assert.assertFalse(PolarisUtils.isValidUrl("just-a-string"));
    Assert.assertFalse(PolarisUtils.isValidUrl(null));
    Assert.assertFalse(PolarisUtils.isValidUrl(""));
  }

  @Test
  public void givenNonEmptyNonPlaceholderString_whenRequiredVarSetCalled_thenReturnTrue() {
    Assert.assertTrue(PolarisUtils.requiredVarSet("hello"));
    Assert.assertTrue(PolarisUtils.requiredVarSet("value123"));
  }

  @Test
  public void givenNullOrPlaceholderString_whenRequiredVarSetCalled_thenReturnFalse() {
    Assert.assertFalse(PolarisUtils.requiredVarSet(null));
    Assert.assertFalse(PolarisUtils.requiredVarSet(""));
    Assert.assertFalse(PolarisUtils.requiredVarSet("    "));
    Assert.assertFalse(PolarisUtils.requiredVarSet("$PLACEHOLDER"));
  }

  @Test
  public void givenValidUrlWithoutPort_whenGetBasePathCalled_thenReturnBaseUrl()
      throws MalformedURLException {
    String basePath = PolarisUtils.getBasePath("https://example.com/path/to/resource");
    Assert.assertEquals("https://example.com", basePath);
  }

  @Test
  public void givenValidUrlWithPort_whenGetBasePathCalled_thenReturnBaseUrlWithPort()
      throws MalformedURLException {
    String basePath = PolarisUtils.getBasePath("http://example.com:8080/path/to/resource");
    Assert.assertEquals("http://example.com:8080", basePath);
  }

  @Test(expected = MalformedURLException.class)
  public void givenInvalidUrl_whenGetBasePathCalled_thenThrowMalformedURLException()
      throws MalformedURLException {
    PolarisUtils.getBasePath("invalid-url");
  }

  @Test
  public void givenBaseUrlPathAndQueryParams_whenBuildUrlCalled_thenReturnUrlWithPathAndParams()
      throws URISyntaxException, MalformedURLException {
    Map<String, String> queryParams = new HashMap<>();
    queryParams.put("key1", "value1");
    queryParams.put("key2", "value2");

    String result =
        PolarisUtils.buildUrl("https://example.com/base", "path/to/resource", queryParams);

    Assert.assertTrue(result.startsWith("https://example.com/base/path/to/resource"));
    Assert.assertTrue(result.contains("key1=value1"));
    Assert.assertTrue(result.contains("key2=value2"));
  }

  @Test
  public void givenBaseUrlAndNullQueryParams_whenBuildUrlCalled_thenReturnUrlWithPathOnly()
      throws URISyntaxException, MalformedURLException {
    String result = PolarisUtils.buildUrl("https://example.com/base", "path/to/resource", null);

    Assert.assertEquals("https://example.com/base/path/to/resource", result);
  }

  @Test
  public void givenBaseUrlAndEmptyPath_whenBuildUrlCalled_thenReturnBaseUrlWithoutModification()
      throws URISyntaxException, MalformedURLException {
    String result = PolarisUtils.buildUrl("https://example.com/base", "", null);

    Assert.assertEquals("https://example.com/base", result);
  }

  @Test
  public void givenBaseUrlWithoutPathAndQueryParams_whenBuildUrlCalled_thenReturnBaseUrl()
      throws URISyntaxException, MalformedURLException {
    String result = PolarisUtils.buildUrl("https://example.com/base", null, null);

    Assert.assertEquals("https://example.com/base", result);
  }

  @Test
  public void
      givenBaseUrlAndPathWithDifferentSlashFormats_whenBuildUrlCalled_thenReturnCorrectFormattedUrl()
          throws URISyntaxException, MalformedURLException {
    // baseUrl without trailing slash, path with leading slash
    String result = PolarisUtils.buildUrl("https://example.com/base", "/path", null);
    Assert.assertEquals("https://example.com/base/path", result);

    // baseUrl with trailing slash, path without leading slash
    result = PolarisUtils.buildUrl("https://example.com/base/", "path", null);
    Assert.assertEquals("https://example.com/base/path", result);

    // baseUrl with trailing slash, path with leading slash
    result = PolarisUtils.buildUrl("https://example.com/base/", "/path", null);
    Assert.assertEquals("https://example.com/base/path", result);
  }

  @Test(expected = MalformedURLException.class)
  public void givenInvalidBaseUrl_whenBuildUrlCalled_thenThrowMalformedURLException()
      throws URISyntaxException, MalformedURLException {
    PolarisUtils.buildUrl("invalid-url", "path", null);
  }

  @Test
  public void givenBaseUrlWithoutExistingPath_whenBuildUrlCalled_thenAppendPathCorrectly()
      throws URISyntaxException, MalformedURLException {
    String result = PolarisUtils.buildUrl("https://example.com", "newpath", null);

    Assert.assertEquals("https://example.com/newpath", result);
  }

  @Test
  public void
      givenBaseUrlWithRootPathAndPathStartingWithSlash_whenBuildUrlCalled_thenAvoidDoubleSlash()
          throws URISyntaxException, MalformedURLException {
    String result = PolarisUtils.buildUrl("https://example.com/", "/newpath", null);

    Assert.assertEquals("https://example.com/newpath", result);
  }

  @Test
  public void givenBaseUrlWithEmptyPath_whenBuildUrlCalled_thenThrowMalformedURLException() {
    Assert.assertThrows(
        MalformedURLException.class, () -> PolarisUtils.buildUrl("", "newpath", null));
  }
}
