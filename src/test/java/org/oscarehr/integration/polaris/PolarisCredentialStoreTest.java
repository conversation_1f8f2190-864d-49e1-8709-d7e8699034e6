/**
 * Copyright (c) 2025 WELL EMR Group Inc.
 * This software is made available under the terms of the
 * GNU General Public License, Version 2, 1991 (GPLv2).
 * License details are available via "gnu.org/licenses/gpl-2.0.html".
 */
package org.oscarehr.integration.polaris;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import lombok.val;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.oscarehr.common.encryption.StringEncryptor;
import org.oscarehr.integration.polaris.model.PolarisConfiguration;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class PolarisCredentialStoreTest {

  private PolarisApiConnector polarisApiConnector;

  private final String AUTHORIZATION_ENDPOINT = "https://example.com/authorization";
  private final String TOKEN_ENDPOINT = "https://example.com/token";
  private final String JWKS_URI = "https://example.com/jwks";
  private final String USER_INFO_ENDPOINT = "https://example.com/userinfo";

  @Before
  public void setUp() {
    polarisApiConnector = mock(PolarisApiConnector.class);
    ReflectionTestUtils.setField(PolarisCredentialStore.class,
        "polarisApiConnector",
        polarisApiConnector);
  }

  @Test
  public void givenTokensSet_whenReset_thenTokensAreCleared() {
    // given
    PolarisCredentialStore.setAccessToken("some-token");
    PolarisCredentialStore.setIdToken("some-id-token");
    PolarisCredentialStore.setExpiresIn(3600);

    // when
    PolarisCredentialStore.reset();

    // then
    assertNull(PolarisCredentialStore.getAccessToken());
    assertNull(PolarisCredentialStore.getIdToken());
    assertEquals(0, PolarisCredentialStore.getExpiresIn());
  }

  @Test
  public void givenValidCredentials_whenSetupClientCredentials_thenNoExceptionThrown() {
    val polarisConfiguration = new PolarisConfiguration();

    // given
    polarisConfiguration.setAdminPolarisFhirClientId("test-client-id");
    polarisConfiguration.setAdminPolarisFhirClientSecret(StringEncryptor.encrypt("test-client-secret"));

    assertDoesNotThrow(
        () -> {
          // when
          PolarisCredentialStore.setupClientCredentials(polarisConfiguration);

          // then no exception
        }
    );
  }

  @Test(expected = PolarisCredentialStoreException.class)
  public void givenNullClientId_whenSetupClientCredentials_thenExceptionIsThrown() throws PolarisCredentialStoreException {
    val polarisConfiguration = new PolarisConfiguration();

    // given
    polarisConfiguration.setAdminPolarisFhirClientId(null);
    polarisConfiguration.setAdminPolarisFhirClientSecret(StringEncryptor.encrypt("test-client-secret"));

    // when
    PolarisCredentialStore.setupClientCredentials(polarisConfiguration);
    // then throw exception
  }

  @Test(expected = PolarisCredentialStoreException.class)
  public void givenNullClientSecret_whenSetupClientCredentials_thenExceptionIsThrown() throws PolarisCredentialStoreException {
    val polarisConfiguration = new PolarisConfiguration();

    // given
    polarisConfiguration.setAdminPolarisFhirClientId(null);
    polarisConfiguration.setAdminPolarisFhirClientSecret(null);

    // when
    PolarisCredentialStore.setupClientCredentials(polarisConfiguration);
    // then throw exception
  }

  @Test
  public void givenNullUrls_whenRetrieveConfigurationUrls_thenPolarisExceptionIsThrown() {
    createMockPolarisEndpointConfiguration(null, null, null, null);
    assertPolarisConfigurationThrowsException();
  }

  @Test
  public void givenBlankUrls_whenRetrieveConfigurationUrls_thenPolarisExceptionIsThrown() {
    createMockPolarisEndpointConfiguration("", "", "", "");
    assertPolarisConfigurationThrowsException();
  }

  @Test
  public void givenSomeBlankUrls_whenRetrieveConfigurationUrls_thenPolarisExceptionIsThrown() {
    createMockPolarisEndpointConfiguration(AUTHORIZATION_ENDPOINT, "", JWKS_URI,
        USER_INFO_ENDPOINT);
    assertPolarisConfigurationThrowsException();
  }

  @Test
  public void givenValidConfiguration_whenRetrieveConfigurationUrls_thenEndpointsAreExtracted()
      throws PolarisCredentialStoreException {
    val configObject = createMockPolarisEndpointConfiguration(AUTHORIZATION_ENDPOINT,
        TOKEN_ENDPOINT, JWKS_URI, USER_INFO_ENDPOINT);
    PolarisCredentialStore.retrieveConfigurationUrls();
    assertEquals(PolarisCredentialStore.getAuthorizationUrl(),
        configObject.getAuthorizationEndpoint());
    assertEquals(PolarisCredentialStore.getTokenUrl(), configObject.getTokenEndpoint());
    assertEquals(PolarisCredentialStore.getJwksUri(), configObject.getJwksUri());
    assertEquals(PolarisCredentialStore.getUserInfoEndpoint(), configObject.getUserInfoEndpoint());
  }

  private PolarisEndpointConfiguration createMockPolarisEndpointConfiguration(
      final String authorizationEndpoint,
      final String tokenEndpoint,
      final String jwksUri,
      final String userInfoEndpoint
  ) {
    val configObject = new PolarisEndpointConfiguration();
    configObject.setAuthorizationEndpoint(authorizationEndpoint);
    configObject.setTokenEndpoint(tokenEndpoint);
    configObject.setJwksUri(jwksUri);
    configObject.setUserInfoEndpoint(userInfoEndpoint);
    when(polarisApiConnector.getConfigurationEndpoints()).thenReturn(configObject);
    return configObject;
  }

  private void assertPolarisConfigurationThrowsException() {
    val exception = assertThrows(PolarisCredentialStoreException.class,
        PolarisCredentialStore::retrieveConfigurationUrls);
    assertEquals("Failed to retrieve Polaris configuration", exception.getMessage());
  }
}
