<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head profile="http://selenium-ide.openqa.org/profiles/test-case">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<link rel="selenium.base" href="http://localhost:8095/" />
<title>MyOSCAR admin - eula</title>
</head>
<body>
<table cellpadding="1" cellspacing="1" border="1">
<thead>
<tr><td rowspan="1" colspan="3">MyOSCAR admin - eula</td></tr>
</thead><tbody>
<tr>
	<td>open</td>
	<td>http://localhost:8095/myoscar_client/login.jsf</td>
	<td></td>
</tr>
<tr>
	<td>type</td>
	<td>name=userName</td>
	<td>admin</td>
</tr>
<tr>
	<td>type</td>
	<td>name=password</td>
	<td>admin</td>
</tr>
<tr>
	<td>clickAndWait</td>
	<td>css=input[type=&quot;submit&quot;]</td>
	<td></td>
</tr>
<tr>
	<td>verifyTextPresent</td>
	<td></td>
	<td>Terms and Conditions</td>
</tr>
<tr>
	<td>clickAndWait</td>
	<td>id=j_id_9:j_id_b</td>
	<td></td>
</tr>
<tr>
	<td>verifyTextPresent</td>
	<td></td>
	<td>Welcome to MyOSCAR</td>
</tr>
</tbody></table>
</body>
</html>
